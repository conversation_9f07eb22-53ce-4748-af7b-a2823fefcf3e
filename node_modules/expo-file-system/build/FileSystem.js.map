{"version": 3, "file": "FileSystem.js", "sourceRoot": "", "sources": ["../src/FileSystem.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAgB,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AACpF,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AACxC,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AAEpC,OAAO,kBAAkB,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAUL,qBAAqB,EAGrB,oBAAoB,GAQrB,MAAM,oBAAoB,CAAC;AAE5B,IAAI,CAAC,kBAAkB,EAAE;IACvB,OAAO,CAAC,IAAI,CACV,2GAA2G,CAC5G,CAAC;CACH;AACD,qCAAqC;AACrC,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,sBAAsB;AAE5E,SAAS,oBAAoB,CAAC,CAAgB;IAC5C,IAAI,CAAC,IAAI,IAAI,EAAE;QACb,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;KACpC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAE5F;;;;GAIG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;AAEtF,eAAe;AACf,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,kBAAkB,CAAC;AAErE;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,YAAY,CAAC,OAAe,EAAE,UAAuB,EAAE;IAC3E,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;QACpC,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;KACnE;IACD,OAAO,MAAM,kBAAkB,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACjE,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,UAA0B,EAAE;IAE5B,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE;QACzC,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;KACxE;IACD,OAAO,MAAM,kBAAkB,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CAAC,OAAe;IACtD,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;QAC7B,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,EAAE;YAC1C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;SACzE;QACD,OAAO,MAAM,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;KAC7D;SAAM;QACL,OAAO,OAAO,CAAC;KAChB;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,OAAe,EACf,QAAgB,EAChB,UAA0B,EAAE;IAE5B,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,EAAE;QAC1C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;KACzE;IACD,OAAO,MAAM,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjF,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,OAAe,EAAE,UAA2B,EAAE;IAC9E,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;QACnC,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;KAClE;IACD,OAAO,MAAM,kBAAkB,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,oCAAoC;IACxD,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,iBAAiB,IAAI,IAAI,EAAE;QAC1D,OAAO;KACR;IACD,MAAM,uBAAuB,GAAG,GAAG,iBAAiB,iBAAiB,CAAC;IACtE,OAAO,MAAM,WAAW,CAAC,uBAAuB,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,SAAS,CAAC,OAA0B;IACxD,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE;QACjC,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;KAChE;IACD,OAAO,MAAM,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACrD,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,SAAS,CAAC,OAA0B;IACxD,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE;QACjC,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;KAChE;IACD,OAAO,MAAM,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACrD,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,OAAe,EACf,UAAgC,EAAE;IAElC,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,EAAE;QAC1C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;KACzE;IACD,OAAO,MAAM,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACvE,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CAAC,OAAe;IACtD,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,EAAE;QAC1C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;KACzE;IACD,OAAO,MAAM,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAClE,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB;IAC3C,IAAI,CAAC,kBAAkB,CAAC,uBAAuB,EAAE;QAC/C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAC;KAC9E;IACD,OAAO,MAAM,kBAAkB,CAAC,uBAAuB,EAAE,CAAC;AAC5D,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB;IAC7C,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE;QACjD,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,2BAA2B,CAAC,CAAC;KAChF;IACD,OAAO,MAAM,kBAAkB,CAAC,yBAAyB,EAAE,CAAC;AAC9D,CAAC;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,GAAW,EACX,OAAe,EACf,UAA2B,EAAE;IAE7B,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE;QACrC,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;KACpE;IAED,OAAO,MAAM,kBAAkB,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE;QAC1D,WAAW,EAAE,qBAAqB,CAAC,UAAU;QAC7C,GAAG,OAAO;KACX,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAC/B,GAAW,EACX,OAAe,EACf,UAAmC,EAAE;IAErC,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE;QACnC,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;KAClE;IAED,OAAO,MAAM,kBAAkB,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE;QACxD,WAAW,EAAE,qBAAqB,CAAC,UAAU;QAC7C,UAAU,EAAE,oBAAoB,CAAC,cAAc;QAC/C,GAAG,OAAO;QACV,UAAU,EAAE,CAAC,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE;KACzD,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,uBAAuB,CACrC,GAAW,EACX,OAAe,EACf,OAAyB,EACzB,QAAsE,EACtE,UAAmB;IAEnB,OAAO,IAAI,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC5E,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,GAAW,EACX,OAAe,EACf,OAAiC,EACjC,QAAoE;IAEpE,OAAO,IAAI,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,oBAAoB,CAC3B,IAA+C;IAE/C,OAAO,gBAAgB,IAAI,IAAI,CAAC;AAClC,CAAC;AAED,MAAM,OAAgB,gCAAgC;IAG5C,KAAK,GAAG,MAAM,EAAE,CAAC;IACf,eAAe,GAAG,KAAK,CAAC;IAC1B,OAAO,GAAG,IAAI,YAAY,CAAC,kBAAkB,CAAC,CAAC;IAC/C,YAAY,CAAuB;IAE3C,eAAe;IACR,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE;YAC9C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,wBAAwB,CAAC,CAAC;SAC7E;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,OAAO,MAAM,kBAAkB,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpE,CAAC;IAES,eAAe;QACvB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAc,IAAI;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAMS,eAAe;QACvB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO;SACR;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,KAAuB,EAAE,EAAE;YAC5F,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;gBAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBACpC,IAAI,QAAQ,EAAE;oBACZ,IAAI,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBACpC,MAAM,IAAI,GAAG;4BACX,GAAG,KAAK,CAAC,IAAI;4BACb,IAAI,aAAa;gCACf,OAAO,CAAC,IAAI,CACV,iIAAiI,CAClI,CAAC;gCACF,OAAO,IAAI,CAAC,cAAc,CAAC;4BAC7B,CAAC;yBACF,CAAC;wBACF,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;qBACvB;oBAED,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBACtB;aACF;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAES,kBAAkB;QAC1B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,OAAO;SACR;QACD,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;CACF;AAED,MAAM,OAAO,UAAW,SAAQ,gCAAoD;IAIxE;IACA;IAEA;IANF,OAAO,CAA0B;IAEzC,YACU,GAAW,EACX,OAAe,EACvB,OAAiC,EACzB,QAAoE;QAE5E,KAAK,EAAE,CAAC;QALA,QAAG,GAAH,GAAG,CAAQ;QACX,YAAO,GAAP,OAAO,CAAQ;QAEf,aAAQ,GAAR,QAAQ,CAA4D;QAI5E,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE;YACpD,MAAM,CAAuC,CAAC;QAEhD,IAAI,CAAC,OAAO,GAAG;YACb,WAAW,EAAE,qBAAqB,CAAC,UAAU;YAC7C,UAAU,EAAE,oBAAoB,CAAC,cAAc;YAC/C,GAAG,OAAO;YACV,UAAU;SACX,CAAC;IACJ,CAAC;IAES,YAAY;QACpB,OAAO,iCAAiC,CAAC;IAC3C,CAAC;IACS,WAAW;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,eAAe;IACR,KAAK,CAAC,WAAW;QACtB,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,EAAE;YAC5C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAC;SAC3E;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YAC1B,OAAO;SACR;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,oBAAoB,CAC1D,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAED,MAAM,OAAO,iBAAkB,SAAQ,gCAAsD;IAEjF;IACA;IACA;IACA;IACA;IALV,YACU,GAAW,EACX,QAAgB,EAChB,UAA2B,EAAE,EAC7B,QAAsE,EACtE,UAAmB;QAE3B,KAAK,EAAE,CAAC;QANA,QAAG,GAAH,GAAG,CAAQ;QACX,aAAQ,GAAR,QAAQ,CAAQ;QAChB,YAAO,GAAP,OAAO,CAAsB;QAC7B,aAAQ,GAAR,QAAQ,CAA8D;QACtE,eAAU,GAAV,UAAU,CAAS;IAG7B,CAAC;IAED,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAES,YAAY;QACpB,OAAO,mCAAmC,CAAC;IAC7C,CAAC;IAES,WAAW;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,EAAE;YACnD,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC,CAAC;SAClF;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YAC1B,OAAO;SACR;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,MAAM,kBAAkB,CAAC,2BAA2B,CACzD,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,UAAU,CAChB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,EAAE;YACnD,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC,CAAC;SAClF;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YAC1B,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,QAAQ;gBACtB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,GAAG,EAAE,IAAI,CAAC,GAAG;aACd,CAAC;SACH;QAED,MAAM,WAAW,GAAG,MAAM,kBAAkB,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpF,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;YACzC,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;SACvB;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,kBAAkB,CAAC,2BAA2B,EAAE;YACnD,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,6BAA6B,CAAC,CAAC;SAClF;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YAC1B,OAAO;SACR;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,MAAM,kBAAkB,CAAC,2BAA2B,CACzD,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,UAAU,CAChB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,OAAO;QACL,OAAO;YACL,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;CACF;AAED,MAAM,qBAAqB,GAAG,iBAAiB,CAAC;AAChD,MAAM,sBAAsB,GAAG,kBAAkB,CAAC;AAClD,MAAM,eAAe,GAAG,WAAW,CAAC;AACpC,MAAM,aAAa,GAAG,SAAS,CAAC;AAChC,MAAM,aAAa,GAAG,SAAS,CAAC;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgFG;AACH,MAAM,KAAW,sBAAsB,CAoGtC;AApGD,WAAiB,sBAAsB;IACrC;;;;;OAKG;IACH,SAAgB,wBAAwB,CAAC,UAAkB;QACzD,OAAO,gEAAgE,UAAU,qBAAqB,UAAU,EAAE,CAAC;IACrH,CAAC;IAFe,+CAAwB,2BAEvC,CAAA;IAED;;;;;;OAMG;IACI,KAAK,UAAU,gCAAgC,CACpD,iBAAgC,IAAI;QAEpC,IAAI,CAAC,kBAAkB,CAAC,gCAAgC,EAAE;YACxD,MAAM,IAAI,mBAAmB,CAC3B,kBAAkB,EAClB,yDAAyD,CAC1D,CAAC;SACH;QAED,OAAO,MAAM,kBAAkB,CAAC,gCAAgC,CAAC,cAAc,CAAC,CAAC;IACnF,CAAC;IAXqB,uDAAgC,mCAWrD,CAAA;IAED;;;;OAIG;IACI,KAAK,UAAU,kBAAkB,CAAC,MAAc;QACrD,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,EAAE;YAC7C,MAAM,IAAI,mBAAmB,CAC3B,kBAAkB,EAClB,2CAA2C,CAC5C,CAAC;SACH;QACD,OAAO,MAAM,kBAAkB,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACpE,CAAC;IARqB,yCAAkB,qBAQvC,CAAA;IAED;;;;;OAKG;IACI,KAAK,UAAU,kBAAkB,CAAC,SAAiB,EAAE,OAAe;QACzE,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,EAAE;YAC7C,MAAM,IAAI,mBAAmB,CAC3B,kBAAkB,EAClB,2CAA2C,CAC5C,CAAC;SACH;QACD,OAAO,MAAM,kBAAkB,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC5E,CAAC;IARqB,yCAAkB,qBAQvC,CAAA;IAED;;;;;;OAMG;IACI,KAAK,UAAU,eAAe,CACnC,SAAiB,EACjB,QAAgB,EAChB,QAAgB;QAEhB,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,EAAE;YAC1C,MAAM,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,wCAAwC,CAAC,CAAC;SAC7F;QACD,OAAO,MAAM,kBAAkB,CAAC,kBAAkB,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACpF,CAAC;IATqB,sCAAe,kBASpC,CAAA;IAED;;OAEG;IACU,yCAAkB,GAAG,sBAAsB,CAAC;IACzD;;OAEG;IACU,wCAAiB,GAAG,qBAAqB,CAAC;IACvD;;OAEG;IACU,kCAAW,GAAG,eAAe,CAAC;IAC3C;;OAEG;IACU,gCAAS,GAAG,aAAa,CAAC;IACvC;;OAEG;IACU,gCAAS,GAAG,aAAa,CAAC;AACzC,CAAC,EApGgB,sBAAsB,KAAtB,sBAAsB,QAoGtC", "sourcesContent": ["import { EventEmitter, Subscription, UnavailabilityError } from 'expo-modules-core';\nimport { Platform } from 'react-native';\nimport { v4 as uuidv4 } from 'uuid';\n\nimport ExponentFileSystem from './ExponentFileSystem';\nimport {\n  DownloadOptions,\n  DownloadPauseState,\n  FileSystemNetworkTaskProgressCallback,\n  DownloadProgressData,\n  UploadProgressData,\n  FileInfo,\n  FileSystemAcceptedUploadHttpMethod,\n  FileSystemDownloadResult,\n  FileSystemRequestDirectoryPermissionsResult,\n  FileSystemSessionType,\n  FileSystemUploadOptions,\n  FileSystemUploadResult,\n  FileSystemUploadType,\n  ProgressEvent,\n  ReadingOptions,\n  WritingOptions,\n  DeletingOptions,\n  InfoOptions,\n  RelocatingOptions,\n  MakeDirectoryOptions,\n} from './FileSystem.types';\n\nif (!ExponentFileSystem) {\n  console.warn(\n    \"No native ExponentFileSystem module found, are you sure the expo-file-system's module is linked properly?\"\n  );\n}\n// Prevent webpack from pruning this.\nconst _unused = new EventEmitter(ExponentFileSystem); // eslint-disable-line\n\nfunction normalizeEndingSlash(p: string | null): string | null {\n  if (p != null) {\n    return p.replace(/\\/*$/, '') + '/';\n  }\n  return null;\n}\n\n/**\n * `file://` URI pointing to the directory where user documents for this app will be stored.\n * Files stored here will remain until explicitly deleted by the app. Ends with a trailing `/`.\n * Example uses are for files the user saves that they expect to see again.\n */\nexport const documentDirectory = normalizeEndingSlash(ExponentFileSystem.documentDirectory);\n\n/**\n * `file://` URI pointing to the directory where temporary files used by this app will be stored.\n * Files stored here may be automatically deleted by the system when low on storage.\n * Example uses are for downloaded or generated files that the app just needs for one-time usage.\n */\nexport const cacheDirectory = normalizeEndingSlash(ExponentFileSystem.cacheDirectory);\n\n// @docsMissing\nexport const { bundledAssets, bundleDirectory } = ExponentFileSystem;\n\n/**\n * Get metadata information about a file, directory or external content/asset.\n * @param fileUri URI to the file or directory. See [supported URI schemes](#supported-uri-schemes).\n * @param options A map of options represented by [`GetInfoAsyncOptions`](#getinfoasyncoptions) type.\n * @return A Promise that resolves to a `FileInfo` object. If no item exists at this URI,\n * the returned Promise resolves to `FileInfo` object in form of `{ exists: false, isDirectory: false }`.\n */\nexport async function getInfoAsync(fileUri: string, options: InfoOptions = {}): Promise<FileInfo> {\n  if (!ExponentFileSystem.getInfoAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getInfoAsync');\n  }\n  return await ExponentFileSystem.getInfoAsync(fileUri, options);\n}\n\n/**\n * Read the entire contents of a file as a string. Binary will be returned in raw format, you will need to append `data:image/png;base64,` to use it as Base64.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * @param options A map of read options represented by [`ReadingOptions`](#readingoptions) type.\n * @return A Promise that resolves to a string containing the entire contents of the file.\n */\nexport async function readAsStringAsync(\n  fileUri: string,\n  options: ReadingOptions = {}\n): Promise<string> {\n  if (!ExponentFileSystem.readAsStringAsync) {\n    throw new UnavailabilityError('expo-file-system', 'readAsStringAsync');\n  }\n  return await ExponentFileSystem.readAsStringAsync(fileUri, options);\n}\n\n/**\n * Takes a `file://` URI and converts it into content URI (`content://`) so that it can be accessed by other applications outside of Expo.\n * @param fileUri The local URI of the file. If there is no file at this URI, an exception will be thrown.\n * @example\n * ```js\n * FileSystem.getContentUriAsync(uri).then(cUri => {\n *   console.log(cUri);\n *   IntentLauncher.startActivityAsync('android.intent.action.VIEW', {\n *     data: cUri,\n *     flags: 1,\n *   });\n * });\n * ```\n * @return Returns a Promise that resolves to a `string` containing a `content://` URI pointing to the file.\n * The URI is the same as the `fileUri` input parameter but in a different format.\n * @platform android\n */\nexport async function getContentUriAsync(fileUri: string): Promise<string> {\n  if (Platform.OS === 'android') {\n    if (!ExponentFileSystem.getContentUriAsync) {\n      throw new UnavailabilityError('expo-file-system', 'getContentUriAsync');\n    }\n    return await ExponentFileSystem.getContentUriAsync(fileUri);\n  } else {\n    return fileUri;\n  }\n}\n\n/**\n * Write the entire contents of a file as a string.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * > Note: when you're using SAF URI the file needs to exist. You can't create a new file.\n * @param contents The string to replace the contents of the file with.\n * @param options A map of write options represented by [`WritingOptions`](#writingoptions) type.\n */\nexport async function writeAsStringAsync(\n  fileUri: string,\n  contents: string,\n  options: WritingOptions = {}\n): Promise<void> {\n  if (!ExponentFileSystem.writeAsStringAsync) {\n    throw new UnavailabilityError('expo-file-system', 'writeAsStringAsync');\n  }\n  return await ExponentFileSystem.writeAsStringAsync(fileUri, contents, options);\n}\n\n/**\n * Delete a file or directory. If the URI points to a directory, the directory and all its contents are recursively deleted.\n * @param fileUri `file://` or [SAF](#saf-uri) URI to the file or directory.\n * @param options A map of write options represented by [`DeletingOptions`](#deletingoptions) type.\n */\nexport async function deleteAsync(fileUri: string, options: DeletingOptions = {}): Promise<void> {\n  if (!ExponentFileSystem.deleteAsync) {\n    throw new UnavailabilityError('expo-file-system', 'deleteAsync');\n  }\n  return await ExponentFileSystem.deleteAsync(fileUri, options);\n}\n\nexport async function deleteLegacyDocumentDirectoryAndroid(): Promise<void> {\n  if (Platform.OS !== 'android' || documentDirectory == null) {\n    return;\n  }\n  const legacyDocumentDirectory = `${documentDirectory}ExperienceData/`;\n  return await deleteAsync(legacyDocumentDirectory, { idempotent: true });\n}\n\n/**\n * Move a file or directory to a new location.\n * @param options A map of move options represented by [`RelocatingOptions`](#relocatingoptions) type.\n */\nexport async function moveAsync(options: RelocatingOptions): Promise<void> {\n  if (!ExponentFileSystem.moveAsync) {\n    throw new UnavailabilityError('expo-file-system', 'moveAsync');\n  }\n  return await ExponentFileSystem.moveAsync(options);\n}\n\n/**\n * Create a copy of a file or directory. Directories are recursively copied with all of their contents.\n * It can be also used to copy content shared by other apps to local filesystem.\n * @param options A map of move options represented by [`RelocatingOptions`](#relocatingoptions) type.\n */\nexport async function copyAsync(options: RelocatingOptions): Promise<void> {\n  if (!ExponentFileSystem.copyAsync) {\n    throw new UnavailabilityError('expo-file-system', 'copyAsync');\n  }\n  return await ExponentFileSystem.copyAsync(options);\n}\n\n/**\n * Create a new empty directory.\n * @param fileUri `file://` URI to the new directory to create.\n * @param options A map of create directory options represented by [`MakeDirectoryOptions`](#makedirectoryoptions) type.\n */\nexport async function makeDirectoryAsync(\n  fileUri: string,\n  options: MakeDirectoryOptions = {}\n): Promise<void> {\n  if (!ExponentFileSystem.makeDirectoryAsync) {\n    throw new UnavailabilityError('expo-file-system', 'makeDirectoryAsync');\n  }\n  return await ExponentFileSystem.makeDirectoryAsync(fileUri, options);\n}\n\n/**\n * Enumerate the contents of a directory.\n * @param fileUri `file://` URI to the directory.\n * @return A Promise that resolves to an array of strings, each containing the name of a file or directory contained in the directory at `fileUri`.\n */\nexport async function readDirectoryAsync(fileUri: string): Promise<string[]> {\n  if (!ExponentFileSystem.readDirectoryAsync) {\n    throw new UnavailabilityError('expo-file-system', 'readDirectoryAsync');\n  }\n  return await ExponentFileSystem.readDirectoryAsync(fileUri, {});\n}\n\n/**\n * Gets the available internal disk storage size, in bytes. This returns the free space on the data partition that hosts all of the internal storage for all apps on the device.\n * @return Returns a Promise that resolves to the number of bytes available on the internal disk, or JavaScript's [`MAX_SAFE_INTEGER`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER)\n * if the capacity is greater than 2<sup>53</sup> - 1 bytes.\n */\nexport async function getFreeDiskStorageAsync(): Promise<number> {\n  if (!ExponentFileSystem.getFreeDiskStorageAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getFreeDiskStorageAsync');\n  }\n  return await ExponentFileSystem.getFreeDiskStorageAsync();\n}\n\n/**\n * Gets total internal disk storage size, in bytes. This is the total capacity of the data partition that hosts all the internal storage for all apps on the device.\n * @return Returns a Promise that resolves to a number that specifies the total internal disk storage capacity in bytes, or JavaScript's [`MAX_SAFE_INTEGER`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER)\n * if the capacity is greater than 2<sup>53</sup> - 1 bytes.\n */\nexport async function getTotalDiskCapacityAsync(): Promise<number> {\n  if (!ExponentFileSystem.getTotalDiskCapacityAsync) {\n    throw new UnavailabilityError('expo-file-system', 'getTotalDiskCapacityAsync');\n  }\n  return await ExponentFileSystem.getTotalDiskCapacityAsync();\n}\n\n/**\n * Download the contents at a remote URI to a file in the app's file system. The directory for a local file uri must exist prior to calling this function.\n * @param uri The remote URI to download from.\n * @param fileUri The local URI of the file to download to. If there is no file at this URI, a new one is created.\n * If there is a file at this URI, its contents are replaced. The directory for the file must exist.\n * @param options A map of download options represented by [`DownloadOptions`](#downloadoptions) type.\n * @example\n * ```js\n * FileSystem.downloadAsync(\n *   'http://techslides.com/demos/sample-videos/small.mp4',\n *   FileSystem.documentDirectory + 'small.mp4'\n * )\n *   .then(({ uri }) => {\n *     console.log('Finished downloading to ', uri);\n *   })\n *   .catch(error => {\n *     console.error(error);\n *   });\n * ```\n * @return Returns a Promise that resolves to a `FileSystemDownloadResult` object.\n */\nexport async function downloadAsync(\n  uri: string,\n  fileUri: string,\n  options: DownloadOptions = {}\n): Promise<FileSystemDownloadResult> {\n  if (!ExponentFileSystem.downloadAsync) {\n    throw new UnavailabilityError('expo-file-system', 'downloadAsync');\n  }\n\n  return await ExponentFileSystem.downloadAsync(uri, fileUri, {\n    sessionType: FileSystemSessionType.BACKGROUND,\n    ...options,\n  });\n}\n\n/**\n * Upload the contents of the file pointed by `fileUri` to the remote url.\n * @param url The remote URL, where the file will be sent.\n * @param fileUri The local URI of the file to send. The file must exist.\n * @param options A map of download options represented by [`FileSystemUploadOptions`](#filesystemuploadoptions) type.\n * @example\n * **Client**\n *\n * ```js\n * import * as FileSystem from 'expo-file-system';\n *\n * try {\n *   const response = await FileSystem.uploadAsync(`http://***********:1234/binary-upload`, fileUri, {\n *     fieldName: 'file',\n *     httpMethod: 'PATCH',\n *     uploadType: FileSystem.FileSystemUploadType.BINARY_CONTENT,\n *   });\n *   console.log(JSON.stringify(response, null, 4));\n * } catch (error) {\n *   console.log(error);\n * }\n * ```\n *\n * **Server**\n *\n * Please refer to the \"[Server: Handling multipart requests](#server-handling-multipart-requests)\" example - there is code for a simple Node.js server.\n * @return Returns a Promise that resolves to `FileSystemUploadResult` object.\n */\nexport async function uploadAsync(\n  url: string,\n  fileUri: string,\n  options: FileSystemUploadOptions = {}\n): Promise<FileSystemUploadResult> {\n  if (!ExponentFileSystem.uploadAsync) {\n    throw new UnavailabilityError('expo-file-system', 'uploadAsync');\n  }\n\n  return await ExponentFileSystem.uploadAsync(url, fileUri, {\n    sessionType: FileSystemSessionType.BACKGROUND,\n    uploadType: FileSystemUploadType.BINARY_CONTENT,\n    ...options,\n    httpMethod: (options.httpMethod || 'POST').toUpperCase(),\n  });\n}\n\n/**\n * Create a `DownloadResumable` object which can start, pause, and resume a download of contents at a remote URI to a file in the app's file system.\n * > Note: You need to call `downloadAsync()`, on a `DownloadResumable` instance to initiate the download.\n * The `DownloadResumable` object has a callback that provides download progress updates.\n * Downloads can be resumed across app restarts by using `AsyncStorage` to store the `DownloadResumable.savable()` object for later retrieval.\n * The `savable` object contains the arguments required to initialize a new `DownloadResumable` object to resume the download after an app restart.\n * The directory for a local file uri must exist prior to calling this function.\n * @param uri The remote URI to download from.\n * @param fileUri The local URI of the file to download to. If there is no file at this URI, a new one is created.\n * If there is a file at this URI, its contents are replaced. The directory for the file must exist.\n * @param options A map of download options represented by [`DownloadOptions`](#downloadoptions) type.\n * @param callback This function is called on each data write to update the download progress.\n * > **Note**: When the app has been moved to the background, this callback won't be fired until it's moved to the foreground.\n * @param resumeData The string which allows the api to resume a paused download. This is set on the `DownloadResumable` object automatically when a download is paused.\n * When initializing a new `DownloadResumable` this should be `null`.\n */\nexport function createDownloadResumable(\n  uri: string,\n  fileUri: string,\n  options?: DownloadOptions,\n  callback?: FileSystemNetworkTaskProgressCallback<DownloadProgressData>,\n  resumeData?: string\n): DownloadResumable {\n  return new DownloadResumable(uri, fileUri, options, callback, resumeData);\n}\n\nexport function createUploadTask(\n  url: string,\n  fileUri: string,\n  options?: FileSystemUploadOptions,\n  callback?: FileSystemNetworkTaskProgressCallback<UploadProgressData>\n): UploadTask {\n  return new UploadTask(url, fileUri, options, callback);\n}\n\nfunction isUploadProgressData(\n  data: DownloadProgressData | UploadProgressData\n): data is UploadProgressData {\n  return 'totalBytesSent' in data;\n}\n\nexport abstract class FileSystemCancellableNetworkTask<\n  T extends DownloadProgressData | UploadProgressData\n> {\n  private _uuid = uuidv4();\n  protected taskWasCanceled = false;\n  private emitter = new EventEmitter(ExponentFileSystem);\n  private subscription?: Subscription | null;\n\n  // @docsMissing\n  public async cancelAsync(): Promise<void> {\n    if (!ExponentFileSystem.networkTaskCancelAsync) {\n      throw new UnavailabilityError('expo-file-system', 'networkTaskCancelAsync');\n    }\n\n    this.removeSubscription();\n    this.taskWasCanceled = true;\n    return await ExponentFileSystem.networkTaskCancelAsync(this.uuid);\n  }\n\n  protected isTaskCancelled(): boolean {\n    if (this.taskWasCanceled) {\n      console.warn('This task was already canceled.');\n      return true;\n    }\n\n    return false;\n  }\n\n  protected get uuid(): string {\n    return this._uuid;\n  }\n\n  protected abstract getEventName(): string;\n\n  protected abstract getCallback(): FileSystemNetworkTaskProgressCallback<T> | undefined;\n\n  protected addSubscription() {\n    if (this.subscription) {\n      return;\n    }\n\n    this.subscription = this.emitter.addListener(this.getEventName(), (event: ProgressEvent<T>) => {\n      if (event.uuid === this.uuid) {\n        const callback = this.getCallback();\n        if (callback) {\n          if (isUploadProgressData(event.data)) {\n            const data = {\n              ...event.data,\n              get totalByteSent() {\n                console.warn(\n                  'Key \"totalByteSent\" in File System UploadProgressData is deprecated and will be removed in SDK 49, use \"totalBytesSent\" instead'\n                );\n                return this.totalBytesSent;\n              },\n            };\n            return callback(data);\n          }\n\n          callback(event.data);\n        }\n      }\n    });\n  }\n\n  protected removeSubscription() {\n    if (!this.subscription) {\n      return;\n    }\n    this.emitter.removeSubscription(this.subscription);\n    this.subscription = null;\n  }\n}\n\nexport class UploadTask extends FileSystemCancellableNetworkTask<UploadProgressData> {\n  private options: FileSystemUploadOptions;\n\n  constructor(\n    private url: string,\n    private fileUri: string,\n    options?: FileSystemUploadOptions,\n    private callback?: FileSystemNetworkTaskProgressCallback<UploadProgressData>\n  ) {\n    super();\n\n    const httpMethod = (options?.httpMethod?.toUpperCase() ||\n      'POST') as FileSystemAcceptedUploadHttpMethod;\n\n    this.options = {\n      sessionType: FileSystemSessionType.BACKGROUND,\n      uploadType: FileSystemUploadType.BINARY_CONTENT,\n      ...options,\n      httpMethod,\n    };\n  }\n\n  protected getEventName(): string {\n    return 'expo-file-system.uploadProgress';\n  }\n  protected getCallback(): FileSystemNetworkTaskProgressCallback<UploadProgressData> | undefined {\n    return this.callback;\n  }\n\n  // @docsMissing\n  public async uploadAsync(): Promise<FileSystemUploadResult | undefined> {\n    if (!ExponentFileSystem.uploadTaskStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'uploadTaskStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    const result = await ExponentFileSystem.uploadTaskStartAsync(\n      this.url,\n      this.fileUri,\n      this.uuid,\n      this.options\n    );\n    this.removeSubscription();\n\n    return result;\n  }\n}\n\nexport class DownloadResumable extends FileSystemCancellableNetworkTask<DownloadProgressData> {\n  constructor(\n    private url: string,\n    private _fileUri: string,\n    private options: DownloadOptions = {},\n    private callback?: FileSystemNetworkTaskProgressCallback<DownloadProgressData>,\n    private resumeData?: string\n  ) {\n    super();\n  }\n\n  public get fileUri(): string {\n    return this._fileUri;\n  }\n\n  protected getEventName(): string {\n    return 'expo-file-system.downloadProgress';\n  }\n\n  protected getCallback(): FileSystemNetworkTaskProgressCallback<DownloadProgressData> | undefined {\n    return this.callback;\n  }\n\n  /**\n   * Download the contents at a remote URI to a file in the app's file system.\n   * @return Returns a Promise that resolves to `FileSystemDownloadResult` object, or to `undefined` when task was cancelled.\n   */\n  async downloadAsync(): Promise<FileSystemDownloadResult | undefined> {\n    if (!ExponentFileSystem.downloadResumableStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    return await ExponentFileSystem.downloadResumableStartAsync(\n      this.url,\n      this._fileUri,\n      this.uuid,\n      this.options,\n      this.resumeData\n    );\n  }\n\n  /**\n   * Pause the current download operation. `resumeData` is added to the `DownloadResumable` object after a successful pause operation.\n   * Returns an object that can be saved with `AsyncStorage` for future retrieval (the same object that is returned from calling `FileSystem.DownloadResumable.savable()`).\n   * @return Returns a Promise that resolves to `DownloadPauseState` object.\n   */\n  async pauseAsync(): Promise<DownloadPauseState> {\n    if (!ExponentFileSystem.downloadResumablePauseAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumablePauseAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return {\n        fileUri: this._fileUri,\n        options: this.options,\n        url: this.url,\n      };\n    }\n\n    const pauseResult = await ExponentFileSystem.downloadResumablePauseAsync(this.uuid);\n    this.removeSubscription();\n    if (pauseResult) {\n      this.resumeData = pauseResult.resumeData;\n      return this.savable();\n    } else {\n      throw new Error('Unable to generate a savable pause state');\n    }\n  }\n\n  /**\n   * Resume a paused download operation.\n   * @return Returns a Promise that resolves to `FileSystemDownloadResult` object, or to `undefined` when task was cancelled.\n   */\n  async resumeAsync(): Promise<FileSystemDownloadResult | undefined> {\n    if (!ExponentFileSystem.downloadResumableStartAsync) {\n      throw new UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');\n    }\n\n    if (this.isTaskCancelled()) {\n      return;\n    }\n\n    this.addSubscription();\n    return await ExponentFileSystem.downloadResumableStartAsync(\n      this.url,\n      this.fileUri,\n      this.uuid,\n      this.options,\n      this.resumeData\n    );\n  }\n\n  /**\n   * Method to get the object which can be saved with `AsyncStorage` for future retrieval.\n   * @returns Returns object in shape of `DownloadPauseState` type.\n   */\n  savable(): DownloadPauseState {\n    return {\n      url: this.url,\n      fileUri: this.fileUri,\n      options: this.options,\n      resumeData: this.resumeData,\n    };\n  }\n}\n\nconst baseReadAsStringAsync = readAsStringAsync;\nconst baseWriteAsStringAsync = writeAsStringAsync;\nconst baseDeleteAsync = deleteAsync;\nconst baseMoveAsync = moveAsync;\nconst baseCopyAsync = copyAsync;\n\n/**\n * The `StorageAccessFramework` is a namespace inside of the `expo-file-system` module, which encapsulates all functions which can be used with [SAF URIs](#saf-uri).\n * You can read more about SAF in the [Android documentation](https://developer.android.com/guide/topics/providers/document-provider).\n *\n * @example\n * # Basic Usage\n *\n * ```ts\n * import { StorageAccessFramework } from 'expo-file-system';\n *\n * // Requests permissions for external directory\n * const permissions = await StorageAccessFramework.requestDirectoryPermissionsAsync();\n *\n * if (permissions.granted) {\n *   // Gets SAF URI from response\n *   const uri = permissions.directoryUri;\n *\n *   // Gets all files inside of selected directory\n *   const files = await StorageAccessFramework.readDirectoryAsync(uri);\n *   alert(`Files inside ${uri}:\\n\\n${JSON.stringify(files)}`);\n * }\n * ```\n *\n * # Migrating an album\n *\n * ```ts\n * import * as MediaLibrary from 'expo-media-library';\n * import * as FileSystem from 'expo-file-system';\n * const { StorageAccessFramework } = FileSystem;\n *\n * async function migrateAlbum(albumName: string) {\n *   // Gets SAF URI to the album\n *   const albumUri = StorageAccessFramework.getUriForDirectoryInRoot(albumName);\n *\n *   // Requests permissions\n *   const permissions = await StorageAccessFramework.requestDirectoryPermissionsAsync(albumUri);\n *   if (!permissions.granted) {\n *     return;\n *   }\n *\n *   const permittedUri = permissions.directoryUri;\n *   // Checks if users selected the correct folder\n *   if (!permittedUri.includes(albumName)) {\n *     return;\n *   }\n *\n *   const mediaLibraryPermissions = await MediaLibrary.requestPermissionsAsync();\n *   if (!mediaLibraryPermissions.granted) {\n *     return;\n *   }\n *\n *   // Moves files from external storage to internal storage\n *   await StorageAccessFramework.moveAsync({\n *     from: permittedUri,\n *     to: FileSystem.documentDirectory!,\n *   });\n *\n *   const outputDir = FileSystem.documentDirectory! + albumName;\n *   const migratedFiles = await FileSystem.readDirectoryAsync(outputDir);\n *\n *   // Creates assets from local files\n *   const [newAlbumCreator, ...assets] = await Promise.all(\n *     migratedFiles.map<Promise<MediaLibrary.Asset>>(\n *       async fileName => await MediaLibrary.createAssetAsync(outputDir + '/' + fileName)\n *     )\n *   );\n *\n *   // Album was empty\n *   if (!newAlbumCreator) {\n *     return;\n *   }\n *\n *   // Creates a new album in the scoped directory\n *   const newAlbum = await MediaLibrary.createAlbumAsync(albumName, newAlbumCreator, false);\n *   if (assets.length) {\n *     await MediaLibrary.addAssetsToAlbumAsync(assets, newAlbum, false);\n *   }\n * }\n * ```\n * @platform Android\n */\nexport namespace StorageAccessFramework {\n  /**\n   * Gets a [SAF URI](#saf-uri) pointing to a folder in the Android root directory. You can use this function to get URI for\n   * `StorageAccessFramework.requestDirectoryPermissionsAsync()` when you trying to migrate an album. In that case, the name of the album is the folder name.\n   * @param folderName The name of the folder which is located in the Android root directory.\n   * @return Returns a [SAF URI](#saf-uri) to a folder.\n   */\n  export function getUriForDirectoryInRoot(folderName: string) {\n    return `content://com.android.externalstorage.documents/tree/primary:${folderName}/document/primary:${folderName}`;\n  }\n\n  /**\n   * Allows users to select a specific directory, granting your app access to all of the files and sub-directories within that directory.\n   * @param initialFileUrl The [SAF URI](#saf-uri) of the directory that the file picker should display when it first loads.\n   * If URI is incorrect or points to a non-existing folder, it's ignored.\n   * @platform android 11+\n   * @return Returns a Promise that resolves to `FileSystemRequestDirectoryPermissionsResult` object.\n   */\n  export async function requestDirectoryPermissionsAsync(\n    initialFileUrl: string | null = null\n  ): Promise<FileSystemRequestDirectoryPermissionsResult> {\n    if (!ExponentFileSystem.requestDirectoryPermissionsAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.requestDirectoryPermissionsAsync'\n      );\n    }\n\n    return await ExponentFileSystem.requestDirectoryPermissionsAsync(initialFileUrl);\n  }\n\n  /**\n   * Enumerate the contents of a directory.\n   * @param dirUri [SAF](#saf-uri) URI to the directory.\n   * @return A Promise that resolves to an array of strings, each containing the full [SAF URI](#saf-uri) of a file or directory contained in the directory at `fileUri`.\n   */\n  export async function readDirectoryAsync(dirUri: string): Promise<string[]> {\n    if (!ExponentFileSystem.readSAFDirectoryAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.readDirectoryAsync'\n      );\n    }\n    return await ExponentFileSystem.readSAFDirectoryAsync(dirUri, {});\n  }\n\n  /**\n   * Creates a new empty directory.\n   * @param parentUri The [SAF](#saf-uri) URI to the parent directory.\n   * @param dirName The name of new directory.\n   * @return A Promise that resolves to a [SAF URI](#saf-uri) to the created directory.\n   */\n  export async function makeDirectoryAsync(parentUri: string, dirName: string): Promise<string> {\n    if (!ExponentFileSystem.makeSAFDirectoryAsync) {\n      throw new UnavailabilityError(\n        'expo-file-system',\n        'StorageAccessFramework.makeDirectoryAsync'\n      );\n    }\n    return await ExponentFileSystem.makeSAFDirectoryAsync(parentUri, dirName);\n  }\n\n  /**\n   * Creates a new empty file.\n   * @param parentUri The [SAF](#saf-uri) URI to the parent directory.\n   * @param fileName The name of new file **without the extension**.\n   * @param mimeType The MIME type of new file.\n   * @return A Promise that resolves to a [SAF URI](#saf-uri) to the created file.\n   */\n  export async function createFileAsync(\n    parentUri: string,\n    fileName: string,\n    mimeType: string\n  ): Promise<string> {\n    if (!ExponentFileSystem.createSAFFileAsync) {\n      throw new UnavailabilityError('expo-file-system', 'StorageAccessFramework.createFileAsync');\n    }\n    return await ExponentFileSystem.createSAFFileAsync(parentUri, fileName, mimeType);\n  }\n\n  /**\n   * Alias for [`writeAsStringAsync`](#filesystemwriteasstringasyncfileuri-contents-options) method.\n   */\n  export const writeAsStringAsync = baseWriteAsStringAsync;\n  /**\n   * Alias for [`readAsStringAsync`](#filesystemreadasstringasyncfileuri-options) method.\n   */\n  export const readAsStringAsync = baseReadAsStringAsync;\n  /**\n   * Alias for [`deleteAsync`](#filesystemdeleteasyncfileuri-options) method.\n   */\n  export const deleteAsync = baseDeleteAsync;\n  /**\n   * Alias for [`moveAsync`](#filesystemmoveasyncoptions) method.\n   */\n  export const moveAsync = baseMoveAsync;\n  /**\n   * Alias fro [`copyAsync`](#filesystemcopyasyncoptions) method.\n   */\n  export const copyAsync = baseCopyAsync;\n}\n"]}