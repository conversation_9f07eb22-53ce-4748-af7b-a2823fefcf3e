{"version": 3, "file": "FileSystem.types.d.ts", "sourceRoot": "", "sources": ["../src/FileSystem.types.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,oBAAY,qBAAqB;IAC/B;;;;OAIG;IACH,UAAU,IAAI;IACd;;;OAGG;IACH,UAAU,IAAI;CACf;AAED,oBAAY,oBAAoB;IAC9B;;OAEG;IACH,cAAc,IAAI;IAClB;;;OAGG;IACH,SAAS,IAAI;CACd;AAED,MAAM,MAAM,eAAe,GAAG;IAC5B;;;OAGG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IAEd,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC;;;;OAIG;IACH,WAAW,CAAC,EAAE,qBAAqB,CAAC;CACrC,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC;;;OAGG;IACH,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;CACzB,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG,oBAAoB,GAAG;IAC5D;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IACZ;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;CACd,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG,wBAAwB,CAAC;AAEtD,MAAM,MAAM,uBAAuB,GAAG,CAAC,mBAAmB,GAAG,sBAAsB,CAAC,GAAG;IACrF;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC;;;OAGG;IACH,UAAU,CAAC,EAAE,kCAAkC,CAAC;IAChD;;;;OAIG;IACH,WAAW,CAAC,EAAE,qBAAqB,CAAC;CACrC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAChC;;;OAGG;IACH,UAAU,CAAC,EAAE,oBAAoB,CAAC;CACnC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAAG;IACnC;;;OAGG;IACH,UAAU,EAAE,oBAAoB,CAAC;IACjC;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACrC,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG,oBAAoB,GAAG;IAC1D;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAGF,MAAM,MAAM,qCAAqC,CAC/C,CAAC,SAAS,oBAAoB,GAAG,kBAAkB,IACjD,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,CAAC;AAEtB;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG,qCAAqC,CAAC,oBAAoB,CAAC,CAAC;AAEnG,MAAM,MAAM,oBAAoB,GAAG;IACjC;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAC1B;;;OAGG;IACH,yBAAyB,EAAE,MAAM,CAAC;CACnC,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,wBAAwB,EAAE,MAAM,CAAC;CAClC,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IACZ;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,OAAO,EAAE,eAAe,CAAC;IACzB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB,CAAC;AAGF,MAAM,MAAM,QAAQ;AAClB;;GAEG;AACH;IACE;;OAEG;IACH,MAAM,EAAE,IAAI,CAAC;IACb;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IACZ;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,WAAW,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,gBAAgB,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AACD;;GAEG;AACH;IACE,MAAM,EAAE,KAAK,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;IACZ,WAAW,EAAE,KAAK,CAAC;CACpB,CAAC;AAGJ;;GAEG;AACH,oBAAY,YAAY;IACtB;;OAEG;IACH,IAAI,SAAS;IACb;;OAEG;IACH,MAAM,WAAW;CAClB;AAGD,MAAM,MAAM,kCAAkC,GAAG,MAAM,GAAG,KAAK,GAAG,OAAO,CAAC;AAE1E,MAAM,MAAM,cAAc,GAAG;IAC3B;;;OAGG;IACH,QAAQ,CAAC,EAAE,YAAY,GAAG,MAAM,GAAG,QAAQ,CAAC;IAC5C;;SAEK;IACL,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC3B;;;OAGG;IACH,QAAQ,CAAC,EAAE,YAAY,GAAG,MAAM,GAAG,QAAQ,CAAC;CAC7C,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG;IAC5B;;;OAGG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG;IACxB;;;OAGG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IACd;;;OAGG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;CACZ,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC;;;OAGG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;CACzB,CAAC;AAGF,MAAM,MAAM,aAAa,CAAC,CAAC,IAAI;IAC7B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,CAAC,CAAC;CACT,CAAC;AAGF,MAAM,MAAM,2CAA2C;AACrD;;GAEG;AACH;IACE,OAAO,EAAE,KAAK,CAAC;CAChB;AACD;;GAEG;AACH;IACE,OAAO,EAAE,IAAI,CAAC;IACd;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;CACtB,CAAC"}