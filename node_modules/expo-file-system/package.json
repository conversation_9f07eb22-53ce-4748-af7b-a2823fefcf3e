{"name": "expo-file-system", "version": "15.2.2", "description": "Provides access to the local file system on the device.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "file-system", "file"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-file-system"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/filesystem/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"uuid": "^3.4.0"}, "devDependencies": {"expo-module-scripts": "^3.0.0", "jest-expo": "~48.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "b019b92ea02e108eece600aae9fb104a3611bdeb"}