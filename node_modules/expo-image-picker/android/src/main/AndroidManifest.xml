<manifest package="expo.modules.imagepicker"
          xmlns:android="http://schemas.android.com/apk/res/android"
    >
    <!-- Required for picking images from camera directly -->
    <uses-permission android:name="android.permission.CAMERA"/>

    <!-- Required for picking images from camera roll -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>

    <application>
      <activity
        android:name="com.canhub.cropper.CropImageActivity"
        android:theme="@style/Base.Theme.AppCompat"/>
      <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
        <provider
            android:name=".fileprovider.ImagePickerFileProvider"
            android:authorities="${applicationId}.ImagePickerFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/image_picker_provider_paths"/>
        </provider>
    </application>

    <queries>
        <intent>
            <!-- Required for picking images from the camera roll if targeting API 30 -->
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <!-- Required for picking images from the camera if targeting API 30 -->
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
        </intent>
    </queries>
</manifest>
