{"version": 3, "file": "ExponentImagePicker.web.js", "sourceRoot": "", "sources": ["../src/ExponentImagePicker.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,gBAAgB,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAEnF,OAAO,EAGL,gBAAgB,GAEjB,MAAM,qBAAqB,CAAC;AAE7B,MAAM,cAAc,GAAG;IACrB,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,uDAAuD;IAC/E,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,SAAS;IACpC,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,+CAA+C;CAC3E,CAAC;AAEF,eAAe;IACb,IAAI,IAAI;QACN,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,EAC5B,UAAU,GAAG,gBAAgB,CAAC,MAAM,EACpC,uBAAuB,GAAG,KAAK,EAC/B,MAAM,GAAG,KAAK,GACf;QACC,YAAY;QACZ,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC5B,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;SACzC;QACD,OAAO,MAAM,oBAAoB,CAAC;YAChC,UAAU;YACV,uBAAuB;YACvB,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,EACtB,UAAU,GAAG,gBAAgB,CAAC,MAAM,EACpC,uBAAuB,GAAG,KAAK,EAC/B,MAAM,GAAG,KAAK,GACf;QACC,YAAY;QACZ,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC5B,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;SACzC;QACD,OAAO,MAAM,oBAAoB,CAAC;YAChC,UAAU;YACV,uBAAuB;YACvB,OAAO,EAAE,IAAI;YACb,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB;QAC7B,OAAO,yBAAyB,EAAE,CAAC;IACrC,CAAC;IACD,KAAK,CAAC,6BAA6B;QACjC,OAAO,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,+BAA+B,CAAC,UAAmB;QACvD,OAAO,yBAAyB,EAAE,CAAC;IACrC,CAAC;IACD,KAAK,CAAC,mCAAmC,CAAC,UAAmB;QAC3D,OAAO,yBAAyB,EAAE,CAAC;IACrC,CAAC;CACF,CAAC;AAEF,SAAS,yBAAyB;IAChC,OAAO;QACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;QAChC,OAAO,EAAE,OAAO;QAChB,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,IAAI;KAClB,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,EAC5B,UAAU,EACV,OAAO,GAAG,KAAK,EACf,uBAAuB,GAAG,KAAK,EAC/B,MAAM,GACiB;IACvB,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;IAEnD,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC9C,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IAC7B,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACnC,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;IAC9C,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAChD,IAAI,uBAAuB,EAAE;QAC3B,KAAK,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;KAC5C;IACD,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KACzC;IACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAEjC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;YAC1C,IAAI,KAAK,CAAC,KAAK,EAAE;gBACf,MAAM,KAAK,GAAG,uBAAuB,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvE,MAAM,MAAM,GAAuB,MAAM,OAAO,CAAC,GAAG,CAClD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,CAC5D,CAAC;gBAEF,OAAO,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;aACtC;iBAAM;gBACL,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;aAC3C;YACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QACtC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,QAAQ,CAAC,UAAgB,EAAE,OAA4B;IAC9D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE;YACpB,MAAM,CAAC,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC,CAAC;QACvF,CAAC,CAAC;QACF,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;YAC7B,MAAM,GAAG,GAAI,MAAc,CAAC,MAAM,CAAC;YACnC,MAAM,SAAS,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;YAE9D,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAC3B,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;gBAC1B,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;gBAChB,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE;oBAClB,OAAO,CAAC;wBACN,GAAG;wBACH,KAAK,EAAE,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,KAAK;wBACxC,MAAM,EAAE,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,MAAM;wBAC3C,iEAAiE;wBACjE,wDAAwD;wBACxD,mEAAmE;wBACnE,iDAAiD;wBACjD,4EAA4E;wBAC5E,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;qBACpE,CAAC,CAAC;gBACL,CAAC,CAAC;gBACF,KAAK,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;aACnC;iBAAM;gBACL,SAAS,EAAE,CAAC;aACb;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { PermissionResponse, PermissionStatus, Platform } from 'expo-modules-core';\n\nimport {\n  ImagePickerAsset,\n  ImagePickerResult,\n  MediaTypeOptions,\n  OpenFileBrowserOptions,\n} from './ImagePicker.types';\n\nconst MediaTypeInput = {\n  [MediaTypeOptions.All]: 'video/mp4,video/quicktime,video/x-m4v,video/*,image/*',\n  [MediaTypeOptions.Images]: 'image/*',\n  [MediaTypeOptions.Videos]: 'video/mp4,video/quicktime,video/x-m4v,video/*',\n};\n\nexport default {\n  get name(): string {\n    return 'ExponentImagePicker';\n  },\n\n  async launchImageLibraryAsync({\n    mediaTypes = MediaTypeOptions.Images,\n    allowsMultipleSelection = false,\n    base64 = false,\n  }): Promise<ImagePickerResult> {\n    // SSR guard\n    if (!Platform.isDOMAvailable) {\n      return { canceled: true, assets: null };\n    }\n    return await openFileBrowserAsync({\n      mediaTypes,\n      allowsMultipleSelection,\n      base64,\n    });\n  },\n\n  async launchCameraAsync({\n    mediaTypes = MediaTypeOptions.Images,\n    allowsMultipleSelection = false,\n    base64 = false,\n  }): Promise<ImagePickerResult> {\n    // SSR guard\n    if (!Platform.isDOMAvailable) {\n      return { canceled: true, assets: null };\n    }\n    return await openFileBrowserAsync({\n      mediaTypes,\n      allowsMultipleSelection,\n      capture: true,\n      base64,\n    });\n  },\n\n  /*\n   * Delegate to expo-permissions to request camera permissions\n   */\n  async getCameraPermissionsAsync() {\n    return permissionGrantedResponse();\n  },\n  async requestCameraPermissionsAsync() {\n    return permissionGrantedResponse();\n  },\n\n  /*\n   * Camera roll permissions don't need to be requested on web, so we always\n   * respond with granted.\n   */\n  async getMediaLibraryPermissionsAsync(_writeOnly: boolean) {\n    return permissionGrantedResponse();\n  },\n  async requestMediaLibraryPermissionsAsync(_writeOnly: boolean): Promise<PermissionResponse> {\n    return permissionGrantedResponse();\n  },\n};\n\nfunction permissionGrantedResponse(): PermissionResponse {\n  return {\n    status: PermissionStatus.GRANTED,\n    expires: 'never',\n    granted: true,\n    canAskAgain: true,\n  };\n}\n\nfunction openFileBrowserAsync({\n  mediaTypes,\n  capture = false,\n  allowsMultipleSelection = false,\n  base64,\n}: OpenFileBrowserOptions): Promise<ImagePickerResult> {\n  const mediaTypeFormat = MediaTypeInput[mediaTypes];\n\n  const input = document.createElement('input');\n  input.style.display = 'none';\n  input.setAttribute('type', 'file');\n  input.setAttribute('accept', mediaTypeFormat);\n  input.setAttribute('id', String(Math.random()));\n  if (allowsMultipleSelection) {\n    input.setAttribute('multiple', 'multiple');\n  }\n  if (capture) {\n    input.setAttribute('capture', 'camera');\n  }\n  document.body.appendChild(input);\n\n  return new Promise((resolve) => {\n    input.addEventListener('change', async () => {\n      if (input.files) {\n        const files = allowsMultipleSelection ? input.files : [input.files[0]];\n        const assets: ImagePickerAsset[] = await Promise.all(\n          Array.from(files).map((file) => readFile(file, { base64 }))\n        );\n\n        resolve({ canceled: false, assets });\n      } else {\n        resolve({ canceled: true, assets: null });\n      }\n      document.body.removeChild(input);\n    });\n\n    const event = new MouseEvent('click');\n    input.dispatchEvent(event);\n  });\n}\n\nfunction readFile(targetFile: Blob, options: { base64: boolean }): Promise<ImagePickerAsset> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onerror = () => {\n      reject(new Error(`Failed to read the selected media because the operation failed.`));\n    };\n    reader.onload = ({ target }) => {\n      const uri = (target as any).result;\n      const returnRaw = () => resolve({ uri, width: 0, height: 0 });\n\n      if (typeof uri === 'string') {\n        const image = new Image();\n        image.src = uri;\n        image.onload = () => {\n          resolve({\n            uri,\n            width: image.naturalWidth ?? image.width,\n            height: image.naturalHeight ?? image.height,\n            // The blob's result cannot be directly decoded as Base64 without\n            // first removing the Data-URL declaration preceding the\n            // Base64-encoded data. To retrieve only the Base64 encoded string,\n            // first remove data:*/*;base64, from the result.\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileReader/readAsDataURL\n            ...(options.base64 && { base64: uri.substr(uri.indexOf(',') + 1) }),\n          });\n        };\n        image.onerror = () => returnRaw();\n      } else {\n        returnRaw();\n      }\n    };\n\n    reader.readAsDataURL(targetFile);\n  });\n}\n"]}