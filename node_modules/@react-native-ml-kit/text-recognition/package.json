{"name": "@react-native-ml-kit/text-recognition", "title": "React Native ML Kit Text Recognition", "version": "1.5.2", "description": "React Native On-Device Text Recognition w/ Google ML Kit", "main": "index.ts", "files": ["README.md", "android", "index.ts", "ios", "RNMLKitTextRecognition.podspec"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/a7med-mahm<PERSON>/react-native-ml-kit.git", "baseUrl": "https://github.com/a7med-mahmoud/react-native-ml-kit"}, "keywords": ["react-native"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "licenseFilename": "LICENSE", "readmeFilename": "README.md", "peerDependencies": {"react": ">=16.8.1", "react-native": ">=0.60.0-rc.0 <1.0.x"}, "devDependencies": {"@types/react-native": "^0.72.3"}}