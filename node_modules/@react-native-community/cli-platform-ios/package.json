{"name": "@react-native-community/cli-platform-ios", "version": "11.3.6", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-tools": "11.3.6", "chalk": "^4.1.2", "execa": "^5.0.0", "fast-xml-parser": "^4.0.12", "glob": "^7.1.3", "ora": "^5.4.1"}, "devDependencies": {"@react-native-community/cli-types": "11.3.6", "@types/glob": "^7.1.1", "@types/lodash": "^4.14.149", "hasbin": "^1.2.3"}, "files": ["build", "!*.d.ts", "!*.map", "native_modules.rb"], "homepage": "https://github.com/react-native-community/cli/tree/master/packages/platform-ios", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/platform-ios"}, "gitHead": "ae61a11bda5d5c1b495f7bf5745123d8df8ffaef"}