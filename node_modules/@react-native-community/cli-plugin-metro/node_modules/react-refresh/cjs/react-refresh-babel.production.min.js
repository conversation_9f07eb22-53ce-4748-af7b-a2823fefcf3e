/** @license React vundefined
 * react-refresh-babel.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

'use strict';function v(q){function r(a,b){var d=a.scope.generateUidIdentifier("c");l.has(a)||l.set(a,[]);l.get(a).push({handle:d,persistentID:b});return d}function t(a){return"string"===typeof a&&"A"<=a[0]&&"Z">=a[0]}function m(a,b,d){var c=b.node;switch(c.type){case "Identifier":if(!t(c.name))break;d(a,c,null);return!0;case "FunctionDeclaration":return d(a,c.id,null),!0;case "ArrowFunctionExpression":if("ArrowFunctionExpression"===c.body.type)break;d(a,c,b);return!0;case "FunctionExpression":return d(a,
c,b),!0;case "CallExpression":var f=b.get("arguments");if(void 0===f||0===f.length)break;var g=b.get("callee");switch(g.node.type){case "MemberExpression":case "Identifier":g=g.getSource();if(!m(a+"$"+g,f[0],d))return!1;d(a,c,b);return!0;default:return!1}case "VariableDeclarator":if(f=c.init,null!==f&&(g=c.id.name,t(g))){switch(f.type){case "ArrowFunctionExpression":case "FunctionExpression":break;case "CallExpression":c=f.callee;var e=c.type;if("Import"===e)return!1;if("Identifier"===e){if(0===c.name.indexOf("require")||
0===c.name.indexOf("import"))return!1}else if("MemberExpression"!==e)return!1;break;case "TaggedTemplateExpression":break;default:return!1}c=b.get("init");if(m(a,c,d))return!0;g=b.scope.getBinding(g);if(void 0===g)return;b=!1;g=g.referencePaths;for(e=0;e<g.length;e++){var h=g[e];if("JSXIdentifier"===h.node.type||"Identifier"===h.node.type){h=h.parent;if("JSXOpeningElement"===h.type)b=!0;else if("CallExpression"===h.type){h=h.callee;var k=void 0;switch(h.type){case "Identifier":k=h.name;break;case "MemberExpression":k=
h.property.name}switch(k){case "createElement":case "jsx":case "jsxDEV":case "jsxs":b=!0}}if(b)return d(a,f,c),!0}}}}return!1}function w(a){a=n.get(a);return void 0===a?null:{key:a.map(function(a){return a.name+"{"+a.key+"}"}).join("\n"),customHooks:a.filter(function(a){a:switch(a.name){case "useState":case "React.useState":case "useReducer":case "React.useReducer":case "useEffect":case "React.useEffect":case "useLayoutEffect":case "React.useLayoutEffect":case "useMemo":case "React.useMemo":case "useCallback":case "React.useCallback":case "useRef":case "React.useRef":case "useContext":case "React.useContext":case "useImperativeMethods":case "React.useImperativeMethods":case "useDebugValue":case "React.useDebugValue":a=
!0;break a;default:a=!1}return!a}).map(function(a){return e.cloneDeep(a.callee)})}}function D(a){a=a.hub.file;var b=x.get(a);if(void 0!==b)return b;b=!1;for(var d=a.ast.comments,c=0;c<d.length;c++)if(-1!==d[c].value.indexOf("@refresh reset")){b=!0;break}x.set(a,b);return b}function u(a,b,d){var c=b.key;b=b.customHooks;var f=D(d.path),g=[];b.forEach(function(a){switch(a.type){case "MemberExpression":if("Identifier"===a.object.type)var b=a.object.name;break;case "Identifier":b=a.name}d.hasBinding(b)?
g.push(a):f=!0});b=c;"function"!==typeof require||y.emitFullSignatures||(b=require("crypto").createHash("sha1").update(c).digest("base64"));a=[a,e.stringLiteral(b)];(f||0<g.length)&&a.push(e.booleanLiteral(f));0<g.length&&a.push(e.functionExpression(null,[],e.blockStatement([e.returnStatement(e.arrayExpression(g))])));return a}var y=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if("function"===typeof q.getEnv){var z=q.getEnv();if("development"!==z&&!y.skipEnvCheck)throw Error('React Refresh Babel transform should only be enabled in development environment. Instead, the environment is: "'+
z+'". If you want to override this check, pass {skipEnvCheck: true} as plugin options.');}var e=q.types,l=new Map,x=new WeakMap,k=new WeakSet,p=new WeakSet,A=new WeakSet,n=new WeakMap,E={CallExpression:function(a){var b=a.node.callee,d=null;switch(b.type){case "Identifier":d=b.name;break;case "MemberExpression":d=b.property.name}if(null!==d&&/^use[A-Z]/.test(d)&&(b=a.scope.getFunctionParent(),null!==b)){b=b.block;n.has(b)||n.set(b,[]);b=n.get(b);var c="";"VariableDeclarator"===a.parent.type&&(c=a.parentPath.get("id").getSource());
var f=a.get("arguments");"useState"===d&&0<f.length?c+="("+f[0].getSource()+")":"useReducer"===d&&1<f.length&&(c+="("+f[1].getSource()+")");b.push({callee:a.node.callee,name:d,key:c})}}};return{visitor:{ExportDefaultDeclaration:function(a){var b=a.node,d=b.declaration,c=a.get("declaration");if("CallExpression"===d.type&&!k.has(b)){k.add(b);var f=a.parentPath;m("%default%",c,function(a,b,c){null!==c&&(a=r(f,a),c.replaceWith(e.assignmentExpression("=",a,b)))})}},FunctionDeclaration:{enter:function(a){var b=
a.node;switch(a.parent.type){case "Program":var d=a;var c=a.parentPath;break;case "ExportNamedDeclaration":d=a.parentPath;c=d.parentPath;break;case "ExportDefaultDeclaration":d=a.parentPath;c=d.parentPath;break;default:return}var f=b.id;null!==f&&(f=f.name,t(f)&&!k.has(b)&&(k.add(b),m(f,a,function(a,b){a=r(c,a);d.insertAfter(e.expressionStatement(e.assignmentExpression("=",a,b)))})))},exit:function(a){var b=a.node,d=b.id;if(null!==d){var c=w(b);if(null!==c&&!p.has(b)){p.add(b);b=a.scope.generateUidIdentifier("_s");
a.scope.parent.push({id:b,init:e.callExpression(e.identifier("$RefreshSig$"),[])});a.get("body").unshiftContainer("body",e.expressionStatement(e.callExpression(b,[])));var f=null;a.find(function(a){if(a.parentPath.isBlock())return f=a,!0});null!==f&&f.insertAfter(e.expressionStatement(e.callExpression(b,u(d,c,f.scope))))}}}},"ArrowFunctionExpression|FunctionExpression":{exit:function(a){var b=a.node,d=w(b);if(null!==d&&!p.has(b)){p.add(b);var c=a.scope.generateUidIdentifier("_s");a.scope.parent.push({id:c,
init:e.callExpression(e.identifier("$RefreshSig$"),[])});"BlockStatement"!==a.node.body.type&&(a.node.body=e.blockStatement([e.returnStatement(a.node.body)]));a.get("body").unshiftContainer("body",e.expressionStatement(e.callExpression(c,[])));if("VariableDeclarator"===a.parent.type){var f=null;a.find(function(a){if(a.parentPath.isBlock())return f=a,!0});null!==f&&f.insertAfter(e.expressionStatement(e.callExpression(c,u(a.parent.id,d,f.scope))))}else a.replaceWith(e.callExpression(c,u(b,d,a.scope)))}}},
VariableDeclaration:function(a){var b=a.node;switch(a.parent.type){case "Program":var d=a;var c=a.parentPath;break;case "ExportNamedDeclaration":d=a.parentPath;c=d.parentPath;break;case "ExportDefaultDeclaration":d=a.parentPath;c=d.parentPath;break;default:return}if(!k.has(b)&&(k.add(b),a=a.get("declarations"),1===a.length)){var f=a[0];m(f.node.id.name,f,function(a,b,h){null!==h&&(a=r(c,a),"ArrowFunctionExpression"!==b.type&&"FunctionExpression"!==b.type||"VariableDeclarator"!==h.parent.type?h.replaceWith(e.assignmentExpression("=",
a,b)):d.insertAfter(e.expressionStatement(e.assignmentExpression("=",a,f.node.id))))})}},Program:{enter:function(a){a.traverse(E)},exit:function(a){var b=l.get(a);if(void 0!==b){var d=a.node;if(!A.has(d)){A.add(d);l.delete(a);var c=[];a.pushContainer("body",e.variableDeclaration("var",c));b.forEach(function(b){var d=b.handle;b=b.persistentID;a.pushContainer("body",e.expressionStatement(e.callExpression(e.identifier("$RefreshReg$"),[d,e.stringLiteral(b)])));c.push(e.variableDeclarator(d))})}}}}}}}
var B={default:v},C=B&&v||B;module.exports=C.default||C;
