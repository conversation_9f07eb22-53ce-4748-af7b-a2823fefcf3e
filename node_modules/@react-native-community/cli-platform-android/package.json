{"name": "@react-native-community/cli-platform-android", "version": "11.3.6", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-tools": "11.3.6", "chalk": "^4.1.2", "execa": "^5.0.0", "glob": "^7.1.3", "logkitty": "^0.7.1"}, "files": ["build", "!*.d.ts", "!*.map", "native_modules.gradle"], "devDependencies": {"@react-native-community/cli-plugin-metro": "11.3.6", "@react-native-community/cli-types": "11.3.6", "@types/fs-extra": "^8.1.0", "@types/glob": "^7.1.1"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/platform-android", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/platform-android"}, "gitHead": "ae61a11bda5d5c1b495f7bf5745123d8df8ffaef"}