{"version": 3, "file": "JsonFile.js", "sourceRoot": "", "sources": ["../src/JsonFile.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAqD;AACrD,4CAAoB;AACpB,kDAA0B;AAC1B,gDAAwB;AACxB,+BAAiC;AACjC,0EAAgD;AAEhD,iEAAoE;AAEpE,MAAM,oBAAoB,GAId,IAAA,gBAAS,EAAC,2BAAe,CAAC,CAAC;AAqBvC,MAAM,eAAe,GAAG;IACtB,cAAc,EAAE,SAAS;IACzB,qBAAqB,EAAE,SAAS;IAChC,mBAAmB,EAAE,SAAS;IAC9B,SAAS,EAAE,KAAK;IAChB,OAAO,EAAE,SAAS;IAClB,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,CAAC;IACR,eAAe,EAAE,IAAI;CACtB,CAAC;AAEF;;;;;;GAMG;AACH,MAAqB,QAAQ;IAe3B,YAAY,IAAY,EAAE,UAAgC,EAAE;QAC1D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,IAAI,CAAC,OAA8B;QACjC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAA8B;QAC5C,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAmB,EAAE,OAA8B;QAClE,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,eAAe,CAAC,IAAY,EAAE,OAA8B;QAC1D,OAAO,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,GAAM,EACN,YAAsB,EACtB,OAA8B;QAE9B,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAW,EAAE,KAAc,EAAE,OAA8B;QACxE,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,UAAU,CACd,OAAsD,EACtD,OAA8B;QAE9B,OAAO,UAAU,CAAc,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,GAAW,EAAE,OAA8B;QAC9D,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,IAAc,EAAE,OAA8B;QAClE,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAA8B;QAC/C,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,WAAW,CAAC,OAA8B;QACxC,OAAO;YACL,GAAG,IAAI,CAAC,OAAO;YACf,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;;AAxEH,2BAyEC;AArEQ,aAAI,GAAG,IAAI,CAAC;AACZ,kBAAS,GAAG,SAAS,CAAC;AACtB,wBAAe,GAAG,eAAe,CAAC;AAClC,mBAAU,GAAG,UAAU,CAAC;AACxB,iBAAQ,GAAG,QAAQ,CAAC;AACpB,iBAAQ,GAAG,QAAQ,CAAC;AACpB,mBAAU,GAAG,UAAU,CAAC;AACxB,uBAAc,GAAG,cAAc,CAAC;AAChC,wBAAe,GAAG,eAAe,CAAC;AAClC,qBAAY,GAAG,YAAY,CAAC;AA8DrC,SAAS,IAAI,CACX,IAAY,EACZ,OAA8B;IAE9B,IAAI,IAAI,CAAC;IACT,IAAI;QACF,IAAI,GAAG,YAAE,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACtC;IAAC,OAAO,KAAU,EAAE;QACnB,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,IAAI,uBAAa,CAAC,yBAAyB,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACnF;aAAM;YACL,OAAO,YAAY,CAAC;SACrB;KACF;IACD,OAAO,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC9C,CAAC;AAED,KAAK,UAAU,SAAS,CACtB,IAAY,EACZ,OAA8B;IAE9B,IAAI,IAAI,CAAC;IACT,IAAI;QACF,IAAI,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACjD;IAAC,OAAO,KAAU,EAAE;QACnB,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAClC,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,IAAI,uBAAa,CAAC,yBAAyB,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SAC7E;aAAM;YACL,OAAO,YAAY,CAAC;SACrB;KACF;IACD,OAAO,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,eAAe,CACtB,IAAY,EACZ,OAA8B,EAC9B,QAAiB;IAEjB,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,IAAI;QACF,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YAChC,OAAO,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACzB;KACF;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,QAAQ,GAAG,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAClD,IAAI,QAAQ,EAAE;gBACZ,MAAM,SAAS,GAAG,IAAA,6BAAgB,EAAC,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAC9D,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC;gBACxB,CAAC,CAAC,OAAO,IAAI,KAAK,SAAS,EAAE,CAAC;aAC/B;YACD,MAAM,IAAI,uBAAa,CAAC,uBAAuB,IAAI,EAAE,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;SACnF;aAAM;YACL,OAAO,YAAY,CAAC;SACrB;KACF;AACH,CAAC;AAED,KAAK,UAAU,QAAQ,CACrB,IAAY,EACZ,GAAM,EACN,YAA0B,EAC1B,OAA8B;IAE9B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,IAAI,GAAG,IAAI,MAAM,EAAE;QACjB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;KACpB;IACD,IAAI,YAAY,KAAK,SAAS,EAAE;QAC9B,MAAM,IAAI,uBAAa,CAAC,yBAAyB,GAAG,0BAA0B,IAAI,EAAE,CAAC,CAAC;KACvF;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,IAAY,EACZ,MAAmB,EACnB,OAA8B;IAE9B,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE;QACtB,MAAM,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;KAClE;IACD,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3C,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3C,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAC/D,IAAI,IAAI,CAAC;IACT,IAAI;QACF,IAAI,KAAK,EAAE;YACT,IAAI,GAAG,eAAK,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC7C;aAAM;YACL,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;SAC5C;KACF;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,IAAI,uBAAa,CAAC,4CAA4C,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;KAChF;IACD,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAClD,MAAM,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3C,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,QAAQ,CACrB,IAAY,EACZ,GAAW,EACX,KAAc,EACd,OAA8B;IAE9B,kEAAkE;IAClE,oEAAoE;IACpE,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,OAAO,UAAU,CAAC,IAAI,EAAE,EAAE,GAAG,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,IAAY,EACZ,OAAsD,EACtD,OAA8B;IAE9B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC1B,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC;KACnC;SAAM;QACL,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KAChC;IACD,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3C,CAAC;AAED,KAAK,UAAU,cAAc,CAC3B,IAAY,EACZ,GAAW,EACX,OAA8B;IAE9B,OAAO,eAAe,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC;AAED,KAAK,UAAU,eAAe,CAC5B,IAAY,EACZ,IAAc,EACd,OAA8B;IAE9B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC9B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;YACnB,SAAS,GAAG,IAAI,CAAC;SAClB;KACF;IAED,IAAI,SAAS,EAAE;QACb,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;KAC1C;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,YAAY,CACzB,IAAY,EACZ,OAA8B;IAE9B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9C,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,qBAAqB,CAC5B,UAAgC,EAAE;IAElC,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC/C,OAAO,OAAO,CAAC,OAAO,CAAC;KACxB;SAAM;QACL,OAAO,OAAO,CAAC,qBAAqB,CAAC;KACtC;AACH,CAAC;AAED,SAAS,mBAAmB,CAC1B,UAAgC,EAAE;IAElC,IAAI,OAAO,CAAC,mBAAmB,KAAK,SAAS,EAAE;QAC7C,OAAO,OAAO,CAAC,OAAO,CAAC;KACxB;SAAM;QACL,OAAO,OAAO,CAAC,mBAAmB,CAAC;KACpC;AACH,CAAC;AAED,SAAS,UAAU,CACjB,OAAyC,EACzC,KAAQ;IAER,IAAI,OAAO,EAAE;QACX,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;YAChC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;SACvB;KACF;IACD,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,uBAAuB,CAAC,KAAU,EAAE,YAAoB;IAC/D,qDAAqD;IACrD,IAAI,YAAY,IAAI,KAAK,IAAI,cAAc,IAAI,KAAK,EAAE;QACpD,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC;KAC/D;IACD,2DAA2D;IAC3D,MAAM,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACtD,IAAI,KAAK,EAAE;QACT,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3D,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;KACvE;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,qBAAqB,CAAC,IAAa,EAAE,IAAa;IACzD,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,EAAE,MAAK,EAAE,EAAE;QACvB,MAAM,IAAI,kCAAkB,CAAC,IAAI,CAAC,CAAC;KACpC;AACH,CAAC", "sourcesContent": ["import { codeFrameColumns } from '@babel/code-frame';\nimport fs from 'fs';\nimport JSON5 from 'json5';\nimport path from 'path';\nimport { promisify } from 'util';\nimport writeFileAtomic from 'write-file-atomic';\n\nimport JsonFileError, { EmptyJsonFileError } from './JsonFileError';\n\nconst writeFileAtomicAsync: (\n  filename: string,\n  data: string | Buffer,\n  options: writeFileAtomic.Options\n) => void = promisify(writeFileAtomic);\n\nexport type JSONValue = boolean | number | string | null | JSONArray | JSONObject;\nexport interface JSONArray extends Array<JSONValue> {}\nexport interface JSONObject {\n  [key: string]: JSONValue | undefined;\n}\n\ntype Defined<T> = T extends undefined ? never : T;\n\ntype Options<TJSONObject extends JSONObject> = {\n  badJsonDefault?: TJSONObject;\n  jsonParseErrorDefault?: TJSONObject;\n  cantReadFileDefault?: TJSONObject;\n  ensureDir?: boolean;\n  default?: TJSONObject;\n  json5?: boolean;\n  space?: number;\n  addNewLineAtEOF?: boolean;\n};\n\nconst DEFAULT_OPTIONS = {\n  badJsonDefault: undefined,\n  jsonParseErrorDefault: undefined,\n  cantReadFileDefault: undefined,\n  ensureDir: false,\n  default: undefined,\n  json5: false,\n  space: 2,\n  addNewLineAtEOF: true,\n};\n\n/**\n * The JsonFile class represents the contents of json file.\n *\n * It's polymorphic on \"JSONObject\", which is a simple type representing\n * and object with string keys and either objects or primitive types as values.\n * @type {[type]}\n */\nexport default class JsonFile<TJSONObject extends JSONObject> {\n  file: string;\n  options: Options<TJSONObject>;\n\n  static read = read;\n  static readAsync = readAsync;\n  static parseJsonString = parseJsonString;\n  static writeAsync = writeAsync;\n  static getAsync = getAsync;\n  static setAsync = setAsync;\n  static mergeAsync = mergeAsync;\n  static deleteKeyAsync = deleteKeyAsync;\n  static deleteKeysAsync = deleteKeysAsync;\n  static rewriteAsync = rewriteAsync;\n\n  constructor(file: string, options: Options<TJSONObject> = {}) {\n    this.file = file;\n    this.options = options;\n  }\n\n  read(options?: Options<TJSONObject>): TJSONObject {\n    return read(this.file, this._getOptions(options));\n  }\n\n  async readAsync(options?: Options<TJSONObject>): Promise<TJSONObject> {\n    return readAsync(this.file, this._getOptions(options));\n  }\n\n  async writeAsync(object: TJSONObject, options?: Options<TJSONObject>) {\n    return writeAsync(this.file, object, this._getOptions(options));\n  }\n\n  parseJsonString(json: string, options?: Options<TJSONObject>): TJSONObject {\n    return parseJsonString(json, options);\n  }\n\n  async getAsync<K extends keyof TJSONObject, TDefault extends TJSONObject[K] | null>(\n    key: K,\n    defaultValue: TDefault,\n    options?: Options<TJSONObject>\n  ): Promise<Defined<TJSONObject[K]> | TDefault> {\n    return getAsync(this.file, key, defaultValue, this._getOptions(options));\n  }\n\n  async setAsync(key: string, value: unknown, options?: Options<TJSONObject>) {\n    return setAsync(this.file, key, value, this._getOptions(options));\n  }\n\n  async mergeAsync(\n    sources: Partial<TJSONObject> | Partial<TJSONObject>[],\n    options?: Options<TJSONObject>\n  ): Promise<TJSONObject> {\n    return mergeAsync<TJSONObject>(this.file, sources, this._getOptions(options));\n  }\n\n  async deleteKeyAsync(key: string, options?: Options<TJSONObject>) {\n    return deleteKeyAsync(this.file, key, this._getOptions(options));\n  }\n\n  async deleteKeysAsync(keys: string[], options?: Options<TJSONObject>) {\n    return deleteKeysAsync(this.file, keys, this._getOptions(options));\n  }\n\n  async rewriteAsync(options?: Options<TJSONObject>) {\n    return rewriteAsync(this.file, this._getOptions(options));\n  }\n\n  _getOptions(options?: Options<TJSONObject>): Options<TJSONObject> {\n    return {\n      ...this.options,\n      ...options,\n    };\n  }\n}\n\nfunction read<TJSONObject extends JSONObject>(\n  file: string,\n  options?: Options<TJSONObject>\n): TJSONObject {\n  let json;\n  try {\n    json = fs.readFileSync(file, 'utf8');\n  } catch (error: any) {\n    assertEmptyJsonString(json, file);\n    const defaultValue = cantReadFileDefault(options);\n    if (defaultValue === undefined) {\n      throw new JsonFileError(`Can't read JSON file: ${file}`, error, error.code, file);\n    } else {\n      return defaultValue;\n    }\n  }\n  return parseJsonString(json, options, file);\n}\n\nasync function readAsync<TJSONObject extends JSONObject>(\n  file: string,\n  options?: Options<TJSONObject>\n): Promise<TJSONObject> {\n  let json;\n  try {\n    json = await fs.promises.readFile(file, 'utf8');\n  } catch (error: any) {\n    assertEmptyJsonString(json, file);\n    const defaultValue = cantReadFileDefault(options);\n    if (defaultValue === undefined) {\n      throw new JsonFileError(`Can't read JSON file: ${file}`, error, error.code);\n    } else {\n      return defaultValue;\n    }\n  }\n  return parseJsonString(json, options);\n}\n\nfunction parseJsonString<TJSONObject extends JSONObject>(\n  json: string,\n  options?: Options<TJSONObject>,\n  fileName?: string\n): TJSONObject {\n  assertEmptyJsonString(json, fileName);\n  try {\n    if (_getOption(options, 'json5')) {\n      return JSON5.parse(json);\n    } else {\n      return JSON.parse(json);\n    }\n  } catch (e: any) {\n    const defaultValue = jsonParseErrorDefault(options);\n    if (defaultValue === undefined) {\n      const location = locationFromSyntaxError(e, json);\n      if (location) {\n        const codeFrame = codeFrameColumns(json, { start: location });\n        e.codeFrame = codeFrame;\n        e.message += `\\n${codeFrame}`;\n      }\n      throw new JsonFileError(`Error parsing JSON: ${json}`, e, 'EJSONPARSE', fileName);\n    } else {\n      return defaultValue;\n    }\n  }\n}\n\nasync function getAsync<TJSONObject extends JSONObject, K extends keyof TJSONObject, DefaultValue>(\n  file: string,\n  key: K,\n  defaultValue: DefaultValue,\n  options?: Options<TJSONObject>\n): Promise<any> {\n  const object = await readAsync(file, options);\n  if (key in object) {\n    return object[key];\n  }\n  if (defaultValue === undefined) {\n    throw new JsonFileError(`No value at key path \"${key}\" in JSON object from: ${file}`);\n  }\n  return defaultValue;\n}\n\nasync function writeAsync<TJSONObject extends JSONObject>(\n  file: string,\n  object: TJSONObject,\n  options?: Options<TJSONObject>\n): Promise<TJSONObject> {\n  if (options?.ensureDir) {\n    await fs.promises.mkdir(path.dirname(file), { recursive: true });\n  }\n  const space = _getOption(options, 'space');\n  const json5 = _getOption(options, 'json5');\n  const addNewLineAtEOF = _getOption(options, 'addNewLineAtEOF');\n  let json;\n  try {\n    if (json5) {\n      json = JSON5.stringify(object, null, space);\n    } else {\n      json = JSON.stringify(object, null, space);\n    }\n  } catch (e: any) {\n    throw new JsonFileError(`Couldn't JSON.stringify object for file: ${file}`, e);\n  }\n  const data = addNewLineAtEOF ? `${json}\\n` : json;\n  await writeFileAtomicAsync(file, data, {});\n  return object;\n}\n\nasync function setAsync<TJSONObject extends JSONObject>(\n  file: string,\n  key: string,\n  value: unknown,\n  options?: Options<TJSONObject>\n): Promise<TJSONObject> {\n  // TODO: Consider implementing some kind of locking mechanism, but\n  // it's not critical for our use case, so we'll leave it out for now\n  const object = await readAsync(file, options);\n  return writeAsync(file, { ...object, [key]: value }, options);\n}\n\nasync function mergeAsync<TJSONObject extends JSONObject>(\n  file: string,\n  sources: Partial<TJSONObject> | Partial<TJSONObject>[],\n  options?: Options<TJSONObject>\n): Promise<TJSONObject> {\n  const object = await readAsync(file, options);\n  if (Array.isArray(sources)) {\n    Object.assign(object, ...sources);\n  } else {\n    Object.assign(object, sources);\n  }\n  return writeAsync(file, object, options);\n}\n\nasync function deleteKeyAsync<TJSONObject extends JSONObject>(\n  file: string,\n  key: string,\n  options?: Options<TJSONObject>\n): Promise<TJSONObject> {\n  return deleteKeysAsync(file, [key], options);\n}\n\nasync function deleteKeysAsync<TJSONObject extends JSONObject>(\n  file: string,\n  keys: string[],\n  options?: Options<TJSONObject>\n): Promise<TJSONObject> {\n  const object = await readAsync(file, options);\n  let didDelete = false;\n\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    if (object.hasOwnProperty(key)) {\n      delete object[key];\n      didDelete = true;\n    }\n  }\n\n  if (didDelete) {\n    return writeAsync(file, object, options);\n  }\n  return object;\n}\n\nasync function rewriteAsync<TJSONObject extends JSONObject>(\n  file: string,\n  options?: Options<TJSONObject>\n): Promise<TJSONObject> {\n  const object = await readAsync(file, options);\n  return writeAsync(file, object, options);\n}\n\nfunction jsonParseErrorDefault<TJSONObject extends JSONObject>(\n  options: Options<TJSONObject> = {}\n): TJSONObject | void {\n  if (options.jsonParseErrorDefault === undefined) {\n    return options.default;\n  } else {\n    return options.jsonParseErrorDefault;\n  }\n}\n\nfunction cantReadFileDefault<TJSONObject extends JSONObject>(\n  options: Options<TJSONObject> = {}\n): TJSONObject | void {\n  if (options.cantReadFileDefault === undefined) {\n    return options.default;\n  } else {\n    return options.cantReadFileDefault;\n  }\n}\n\nfunction _getOption<TJSONObject extends JSONObject, K extends keyof Options<TJSONObject>>(\n  options: Options<TJSONObject> | undefined,\n  field: K\n): Options<TJSONObject>[K] {\n  if (options) {\n    if (options[field] !== undefined) {\n      return options[field];\n    }\n  }\n  return DEFAULT_OPTIONS[field];\n}\n\nfunction locationFromSyntaxError(error: any, sourceString: string) {\n  // JSON5 SyntaxError has lineNumber and columnNumber.\n  if ('lineNumber' in error && 'columnNumber' in error) {\n    return { line: error.lineNumber, column: error.columnNumber };\n  }\n  // JSON SyntaxError only includes the index in the message.\n  const match = /at position (\\d+)/.exec(error.message);\n  if (match) {\n    const index = parseInt(match[1], 10);\n    const lines = sourceString.slice(0, index + 1).split('\\n');\n    return { line: lines.length, column: lines[lines.length - 1].length };\n  }\n\n  return null;\n}\n\nfunction assertEmptyJsonString(json?: string, file?: string) {\n  if (json?.trim() === '') {\n    throw new EmptyJsonFileError(file);\n  }\n}\n"]}