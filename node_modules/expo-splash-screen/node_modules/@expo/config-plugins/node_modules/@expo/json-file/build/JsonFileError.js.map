{"version": 3, "file": "JsonFileError.js", "sourceRoot": "", "sources": ["../src/JsonFileError.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,MAAqB,aAAc,SAAQ,KAAK;IAM9C,YAAY,OAAe,EAAE,KAAa,EAAE,IAAa,EAAE,QAAiB;QAC1E,IAAI,WAAW,GAAG,OAAO,CAAC;QAC1B,IAAI,QAAQ,EAAE;YACZ,WAAW,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,WAAW,QAAQ,EAAE,CAAC;SAC5D;QACD,IAAI,KAAK,EAAE;YACT,WAAW,IAAI,eAAe,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;SAC9D;QACD,KAAK,CAAC,WAAW,CAAC,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;CACF;AArBD,gCAqBC;AAED,MAAa,kBAAmB,SAAQ,aAAa;IACnD,YAAY,QAAiB;QAC3B,KAAK,CAAC,mCAAmC,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IAChF,CAAC;CACF;AAJD,gDAIC", "sourcesContent": ["/**\n * Note that instances of this class do NOT pass `instanceof JsonFileError`.\n */\nexport default class JsonFileError extends Error {\n  cause: Error | undefined;\n  code: string | undefined;\n  fileName: string | undefined;\n  isJsonFileError: true;\n\n  constructor(message: string, cause?: Error, code?: string, fileName?: string) {\n    let fullMessage = message;\n    if (fileName) {\n      fullMessage += `\\n${cause ? '├' : '└'}─ File: ${fileName}`;\n    }\n    if (cause) {\n      fullMessage += `\\n└─ Cause: ${cause.name}: ${cause.message}`;\n    }\n    super(fullMessage);\n    this.name = this.constructor.name;\n    this.cause = cause;\n    this.code = code;\n    this.fileName = fileName;\n    this.isJsonFileError = true;\n  }\n}\n\nexport class EmptyJsonFileError extends JsonFileError {\n  constructor(fileName?: string) {\n    super(`Cannot parse an empty JSON string`, undefined, 'EJSONEMPTY', fileName);\n  }\n}\n"]}