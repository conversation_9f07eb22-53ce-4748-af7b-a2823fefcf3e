{"version": 3, "file": "errors.js", "names": ["UnexpectedError", "Error", "constructor", "message", "_defineProperty", "exports", "PluginError", "code", "cause", "name"], "sources": ["../../src/utils/errors.ts"], "sourcesContent": ["export class UnexpectedError extends Error {\n  readonly name = 'UnexpectedError';\n\n  constructor(message: string) {\n    super(`${message}\\nPlease report this as an issue on https://github.com/expo/expo-cli/issues`);\n  }\n}\n\nexport type PluginErrorCode =\n  | 'INVALID_PLUGIN_TYPE'\n  | 'INVALID_PLUGIN_IMPORT'\n  | 'PLUGIN_NOT_FOUND'\n  | 'CONFLICTING_PROVIDER'\n  | 'INVALID_MOD_ORDER'\n  | 'MISSING_PROVIDER';\n\n/**\n * Based on `JsonFileError` from `@expo/json-file`\n */\nexport class PluginError extends Error {\n  readonly name = 'PluginError';\n  readonly isPluginError = true;\n\n  constructor(message: string, public code: PluginErrorCode, public cause?: Error) {\n    super(cause ? `${message}\\n└─ Cause: ${cause.name}: ${cause.message}` : message);\n  }\n}\n"], "mappings": ";;;;;;;;;AAAO,MAAMA,eAAe,SAASC,KAAK,CAAC;EAGzCC,WAAWA,CAACC,OAAe,EAAE;IAC3B,KAAK,CAAE,GAAEA,OAAQ,6EAA4E,CAAC;IAACC,eAAA,eAHjF,iBAAiB;EAIjC;AACF;AAACC,OAAA,CAAAL,eAAA,GAAAA,eAAA;AAUD;AACA;AACA;AACO,MAAMM,WAAW,SAASL,KAAK,CAAC;EAIrCC,WAAWA,CAACC,OAAe,EAASI,IAAqB,EAASC,KAAa,EAAE;IAC/E,KAAK,CAACA,KAAK,GAAI,GAAEL,OAAQ,eAAcK,KAAK,CAACC,IAAK,KAAID,KAAK,CAACL,OAAQ,EAAC,GAAGA,OAAO,CAAC;IAAC,KAD/CI,IAAqB,GAArBA,IAAqB;IAAA,KAASC,KAAa,GAAbA,KAAa;IAAAJ,eAAA,eAH/D,aAAa;IAAAA,eAAA,wBACJ,IAAI;EAI7B;AACF;AAACC,OAAA,CAAAC,WAAA,GAAAA,WAAA"}