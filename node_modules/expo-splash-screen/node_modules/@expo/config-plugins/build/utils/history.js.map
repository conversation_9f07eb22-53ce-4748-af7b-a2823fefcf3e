{"version": 3, "file": "history.js", "names": ["getHistoryItem", "config", "name", "_config$_internal$plu", "_config$_internal", "_config$_internal$plu2", "_internal", "pluginHistory", "addHistoryItem", "item", "version"], "sources": ["../../src/utils/history.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { ModPlatform } from '../Plugin.types';\n\nexport type PluginHistoryItem = {\n  name: string;\n  version: string;\n  platform?: ModPlatform;\n};\n\nexport function getHistoryItem(\n  config: Pick<ExpoConfig, '_internal'>,\n  name: string\n): PluginHistoryItem | null {\n  return config._internal?.pluginHistory?.[name] ?? null;\n}\n\nexport function addHistoryItem(\n  config: ExpoConfig,\n  item: Omit<PluginHistoryItem, 'version'> & { version?: string }\n): ExpoConfig {\n  if (!config._internal) {\n    config._internal = {};\n  }\n  if (!config._internal.pluginHistory) {\n    config._internal.pluginHistory = {};\n  }\n\n  if (!item.version) {\n    item.version = 'UNVERSIONED';\n  }\n\n  config._internal.pluginHistory[item.name] = item;\n\n  return config;\n}\n"], "mappings": ";;;;;;;AAUO,SAASA,cAAcA,CAC5BC,MAAqC,EACrCC,IAAY,EACc;EAAA,IAAAC,qBAAA,EAAAC,iBAAA,EAAAC,sBAAA;EAC1B,QAAAF,qBAAA,IAAAC,iBAAA,GAAOH,MAAM,CAACK,SAAS,cAAAF,iBAAA,wBAAAC,sBAAA,GAAhBD,iBAAA,CAAkBG,aAAa,cAAAF,sBAAA,uBAA/BA,sBAAA,CAAkCH,IAAI,CAAC,cAAAC,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AACxD;AAEO,SAASK,cAAcA,CAC5BP,MAAkB,EAClBQ,IAA+D,EACnD;EACZ,IAAI,CAACR,MAAM,CAACK,SAAS,EAAE;IACrBL,MAAM,CAACK,SAAS,GAAG,CAAC,CAAC;EACvB;EACA,IAAI,CAACL,MAAM,CAACK,SAAS,CAACC,aAAa,EAAE;IACnCN,MAAM,CAACK,SAAS,CAACC,aAAa,GAAG,CAAC,CAAC;EACrC;EAEA,IAAI,CAACE,IAAI,CAACC,OAAO,EAAE;IACjBD,IAAI,CAACC,OAAO,GAAG,aAAa;EAC9B;EAEAT,MAAM,CAACK,SAAS,CAACC,aAAa,CAACE,IAAI,CAACP,IAAI,CAAC,GAAGO,IAAI;EAEhD,OAAOR,MAAM;AACf"}