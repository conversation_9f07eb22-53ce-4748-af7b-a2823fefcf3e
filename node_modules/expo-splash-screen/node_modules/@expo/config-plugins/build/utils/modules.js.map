{"version": 3, "file": "modules.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "obj", "__esModule", "default", "statAsync", "file", "fs", "promises", "stat", "fileExistsAsync", "_await$statAsync$isFi", "_await$statAsync", "isFile", "directoryExistsAsync", "_await$statAsync$isDi", "_await$statAsync2", "isDirectory", "fileExists", "statSync"], "sources": ["../../src/utils/modules.ts"], "sourcesContent": ["import fs from 'fs';\n\n/**\n * A non-failing version of async FS stat.\n *\n * @param file\n */\nasync function statAsync(file: string): Promise<fs.Stats | null> {\n  try {\n    return await fs.promises.stat(file);\n  } catch {\n    return null;\n  }\n}\n\nexport async function fileExistsAsync(file: string): Promise<boolean> {\n  return (await statAsync(file))?.isFile() ?? false;\n}\n\nexport async function directoryExistsAsync(file: string): Promise<boolean> {\n  return (await statAsync(file))?.isDirectory() ?? false;\n}\n\nexport function fileExists(file: string): boolean {\n  try {\n    return fs.statSync(file).isFile();\n  } catch {\n    return false;\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoB,SAAAC,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEpB;AACA;AACA;AACA;AACA;AACA,eAAeG,SAASA,CAACC,IAAY,EAA4B;EAC/D,IAAI;IACF,OAAO,MAAMC,aAAE,CAACC,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC;EACrC,CAAC,CAAC,MAAM;IACN,OAAO,IAAI;EACb;AACF;AAEO,eAAeI,eAAeA,CAACJ,IAAY,EAAoB;EAAA,IAAAK,qBAAA,EAAAC,gBAAA;EACpE,QAAAD,qBAAA,IAAAC,gBAAA,GAAQ,MAAMP,SAAS,CAACC,IAAI,CAAC,cAAAM,gBAAA,uBAAtBA,gBAAA,CAAyBC,MAAM,EAAE,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,KAAK;AACnD;AAEO,eAAeG,oBAAoBA,CAACR,IAAY,EAAoB;EAAA,IAAAS,qBAAA,EAAAC,iBAAA;EACzE,QAAAD,qBAAA,IAAAC,iBAAA,GAAQ,MAAMX,SAAS,CAACC,IAAI,CAAC,cAAAU,iBAAA,uBAAtBA,iBAAA,CAAyBC,WAAW,EAAE,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,KAAK;AACxD;AAEO,SAASG,UAAUA,CAACZ,IAAY,EAAW;EAChD,IAAI;IACF,OAAOC,aAAE,CAACY,QAAQ,CAACb,IAAI,CAAC,CAACO,MAAM,EAAE;EACnC,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF"}