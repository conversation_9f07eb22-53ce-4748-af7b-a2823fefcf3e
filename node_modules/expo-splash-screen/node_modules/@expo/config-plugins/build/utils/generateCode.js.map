{"version": 3, "file": "generateCode.js", "names": ["_crypto", "data", "_interopRequireDefault", "require", "obj", "__esModule", "default", "getGeneratedSectionIndexes", "src", "tag", "contents", "split", "start", "findIndex", "line", "includes", "end", "mergeContents", "newSrc", "anchor", "offset", "comment", "header", "createGeneratedHeaderComment", "sanitizedTarget", "removeGeneratedContents", "addLines", "didMerge", "<PERSON><PERSON><PERSON><PERSON>", "removeContents", "content", "find", "toAdd", "lines", "lineIndex", "match", "error", "Error", "code", "newLine", "splice", "join", "hash<PERSON><PERSON>", "createHash", "hash", "crypto", "update", "digest"], "sources": ["../../src/utils/generateCode.ts"], "sourcesContent": ["/**\n * Get line indexes for the generated section of a file.\n *\n * @param src\n */\nimport crypto from 'crypto';\n\nfunction getGeneratedSectionIndexes(\n  src: string,\n  tag: string\n): { contents: string[]; start: number; end: number } {\n  const contents = src.split('\\n');\n  const start = contents.findIndex((line) => line.includes(`@generated begin ${tag}`));\n  const end = contents.findIndex((line) => line.includes(`@generated end ${tag}`));\n\n  return { contents, start, end };\n}\n\nexport type MergeResults = {\n  contents: string;\n  didClear: boolean;\n  didMerge: boolean;\n};\n\n/**\n * Merge the contents of two files together and add a generated header.\n *\n * @param src contents of the original file\n * @param newSrc new contents to merge into the original file\n * @param identifier used to update and remove merges\n * @param anchor regex to where the merge should begin\n * @param offset line offset to start merging at (<1 for behind the anchor)\n * @param comment comment style `//` or `#`\n */\nexport function mergeContents({\n  src,\n  newSrc,\n  tag,\n  anchor,\n  offset,\n  comment,\n}: {\n  src: string;\n  newSrc: string;\n  tag: string;\n  anchor: string | RegExp;\n  offset: number;\n  comment: string;\n}): MergeResults {\n  const header = createGeneratedHeaderComment(newSrc, tag, comment);\n  if (!src.includes(header)) {\n    // Ensure the old generated contents are removed.\n    const sanitizedTarget = removeGeneratedContents(src, tag);\n    return {\n      contents: addLines(sanitizedTarget ?? src, anchor, offset, [\n        header,\n        ...newSrc.split('\\n'),\n        `${comment} @generated end ${tag}`,\n      ]),\n      didMerge: true,\n      didClear: !!sanitizedTarget,\n    };\n  }\n  return { contents: src, didClear: false, didMerge: false };\n}\n\nexport function removeContents({ src, tag }: { src: string; tag: string }): MergeResults {\n  // Ensure the old generated contents are removed.\n  const sanitizedTarget = removeGeneratedContents(src, tag);\n  return {\n    contents: sanitizedTarget ?? src,\n    didMerge: false,\n    didClear: !!sanitizedTarget,\n  };\n}\n\nfunction addLines(content: string, find: string | RegExp, offset: number, toAdd: string[]) {\n  const lines = content.split('\\n');\n\n  let lineIndex = lines.findIndex((line) => line.match(find));\n  if (lineIndex < 0) {\n    const error = new Error(`Failed to match \"${find}\" in contents:\\n${content}`);\n    // @ts-ignore\n    error.code = 'ERR_NO_MATCH';\n    throw error;\n  }\n  for (const newLine of toAdd) {\n    lines.splice(lineIndex + offset, 0, newLine);\n    lineIndex++;\n  }\n\n  return lines.join('\\n');\n}\n\n/**\n * Removes the generated section from a file, returns null when nothing can be removed.\n * This sways heavily towards not removing lines unless it's certain that modifications were not made manually.\n *\n * @param src\n */\nexport function removeGeneratedContents(src: string, tag: string): string | null {\n  const { contents, start, end } = getGeneratedSectionIndexes(src, tag);\n  if (start > -1 && end > -1 && start < end) {\n    contents.splice(start, end - start + 1);\n    // TODO: We could in theory check that the contents we're removing match the hash used in the header,\n    // this would ensure that we don't accidentally remove lines that someone added or removed from the generated section.\n    return contents.join('\\n');\n  }\n  return null;\n}\n\nexport function createGeneratedHeaderComment(\n  contents: string,\n  tag: string,\n  comment: string\n): string {\n  const hashKey = createHash(contents);\n\n  // Everything after the `${tag} ` is unversioned and can be freely modified without breaking changes.\n  return `${comment} @generated begin ${tag} - expo prebuild (DO NOT MODIFY) ${hashKey}`;\n}\n\nexport function createHash(src: string): string {\n  // this doesn't need to be secure, the shorter the better.\n  const hash = crypto.createHash('sha1').update(src).digest('hex');\n  return `sync-${hash}`;\n}\n"], "mappings": ";;;;;;;;;;AAKA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAC,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAL5B;AACA;AACA;AACA;AACA;;AAGA,SAASG,0BAA0BA,CACjCC,GAAW,EACXC,GAAW,EACyC;EACpD,MAAMC,QAAQ,GAAGF,GAAG,CAACG,KAAK,CAAC,IAAI,CAAC;EAChC,MAAMC,KAAK,GAAGF,QAAQ,CAACG,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAE,oBAAmBN,GAAI,EAAC,CAAC,CAAC;EACpF,MAAMO,GAAG,GAAGN,QAAQ,CAACG,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAE,kBAAiBN,GAAI,EAAC,CAAC,CAAC;EAEhF,OAAO;IAAEC,QAAQ;IAAEE,KAAK;IAAEI;EAAI,CAAC;AACjC;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAAC;EAC5BT,GAAG;EACHU,MAAM;EACNT,GAAG;EACHU,MAAM;EACNC,MAAM;EACNC;AAQF,CAAC,EAAgB;EACf,MAAMC,MAAM,GAAGC,4BAA4B,CAACL,MAAM,EAAET,GAAG,EAAEY,OAAO,CAAC;EACjE,IAAI,CAACb,GAAG,CAACO,QAAQ,CAACO,MAAM,CAAC,EAAE;IACzB;IACA,MAAME,eAAe,GAAGC,uBAAuB,CAACjB,GAAG,EAAEC,GAAG,CAAC;IACzD,OAAO;MACLC,QAAQ,EAAEgB,QAAQ,CAACF,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAIhB,GAAG,EAAEW,MAAM,EAAEC,MAAM,EAAE,CACzDE,MAAM,EACN,GAAGJ,MAAM,CAACP,KAAK,CAAC,IAAI,CAAC,EACpB,GAAEU,OAAQ,mBAAkBZ,GAAI,EAAC,CACnC,CAAC;MACFkB,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CAAC,CAACJ;IACd,CAAC;EACH;EACA,OAAO;IAAEd,QAAQ,EAAEF,GAAG;IAAEoB,QAAQ,EAAE,KAAK;IAAED,QAAQ,EAAE;EAAM,CAAC;AAC5D;AAEO,SAASE,cAAcA,CAAC;EAAErB,GAAG;EAAEC;AAAkC,CAAC,EAAgB;EACvF;EACA,MAAMe,eAAe,GAAGC,uBAAuB,CAACjB,GAAG,EAAEC,GAAG,CAAC;EACzD,OAAO;IACLC,QAAQ,EAAEc,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAIhB,GAAG;IAChCmB,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,CAAC,CAACJ;EACd,CAAC;AACH;AAEA,SAASE,QAAQA,CAACI,OAAe,EAAEC,IAAqB,EAAEX,MAAc,EAAEY,KAAe,EAAE;EACzF,MAAMC,KAAK,GAAGH,OAAO,CAACnB,KAAK,CAAC,IAAI,CAAC;EAEjC,IAAIuB,SAAS,GAAGD,KAAK,CAACpB,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACqB,KAAK,CAACJ,IAAI,CAAC,CAAC;EAC3D,IAAIG,SAAS,GAAG,CAAC,EAAE;IACjB,MAAME,KAAK,GAAG,IAAIC,KAAK,CAAE,oBAAmBN,IAAK,mBAAkBD,OAAQ,EAAC,CAAC;IAC7E;IACAM,KAAK,CAACE,IAAI,GAAG,cAAc;IAC3B,MAAMF,KAAK;EACb;EACA,KAAK,MAAMG,OAAO,IAAIP,KAAK,EAAE;IAC3BC,KAAK,CAACO,MAAM,CAACN,SAAS,GAAGd,MAAM,EAAE,CAAC,EAAEmB,OAAO,CAAC;IAC5CL,SAAS,EAAE;EACb;EAEA,OAAOD,KAAK,CAACQ,IAAI,CAAC,IAAI,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAShB,uBAAuBA,CAACjB,GAAW,EAAEC,GAAW,EAAiB;EAC/E,MAAM;IAAEC,QAAQ;IAAEE,KAAK;IAAEI;EAAI,CAAC,GAAGT,0BAA0B,CAACC,GAAG,EAAEC,GAAG,CAAC;EACrE,IAAIG,KAAK,GAAG,CAAC,CAAC,IAAII,GAAG,GAAG,CAAC,CAAC,IAAIJ,KAAK,GAAGI,GAAG,EAAE;IACzCN,QAAQ,CAAC8B,MAAM,CAAC5B,KAAK,EAAEI,GAAG,GAAGJ,KAAK,GAAG,CAAC,CAAC;IACvC;IACA;IACA,OAAOF,QAAQ,CAAC+B,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA,OAAO,IAAI;AACb;AAEO,SAASlB,4BAA4BA,CAC1Cb,QAAgB,EAChBD,GAAW,EACXY,OAAe,EACP;EACR,MAAMqB,OAAO,GAAGC,UAAU,CAACjC,QAAQ,CAAC;;EAEpC;EACA,OAAQ,GAAEW,OAAQ,qBAAoBZ,GAAI,oCAAmCiC,OAAQ,EAAC;AACxF;AAEO,SAASC,UAAUA,CAACnC,GAAW,EAAU;EAC9C;EACA,MAAMoC,IAAI,GAAGC,iBAAM,CAACF,UAAU,CAAC,MAAM,CAAC,CAACG,MAAM,CAACtC,GAAG,CAAC,CAACuC,MAAM,CAAC,KAAK,CAAC;EAChE,OAAQ,QAAOH,IAAK,EAAC;AACvB"}