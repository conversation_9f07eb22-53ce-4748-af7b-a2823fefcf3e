{"version": 3, "file": "sortObject.js", "names": ["sortObject", "obj", "compareFn", "Object", "keys", "sort", "reduce", "acc", "key", "sortObjWithOrder", "order", "sorted", "sortWithOrder", "groupOrder", "Set", "concat", "length", "shift", "index", "indexOf", "item", "splice", "push", "reverseSortString", "a", "b", "exports"], "sources": ["../../src/utils/sortObject.ts"], "sourcesContent": ["export function sortObject<T extends Record<string, any> = Record<string, any>>(\n  obj: T,\n  compareFn?: (a: string, b: string) => number\n): T {\n  return Object.keys(obj)\n    .sort(compareFn)\n    .reduce(\n      (acc, key) => ({\n        ...acc,\n        [key]: obj[key],\n      }),\n      {}\n    ) as T;\n}\n\nexport function sortObjWithOrder<T extends Record<string, any> = Record<string, any>>(\n  obj: T,\n  order: string[]\n): T {\n  const sorted = sortWithOrder(Object.keys(obj), order);\n\n  return sorted.reduce(\n    (acc, key) => ({\n      ...acc,\n      [key]: obj[key],\n    }),\n    {}\n  ) as T;\n}\n\nexport function sortWithOrder(obj: string[], order: string[]): string[] {\n  const groupOrder = [...new Set(order.concat(obj))];\n  const sorted: string[] = [];\n\n  while (groupOrder.length) {\n    const key = groupOrder.shift()!;\n    const index = obj.indexOf(key);\n    if (index > -1) {\n      const [item] = obj.splice(index, 1);\n      sorted.push(item);\n    }\n  }\n\n  return sorted;\n}\n\nexport const reverseSortString = (a: string, b: string) => {\n  if (a < b) return 1;\n  if (a > b) return -1;\n  return 0;\n};\n"], "mappings": ";;;;;;;;;AAAO,SAASA,UAAUA,CACxBC,GAAM,EACNC,SAA4C,EACzC;EACH,OAAOC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CACpBI,IAAI,CAACH,SAAS,CAAC,CACfI,MAAM,CACL,CAACC,GAAG,EAAEC,GAAG,MAAM;IACb,GAAGD,GAAG;IACN,CAACC,GAAG,GAAGP,GAAG,CAACO,GAAG;EAChB,CAAC,CAAC,EACF,CAAC,CAAC,CACH;AACL;AAEO,SAASC,gBAAgBA,CAC9BR,GAAM,EACNS,KAAe,EACZ;EACH,MAAMC,MAAM,GAAGC,aAAa,CAACT,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,EAAES,KAAK,CAAC;EAErD,OAAOC,MAAM,CAACL,MAAM,CAClB,CAACC,GAAG,EAAEC,GAAG,MAAM;IACb,GAAGD,GAAG;IACN,CAACC,GAAG,GAAGP,GAAG,CAACO,GAAG;EAChB,CAAC,CAAC,EACF,CAAC,CAAC,CACH;AACH;AAEO,SAASI,aAAaA,CAACX,GAAa,EAAES,KAAe,EAAY;EACtE,MAAMG,UAAU,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACJ,KAAK,CAACK,MAAM,CAACd,GAAG,CAAC,CAAC,CAAC;EAClD,MAAMU,MAAgB,GAAG,EAAE;EAE3B,OAAOE,UAAU,CAACG,MAAM,EAAE;IACxB,MAAMR,GAAG,GAAGK,UAAU,CAACI,KAAK,EAAG;IAC/B,MAAMC,KAAK,GAAGjB,GAAG,CAACkB,OAAO,CAACX,GAAG,CAAC;IAC9B,IAAIU,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,MAAM,CAACE,IAAI,CAAC,GAAGnB,GAAG,CAACoB,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MACnCP,MAAM,CAACW,IAAI,CAACF,IAAI,CAAC;IACnB;EACF;EAEA,OAAOT,MAAM;AACf;AAEO,MAAMY,iBAAiB,GAAGA,CAACC,CAAS,EAAEC,CAAS,KAAK;EACzD,IAAID,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC;EACnB,IAAID,CAAC,GAAGC,CAAC,EAAE,OAAO,CAAC,CAAC;EACpB,OAAO,CAAC;AACV,CAAC;AAACC,OAAA,CAAAH,iBAAA,GAAAA,iBAAA"}