{"version": 3, "file": "matchBrackets.js", "names": ["LEFT_BRACKETS", "RIGHT_BRACKETS", "findMatchingBracketPosition", "contents", "bracket", "offset", "firstBracketPos", "indexOf", "stackCounter", "matchingBracket", "getMatchingBracket", "isLeftBracket", "contents<PERSON>ength", "length", "i", "c", "leftBracketList", "includes", "Error"], "sources": ["../../src/utils/matchBrackets.ts"], "sourcesContent": ["const LEFT_BRACKETS = ['(', '{'] as const;\nconst RIGHT_BRACKETS = [')', '}'] as const;\n\ntype LeftBracket = typeof LEFT_BRACKETS[number];\ntype RightBracket = typeof RIGHT_BRACKETS[number];\ntype Bracket = LeftBracket | RightBracket;\n\nexport function findMatchingBracketPosition(\n  contents: string,\n  bracket: Bracket,\n  offset: number = 0\n): number {\n  // search first occurrence of `bracket`\n  const firstBracketPos = contents.indexOf(bracket, offset);\n  if (firstBracketPos < 0) {\n    return -1;\n  }\n\n  let stackCounter = 0;\n  const matchingBracket = getMatchingBracket(bracket);\n\n  if (isLeftBracket(bracket)) {\n    const contentsLength = contents.length;\n    // search forward\n    for (let i = firstBracketPos + 1; i < contentsLength; ++i) {\n      const c = contents[i];\n      if (c === bracket) {\n        stackCounter += 1;\n      } else if (c === matchingBracket) {\n        if (stackCounter === 0) {\n          return i;\n        }\n        stackCounter -= 1;\n      }\n    }\n  } else {\n    // search backward\n    for (let i = firstBracketPos - 1; i >= 0; --i) {\n      const c = contents[i];\n      if (c === bracket) {\n        stackCounter += 1;\n      } else if (c === matchingBracket) {\n        if (stackCounter === 0) {\n          return i;\n        }\n        stackCounter -= 1;\n      }\n    }\n  }\n\n  return -1;\n}\n\nfunction isLeftBracket(bracket: Bracket): boolean {\n  const leftBracketList: readonly Bracket[] = LEFT_BRACKETS;\n  return leftBracketList.includes(bracket);\n}\n\nfunction getMatchingBracket(bracket: Bracket): Bracket {\n  switch (bracket) {\n    case '(':\n      return ')';\n    case ')':\n      return '(';\n    case '{':\n      return '}';\n    case '}':\n      return '{';\n    default:\n      throw new Error(`Unsupported bracket - ${bracket}`);\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,CAAU;AACzC,MAAMC,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,CAAU;AAMnC,SAASC,2BAA2BA,CACzCC,QAAgB,EAChBC,OAAgB,EAChBC,MAAc,GAAG,CAAC,EACV;EACR;EACA,MAAMC,eAAe,GAAGH,QAAQ,CAACI,OAAO,CAACH,OAAO,EAAEC,MAAM,CAAC;EACzD,IAAIC,eAAe,GAAG,CAAC,EAAE;IACvB,OAAO,CAAC,CAAC;EACX;EAEA,IAAIE,YAAY,GAAG,CAAC;EACpB,MAAMC,eAAe,GAAGC,kBAAkB,CAACN,OAAO,CAAC;EAEnD,IAAIO,aAAa,CAACP,OAAO,CAAC,EAAE;IAC1B,MAAMQ,cAAc,GAAGT,QAAQ,CAACU,MAAM;IACtC;IACA,KAAK,IAAIC,CAAC,GAAGR,eAAe,GAAG,CAAC,EAAEQ,CAAC,GAAGF,cAAc,EAAE,EAAEE,CAAC,EAAE;MACzD,MAAMC,CAAC,GAAGZ,QAAQ,CAACW,CAAC,CAAC;MACrB,IAAIC,CAAC,KAAKX,OAAO,EAAE;QACjBI,YAAY,IAAI,CAAC;MACnB,CAAC,MAAM,IAAIO,CAAC,KAAKN,eAAe,EAAE;QAChC,IAAID,YAAY,KAAK,CAAC,EAAE;UACtB,OAAOM,CAAC;QACV;QACAN,YAAY,IAAI,CAAC;MACnB;IACF;EACF,CAAC,MAAM;IACL;IACA,KAAK,IAAIM,CAAC,GAAGR,eAAe,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC7C,MAAMC,CAAC,GAAGZ,QAAQ,CAACW,CAAC,CAAC;MACrB,IAAIC,CAAC,KAAKX,OAAO,EAAE;QACjBI,YAAY,IAAI,CAAC;MACnB,CAAC,MAAM,IAAIO,CAAC,KAAKN,eAAe,EAAE;QAChC,IAAID,YAAY,KAAK,CAAC,EAAE;UACtB,OAAOM,CAAC;QACV;QACAN,YAAY,IAAI,CAAC;MACnB;IACF;EACF;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAASG,aAAaA,CAACP,OAAgB,EAAW;EAChD,MAAMY,eAAmC,GAAGhB,aAAa;EACzD,OAAOgB,eAAe,CAACC,QAAQ,CAACb,OAAO,CAAC;AAC1C;AAEA,SAASM,kBAAkBA,CAACN,OAAgB,EAAW;EACrD,QAAQA,OAAO;IACb,KAAK,GAAG;MACN,OAAO,GAAG;IACZ,KAAK,GAAG;MACN,OAAO,GAAG;IACZ,KAAK,GAAG;MACN,OAAO,GAAG;IACZ,KAAK,GAAG;MACN,OAAO,GAAG;IACZ;MACE,MAAM,IAAIc,KAAK,CAAE,yBAAwBd,OAAQ,EAAC,CAAC;EAAC;AAE1D"}