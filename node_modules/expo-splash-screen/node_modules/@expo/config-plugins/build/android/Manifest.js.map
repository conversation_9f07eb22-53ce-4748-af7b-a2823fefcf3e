{"version": 3, "file": "Manifest.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_fs", "_path", "XML", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "writeAndroidManifestAsync", "manifestPath", "androidManifest", "manifestXml", "format", "fs", "promises", "mkdir", "path", "dirname", "recursive", "writeFile", "readAndroidManifestAsync", "xml", "readXMLAsync", "isManifest", "Error", "manifest", "getMainApplication", "_androidManifest$mani", "_androidManifest$mani2", "_androidManifest$mani3", "application", "filter", "e", "_e$$", "$", "endsWith", "getMainApplicationOrThrow", "mainApplication", "assert", "getMainActivityOrThrow", "mainActivity", "getMainActivity", "getRunnableActivity", "_androidManifest$mani4", "_androidManifest$mani5", "_androidManifest$mani6", "_androidManifest$mani7", "_androidManifest$mani8", "enabledActivities", "activity", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "_intentFilter$action", "_intentFilter$categor", "action", "find", "category", "_androidManifest$mani9", "_androidManifest$mani10", "_androidManifest$mani11", "_androidManifest$mani12", "_androidManifest$mani13", "_mainActivity$", "addMetaDataItemToMainApplication", "itemName", "itemValue", "itemType", "existingMetaDataItem", "newItem", "prefixAndroidKeys", "name", "length", "push", "removeMetaDataItemFromMainApplication", "index", "findMetaDataItem", "splice", "findApplicationSubItem", "parent", "findIndex", "findUsesLibraryItem", "getMainApplicationMetaDataValue", "_mainApplication$meta", "_item$$$androidValue", "item", "addUsesLibraryItemToMainApplication", "removeUsesLibraryItemFromMainApplication", "head", "entries", "reduce", "prev", "curr", "ensureToolsAvailable", "ensureManifestHasNamespace", "namespace", "url", "_manifest$manifest", "_manifest$manifest$$"], "sources": ["../../src/android/Manifest.ts"], "sourcesContent": ["import assert from 'assert';\nimport fs from 'fs';\nimport path from 'path';\n\nimport * as XML from '../utils/XML';\n\nexport type StringBoolean = 'true' | 'false';\n\ntype ManifestMetaDataAttributes = AndroidManifestAttributes & {\n  'android:value'?: string;\n  'android:resource'?: string;\n};\n\ntype AndroidManifestAttributes = {\n  'android:name': string | 'android.intent.action.VIEW';\n  'tools:node'?: string | 'remove';\n};\n\ntype ManifestAction = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestCategory = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestData = {\n  $: {\n    [key: string]: string | undefined;\n    'android:host'?: string;\n    'android:pathPrefix'?: string;\n    'android:scheme'?: string;\n  };\n};\n\ntype ManifestReceiver = {\n  $: AndroidManifestAttributes & {\n    'android:exported'?: StringBoolean;\n    'android:enabled'?: StringBoolean;\n  };\n  'intent-filter'?: ManifestIntentFilter[];\n};\n\nexport type ManifestIntentFilter = {\n  $?: {\n    'android:autoVerify'?: StringBoolean;\n    'data-generated'?: StringBoolean;\n  };\n  action?: ManifestAction[];\n  data?: ManifestData[];\n  category?: ManifestCategory[];\n};\n\nexport type ManifestMetaData = {\n  $: ManifestMetaDataAttributes;\n};\n\ntype ManifestServiceAttributes = AndroidManifestAttributes & {\n  'android:enabled'?: StringBoolean;\n  'android:exported'?: StringBoolean;\n  'android:permission'?: string;\n  // ...\n};\n\ntype ManifestService = {\n  $: ManifestServiceAttributes;\n  'intent-filter'?: ManifestIntentFilter[];\n};\n\ntype ManifestApplicationAttributes = {\n  'android:name': string | '.MainApplication';\n  'android:icon'?: string;\n  'android:roundIcon'?: string;\n  'android:label'?: string;\n  'android:allowBackup'?: StringBoolean;\n  'android:largeHeap'?: StringBoolean;\n  'android:requestLegacyExternalStorage'?: StringBoolean;\n  'android:usesCleartextTraffic'?: StringBoolean;\n  [key: string]: string | undefined;\n};\n\nexport type ManifestActivity = {\n  $: ManifestApplicationAttributes & {\n    'android:exported'?: StringBoolean;\n    'android:launchMode'?: string;\n    'android:theme'?: string;\n    'android:windowSoftInputMode'?:\n      | string\n      | 'stateUnspecified'\n      | 'stateUnchanged'\n      | 'stateHidden'\n      | 'stateAlwaysHidden'\n      | 'stateVisible'\n      | 'stateAlwaysVisible'\n      | 'adjustUnspecified'\n      | 'adjustResize'\n      | 'adjustPan';\n    [key: string]: string | undefined;\n  };\n  'intent-filter'?: ManifestIntentFilter[];\n  // ...\n};\n\nexport type ManifestUsesLibrary = {\n  $: AndroidManifestAttributes & {\n    'android:required'?: StringBoolean;\n  };\n};\n\nexport type ManifestApplication = {\n  $: ManifestApplicationAttributes;\n  activity?: ManifestActivity[];\n  service?: ManifestService[];\n  receiver?: ManifestReceiver[];\n  'meta-data'?: ManifestMetaData[];\n  'uses-library'?: ManifestUsesLibrary[];\n  // ...\n};\n\ntype ManifestPermission = {\n  $: AndroidManifestAttributes & {\n    'android:protectionLevel'?: string | 'signature';\n  };\n};\n\nexport type ManifestUsesPermission = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestUsesFeature = {\n  $: AndroidManifestAttributes & {\n    'android:glEsVersion'?: string;\n    'android:required': StringBoolean;\n  };\n};\n\nexport type AndroidManifest = {\n  manifest: {\n    // Probably more, but this is currently all we'd need for most cases in Expo.\n    $: {\n      'xmlns:android': string;\n      'xmlns:tools'?: string;\n      package?: string;\n      [key: string]: string | undefined;\n    };\n    permission?: ManifestPermission[];\n    'uses-permission'?: ManifestUsesPermission[];\n    'uses-permission-sdk-23'?: ManifestUsesPermission[];\n    'uses-feature'?: ManifestUsesFeature[];\n    application?: ManifestApplication[];\n  };\n};\n\nexport async function writeAndroidManifestAsync(\n  manifestPath: string,\n  androidManifest: AndroidManifest\n): Promise<void> {\n  const manifestXml = XML.format(androidManifest);\n  await fs.promises.mkdir(path.dirname(manifestPath), { recursive: true });\n  await fs.promises.writeFile(manifestPath, manifestXml);\n}\n\nexport async function readAndroidManifestAsync(manifestPath: string): Promise<AndroidManifest> {\n  const xml = await XML.readXMLAsync({ path: manifestPath });\n  if (!isManifest(xml)) {\n    throw new Error('Invalid manifest found at: ' + manifestPath);\n  }\n  return xml;\n}\n\nfunction isManifest(xml: XML.XMLObject): xml is AndroidManifest {\n  // TODO: Maybe more validation\n  return !!xml.manifest;\n}\n\n/** Returns the `manifest.application` tag ending in `.MainApplication` */\nexport function getMainApplication(androidManifest: AndroidManifest): ManifestApplication | null {\n  return (\n    androidManifest?.manifest?.application?.filter((e) =>\n      e?.$?.['android:name'].endsWith('.MainApplication')\n    )[0] ?? null\n  );\n}\n\nexport function getMainApplicationOrThrow(androidManifest: AndroidManifest): ManifestApplication {\n  const mainApplication = getMainApplication(androidManifest);\n  assert(mainApplication, 'AndroidManifest.xml is missing the required MainApplication element');\n  return mainApplication;\n}\n\nexport function getMainActivityOrThrow(androidManifest: AndroidManifest): ManifestActivity {\n  const mainActivity = getMainActivity(androidManifest);\n  assert(mainActivity, 'AndroidManifest.xml is missing the required MainActivity element');\n  return mainActivity;\n}\n\nexport function getRunnableActivity(androidManifest: AndroidManifest): ManifestActivity | null {\n  // Get enabled activities\n  const enabledActivities = androidManifest?.manifest?.application?.[0]?.activity?.filter?.(\n    (e: any) => e.$['android:enabled'] !== 'false' && e.$['android:enabled'] !== false\n  );\n\n  if (!enabledActivities) {\n    return null;\n  }\n\n  // Get the activity that has a runnable intent-filter\n  for (const activity of enabledActivities) {\n    if (Array.isArray(activity['intent-filter'])) {\n      for (const intentFilter of activity['intent-filter']) {\n        if (\n          intentFilter.action?.find(\n            (action) => action.$['android:name'] === 'android.intent.action.MAIN'\n          ) &&\n          intentFilter.category?.find(\n            (category) => category.$['android:name'] === 'android.intent.category.LAUNCHER'\n          )\n        ) {\n          return activity;\n        }\n      }\n    }\n  }\n\n  return null;\n}\n\nexport function getMainActivity(androidManifest: AndroidManifest): ManifestActivity | null {\n  const mainActivity = androidManifest?.manifest?.application?.[0]?.activity?.filter?.(\n    (e: any) => e.$['android:name'] === '.MainActivity'\n  );\n  return mainActivity?.[0] ?? null;\n}\n\nexport function addMetaDataItemToMainApplication(\n  mainApplication: ManifestApplication,\n  itemName: string,\n  itemValue: string,\n  itemType: 'resource' | 'value' = 'value'\n): ManifestApplication {\n  let existingMetaDataItem;\n  const newItem = {\n    $: prefixAndroidKeys({ name: itemName, [itemType]: itemValue }),\n  } as ManifestMetaData;\n  if (mainApplication['meta-data']) {\n    existingMetaDataItem = mainApplication['meta-data'].filter(\n      (e: any) => e.$['android:name'] === itemName\n    );\n    if (existingMetaDataItem.length) {\n      existingMetaDataItem[0].$[`android:${itemType}` as keyof ManifestMetaDataAttributes] =\n        itemValue;\n    } else {\n      mainApplication['meta-data'].push(newItem);\n    }\n  } else {\n    mainApplication['meta-data'] = [newItem];\n  }\n  return mainApplication;\n}\n\nexport function removeMetaDataItemFromMainApplication(mainApplication: any, itemName: string) {\n  const index = findMetaDataItem(mainApplication, itemName);\n  if (mainApplication?.['meta-data'] && index > -1) {\n    mainApplication['meta-data'].splice(index, 1);\n  }\n  return mainApplication;\n}\n\nfunction findApplicationSubItem(\n  mainApplication: ManifestApplication,\n  category: keyof ManifestApplication,\n  itemName: string\n): number {\n  const parent = mainApplication[category];\n  if (Array.isArray(parent)) {\n    const index = parent.findIndex((e: any) => e.$['android:name'] === itemName);\n\n    return index;\n  }\n  return -1;\n}\n\nexport function findMetaDataItem(mainApplication: any, itemName: string): number {\n  return findApplicationSubItem(mainApplication, 'meta-data', itemName);\n}\n\nexport function findUsesLibraryItem(mainApplication: any, itemName: string): number {\n  return findApplicationSubItem(mainApplication, 'uses-library', itemName);\n}\n\nexport function getMainApplicationMetaDataValue(\n  androidManifest: AndroidManifest,\n  name: string\n): string | null {\n  const mainApplication = getMainApplication(androidManifest);\n\n  if (mainApplication?.hasOwnProperty('meta-data')) {\n    const item = mainApplication?.['meta-data']?.find((e: any) => e.$['android:name'] === name);\n    return item?.$['android:value'] ?? null;\n  }\n\n  return null;\n}\n\nexport function addUsesLibraryItemToMainApplication(\n  mainApplication: ManifestApplication,\n  item: { name: string; required?: boolean }\n): ManifestApplication {\n  let existingMetaDataItem;\n  const newItem = {\n    $: prefixAndroidKeys(item),\n  } as ManifestUsesLibrary;\n\n  if (mainApplication['uses-library']) {\n    existingMetaDataItem = mainApplication['uses-library'].filter(\n      (e) => e.$['android:name'] === item.name\n    );\n    if (existingMetaDataItem.length) {\n      existingMetaDataItem[0].$ = newItem.$;\n    } else {\n      mainApplication['uses-library'].push(newItem);\n    }\n  } else {\n    mainApplication['uses-library'] = [newItem];\n  }\n  return mainApplication;\n}\n\nexport function removeUsesLibraryItemFromMainApplication(\n  mainApplication: ManifestApplication,\n  itemName: string\n) {\n  const index = findUsesLibraryItem(mainApplication, itemName);\n  if (mainApplication?.['uses-library'] && index > -1) {\n    mainApplication['uses-library'].splice(index, 1);\n  }\n  return mainApplication;\n}\n\nexport function prefixAndroidKeys<T extends Record<string, any> = Record<string, string>>(\n  head: T\n): Record<string, any> {\n  // prefix all keys with `android:`\n  return Object.entries(head).reduce(\n    (prev, [key, curr]) => ({ ...prev, [`android:${key}`]: curr }),\n    {} as T\n  );\n}\n\n/**\n * Ensure the `tools:*` namespace is available in the manifest.\n *\n * @param manifest AndroidManifest.xml\n * @returns manifest with the `tools:*` namespace available\n */\nexport function ensureToolsAvailable(manifest: AndroidManifest) {\n  return ensureManifestHasNamespace(manifest, {\n    namespace: 'xmlns:tools',\n    url: 'http://schemas.android.com/tools',\n  });\n}\n\n/**\n * Ensure a particular namespace is available in the manifest.\n *\n * @param manifest `AndroidManifest.xml`\n * @returns manifest with the provided namespace available\n */\nfunction ensureManifestHasNamespace(\n  manifest: AndroidManifest,\n  { namespace, url }: { namespace: string; url: string }\n) {\n  if (manifest?.manifest?.$?.[namespace]) {\n    return manifest;\n  }\n  manifest.manifest.$[namespace] = url;\n  return manifest;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,IAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,GAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoC,SAAAO,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAjB,uBAAAW,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAqJ7B,eAAeiB,yBAAyBA,CAC7CC,YAAoB,EACpBC,eAAgC,EACjB;EACf,MAAMC,WAAW,GAAG3B,GAAG,GAAC4B,MAAM,CAACF,eAAe,CAAC;EAC/C,MAAMG,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACC,eAAI,CAACC,OAAO,CAACR,YAAY,CAAC,EAAE;IAAES,SAAS,EAAE;EAAK,CAAC,CAAC;EACxE,MAAML,aAAE,CAACC,QAAQ,CAACK,SAAS,CAACV,YAAY,EAAEE,WAAW,CAAC;AACxD;AAEO,eAAeS,wBAAwBA,CAACX,YAAoB,EAA4B;EAC7F,MAAMY,GAAG,GAAG,MAAMrC,GAAG,GAACsC,YAAY,CAAC;IAAEN,IAAI,EAAEP;EAAa,CAAC,CAAC;EAC1D,IAAI,CAACc,UAAU,CAACF,GAAG,CAAC,EAAE;IACpB,MAAM,IAAIG,KAAK,CAAC,6BAA6B,GAAGf,YAAY,CAAC;EAC/D;EACA,OAAOY,GAAG;AACZ;AAEA,SAASE,UAAUA,CAACF,GAAkB,EAA0B;EAC9D;EACA,OAAO,CAAC,CAACA,GAAG,CAACI,QAAQ;AACvB;;AAEA;AACO,SAASC,kBAAkBA,CAAChB,eAAgC,EAA8B;EAAA,IAAAiB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC/F,QAAAF,qBAAA,GACEjB,eAAe,aAAfA,eAAe,wBAAAkB,sBAAA,GAAflB,eAAe,CAAEe,QAAQ,cAAAG,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BE,WAAW,cAAAD,sBAAA,uBAAtCA,sBAAA,CAAwCE,MAAM,CAAEC,CAAC;IAAA,IAAAC,IAAA;IAAA,OAC/CD,CAAC,aAADA,CAAC,wBAAAC,IAAA,GAADD,CAAC,CAAEE,CAAC,cAAAD,IAAA,uBAAJA,IAAA,CAAO,cAAc,CAAC,CAACE,QAAQ,CAAC,kBAAkB,CAAC;EAAA,EACpD,CAAC,CAAC,CAAC,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AAEhB;AAEO,SAASS,yBAAyBA,CAAC1B,eAAgC,EAAuB;EAC/F,MAAM2B,eAAe,GAAGX,kBAAkB,CAAChB,eAAe,CAAC;EAC3D,IAAA4B,iBAAM,EAACD,eAAe,EAAE,qEAAqE,CAAC;EAC9F,OAAOA,eAAe;AACxB;AAEO,SAASE,sBAAsBA,CAAC7B,eAAgC,EAAoB;EACzF,MAAM8B,YAAY,GAAGC,eAAe,CAAC/B,eAAe,CAAC;EACrD,IAAA4B,iBAAM,EAACE,YAAY,EAAE,kEAAkE,CAAC;EACxF,OAAOA,YAAY;AACrB;AAEO,SAASE,mBAAmBA,CAAChC,eAAgC,EAA2B;EAAA,IAAAiC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC7F;EACA,MAAMC,iBAAiB,GAAGtC,eAAe,aAAfA,eAAe,wBAAAiC,sBAAA,GAAfjC,eAAe,CAAEe,QAAQ,cAAAkB,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2Bb,WAAW,cAAAc,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAyC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA3CD,sBAAA,CAA6CI,QAAQ,cAAAH,sBAAA,wBAAAC,sBAAA,GAArDD,sBAAA,CAAuDf,MAAM,cAAAgB,sBAAA,uBAA7DA,sBAAA,CAAA1C,IAAA,CAAAyC,sBAAA,EACvBd,CAAM,IAAKA,CAAC,CAACE,CAAC,CAAC,iBAAiB,CAAC,KAAK,OAAO,IAAIF,CAAC,CAACE,CAAC,CAAC,iBAAiB,CAAC,KAAK,KAAK,CACnF;EAED,IAAI,CAACc,iBAAiB,EAAE;IACtB,OAAO,IAAI;EACb;;EAEA;EACA,KAAK,MAAMC,QAAQ,IAAID,iBAAiB,EAAE;IACxC,IAAIE,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,eAAe,CAAC,CAAC,EAAE;MAC5C,KAAK,MAAMG,YAAY,IAAIH,QAAQ,CAAC,eAAe,CAAC,EAAE;QAAA,IAAAI,oBAAA,EAAAC,qBAAA;QACpD,IACE,CAAAD,oBAAA,GAAAD,YAAY,CAACG,MAAM,cAAAF,oBAAA,eAAnBA,oBAAA,CAAqBG,IAAI,CACtBD,MAAM,IAAKA,MAAM,CAACrB,CAAC,CAAC,cAAc,CAAC,KAAK,4BAA4B,CACtE,KAAAoB,qBAAA,GACDF,YAAY,CAACK,QAAQ,cAAAH,qBAAA,eAArBA,qBAAA,CAAuBE,IAAI,CACxBC,QAAQ,IAAKA,QAAQ,CAACvB,CAAC,CAAC,cAAc,CAAC,KAAK,kCAAkC,CAChF,EACD;UACA,OAAOe,QAAQ;QACjB;MACF;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAEO,SAASR,eAAeA,CAAC/B,eAAgC,EAA2B;EAAA,IAAAgD,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,cAAA;EACzF,MAAMvB,YAAY,GAAG9B,eAAe,aAAfA,eAAe,wBAAAgD,sBAAA,GAAfhD,eAAe,CAAEe,QAAQ,cAAAiC,sBAAA,wBAAAC,uBAAA,GAAzBD,sBAAA,CAA2B5B,WAAW,cAAA6B,uBAAA,wBAAAC,uBAAA,GAAtCD,uBAAA,CAAyC,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA3CD,uBAAA,CAA6CX,QAAQ,cAAAY,uBAAA,wBAAAC,uBAAA,GAArDD,uBAAA,CAAuD9B,MAAM,cAAA+B,uBAAA,uBAA7DA,uBAAA,CAAAzD,IAAA,CAAAwD,uBAAA,EAClB7B,CAAM,IAAKA,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,KAAK,eAAe,CACpD;EACD,QAAA6B,cAAA,GAAOvB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAG,CAAC,CAAC,cAAAuB,cAAA,cAAAA,cAAA,GAAI,IAAI;AAClC;AAEO,SAASC,gCAAgCA,CAC9C3B,eAAoC,EACpC4B,QAAgB,EAChBC,SAAiB,EACjBC,QAA8B,GAAG,OAAO,EACnB;EACrB,IAAIC,oBAAoB;EACxB,MAAMC,OAAO,GAAG;IACdnC,CAAC,EAAEoC,iBAAiB,CAAC;MAAEC,IAAI,EAAEN,QAAQ;MAAE,CAACE,QAAQ,GAAGD;IAAU,CAAC;EAChE,CAAqB;EACrB,IAAI7B,eAAe,CAAC,WAAW,CAAC,EAAE;IAChC+B,oBAAoB,GAAG/B,eAAe,CAAC,WAAW,CAAC,CAACN,MAAM,CACvDC,CAAM,IAAKA,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,KAAK+B,QAAQ,CAC7C;IACD,IAAIG,oBAAoB,CAACI,MAAM,EAAE;MAC/BJ,oBAAoB,CAAC,CAAC,CAAC,CAAClC,CAAC,CAAE,WAAUiC,QAAS,EAAC,CAAqC,GAClFD,SAAS;IACb,CAAC,MAAM;MACL7B,eAAe,CAAC,WAAW,CAAC,CAACoC,IAAI,CAACJ,OAAO,CAAC;IAC5C;EACF,CAAC,MAAM;IACLhC,eAAe,CAAC,WAAW,CAAC,GAAG,CAACgC,OAAO,CAAC;EAC1C;EACA,OAAOhC,eAAe;AACxB;AAEO,SAASqC,qCAAqCA,CAACrC,eAAoB,EAAE4B,QAAgB,EAAE;EAC5F,MAAMU,KAAK,GAAGC,gBAAgB,CAACvC,eAAe,EAAE4B,QAAQ,CAAC;EACzD,IAAI5B,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAG,WAAW,CAAC,IAAIsC,KAAK,GAAG,CAAC,CAAC,EAAE;IAChDtC,eAAe,CAAC,WAAW,CAAC,CAACwC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAC/C;EACA,OAAOtC,eAAe;AACxB;AAEA,SAASyC,sBAAsBA,CAC7BzC,eAAoC,EACpCoB,QAAmC,EACnCQ,QAAgB,EACR;EACR,MAAMc,MAAM,GAAG1C,eAAe,CAACoB,QAAQ,CAAC;EACxC,IAAIP,KAAK,CAACC,OAAO,CAAC4B,MAAM,CAAC,EAAE;IACzB,MAAMJ,KAAK,GAAGI,MAAM,CAACC,SAAS,CAAEhD,CAAM,IAAKA,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,KAAK+B,QAAQ,CAAC;IAE5E,OAAOU,KAAK;EACd;EACA,OAAO,CAAC,CAAC;AACX;AAEO,SAASC,gBAAgBA,CAACvC,eAAoB,EAAE4B,QAAgB,EAAU;EAC/E,OAAOa,sBAAsB,CAACzC,eAAe,EAAE,WAAW,EAAE4B,QAAQ,CAAC;AACvE;AAEO,SAASgB,mBAAmBA,CAAC5C,eAAoB,EAAE4B,QAAgB,EAAU;EAClF,OAAOa,sBAAsB,CAACzC,eAAe,EAAE,cAAc,EAAE4B,QAAQ,CAAC;AAC1E;AAEO,SAASiB,+BAA+BA,CAC7CxE,eAAgC,EAChC6D,IAAY,EACG;EACf,MAAMlC,eAAe,GAAGX,kBAAkB,CAAChB,eAAe,CAAC;EAE3D,IAAI2B,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEjC,cAAc,CAAC,WAAW,CAAC,EAAE;IAAA,IAAA+E,qBAAA,EAAAC,oBAAA;IAChD,MAAMC,IAAI,GAAGhD,eAAe,aAAfA,eAAe,wBAAA8C,qBAAA,GAAf9C,eAAe,CAAG,WAAW,CAAC,cAAA8C,qBAAA,uBAA9BA,qBAAA,CAAgC3B,IAAI,CAAExB,CAAM,IAAKA,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,KAAKqC,IAAI,CAAC;IAC3F,QAAAa,oBAAA,GAAOC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEnD,CAAC,CAAC,eAAe,CAAC,cAAAkD,oBAAA,cAAAA,oBAAA,GAAI,IAAI;EACzC;EAEA,OAAO,IAAI;AACb;AAEO,SAASE,mCAAmCA,CACjDjD,eAAoC,EACpCgD,IAA0C,EACrB;EACrB,IAAIjB,oBAAoB;EACxB,MAAMC,OAAO,GAAG;IACdnC,CAAC,EAAEoC,iBAAiB,CAACe,IAAI;EAC3B,CAAwB;EAExB,IAAIhD,eAAe,CAAC,cAAc,CAAC,EAAE;IACnC+B,oBAAoB,GAAG/B,eAAe,CAAC,cAAc,CAAC,CAACN,MAAM,CAC1DC,CAAC,IAAKA,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,KAAKmD,IAAI,CAACd,IAAI,CACzC;IACD,IAAIH,oBAAoB,CAACI,MAAM,EAAE;MAC/BJ,oBAAoB,CAAC,CAAC,CAAC,CAAClC,CAAC,GAAGmC,OAAO,CAACnC,CAAC;IACvC,CAAC,MAAM;MACLG,eAAe,CAAC,cAAc,CAAC,CAACoC,IAAI,CAACJ,OAAO,CAAC;IAC/C;EACF,CAAC,MAAM;IACLhC,eAAe,CAAC,cAAc,CAAC,GAAG,CAACgC,OAAO,CAAC;EAC7C;EACA,OAAOhC,eAAe;AACxB;AAEO,SAASkD,wCAAwCA,CACtDlD,eAAoC,EACpC4B,QAAgB,EAChB;EACA,MAAMU,KAAK,GAAGM,mBAAmB,CAAC5C,eAAe,EAAE4B,QAAQ,CAAC;EAC5D,IAAI5B,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAG,cAAc,CAAC,IAAIsC,KAAK,GAAG,CAAC,CAAC,EAAE;IACnDtC,eAAe,CAAC,cAAc,CAAC,CAACwC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAClD;EACA,OAAOtC,eAAe;AACxB;AAEO,SAASiC,iBAAiBA,CAC/BkB,IAAO,EACc;EACrB;EACA,OAAOzF,MAAM,CAAC0F,OAAO,CAACD,IAAI,CAAC,CAACE,MAAM,CAChC,CAACC,IAAI,EAAE,CAACzF,GAAG,EAAE0F,IAAI,CAAC,MAAM;IAAE,GAAGD,IAAI;IAAE,CAAE,WAAUzF,GAAI,EAAC,GAAG0F;EAAK,CAAC,CAAC,EAC9D,CAAC,CAAC,CACH;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,oBAAoBA,CAACpE,QAAyB,EAAE;EAC9D,OAAOqE,0BAA0B,CAACrE,QAAQ,EAAE;IAC1CsE,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,0BAA0BA,CACjCrE,QAAyB,EACzB;EAAEsE,SAAS;EAAEC;AAAwC,CAAC,EACtD;EAAA,IAAAC,kBAAA,EAAAC,oBAAA;EACA,IAAIzE,QAAQ,aAARA,QAAQ,gBAAAwE,kBAAA,GAARxE,QAAQ,CAAEA,QAAQ,cAAAwE,kBAAA,gBAAAC,oBAAA,GAAlBD,kBAAA,CAAoB/D,CAAC,cAAAgE,oBAAA,eAArBA,oBAAA,CAAwBH,SAAS,CAAC,EAAE;IACtC,OAAOtE,QAAQ;EACjB;EACAA,QAAQ,CAACA,QAAQ,CAACS,CAAC,CAAC6D,SAAS,CAAC,GAAGC,GAAG;EACpC,OAAOvE,QAAQ;AACjB"}