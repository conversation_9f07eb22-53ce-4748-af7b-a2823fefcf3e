{"version": 3, "file": "Colors.js", "names": ["_Paths", "data", "require", "_Resources", "getProjectColorsXMLPathAsync", "projectRoot", "kind", "getResourceXMLPathAsync", "name", "setColorItem", "itemToAdd", "colorFileContentsJSON", "_colorFileContentsJSO", "resources", "color", "colorNameExists", "filter", "e", "$", "_", "push", "removeColorItem", "named", "contents", "_contents$resources", "index", "findIndex", "splice", "assignColorValue", "xml", "value", "buildResourceItem", "getColorsAsObject", "_xml$resources", "getResourceItemsAsObject", "getObjectAsColorsXml", "obj", "getObjectAsResourceItems"], "sources": ["../../src/android/Colors.ts"], "sourcesContent": ["import { getResourceXMLPathAsync } from './Paths';\nimport {\n  buildResourceItem,\n  getObjectAsResourceItems,\n  getResourceItemsAsObject,\n  ResourceItemXML,\n  ResourceKind,\n  ResourceXML,\n} from './Resources';\n\nexport function getProjectColorsXMLPathAsync(\n  projectRoot: string,\n  { kind }: { kind?: ResourceKind } = {}\n) {\n  return getResourceXMLPathAsync(projectRoot, { kind, name: 'colors' });\n}\n\nexport function setColorItem(itemToAdd: ResourceItemXML, colorFileContentsJSON: ResourceXML) {\n  if (colorFileContentsJSON.resources?.color) {\n    const colorNameExists = colorFileContentsJSON.resources.color.filter(\n      (e: ResourceItemXML) => e.$.name === itemToAdd.$.name\n    )[0];\n    if (colorNameExists) {\n      colorNameExists._ = itemToAdd._;\n    } else {\n      colorFileContentsJSON.resources.color.push(itemToAdd);\n    }\n  } else {\n    if (!colorFileContentsJSON.resources || typeof colorFileContentsJSON.resources === 'string') {\n      //file was empty and JSON is `{resources : ''}`\n      colorFileContentsJSON.resources = {};\n    }\n    colorFileContentsJSON.resources.color = [itemToAdd];\n  }\n  return colorFileContentsJSON;\n}\n\nexport function removeColorItem(named: string, contents: ResourceXML) {\n  if (contents.resources?.color) {\n    const index = contents.resources.color.findIndex((e: ResourceItemXML) => e.$.name === named);\n    if (index > -1) {\n      // replace the previous value\n      contents.resources.color.splice(index, 1);\n    }\n  }\n  return contents;\n}\n\n/**\n * Set or remove value in XML based on nullish factor of the `value` property.\n */\nexport function assignColorValue(\n  xml: ResourceXML,\n  {\n    value,\n    name,\n  }: {\n    value?: string | null;\n    name: string;\n  }\n) {\n  if (value) {\n    return setColorItem(\n      buildResourceItem({\n        name,\n        value,\n      }),\n      xml\n    );\n  }\n\n  return removeColorItem(name, xml);\n}\n\n/**\n * Helper to convert a basic XML object into a simple k/v pair.\n * `colors.xml` is a very basic XML file so this is pretty safe to do.\n * Added for testing purposes.\n *\n * @param xml\n * @returns\n */\nexport function getColorsAsObject(xml: ResourceXML): Record<string, string> | null {\n  if (!xml?.resources?.color) {\n    return null;\n  }\n\n  return getResourceItemsAsObject(xml.resources.color);\n}\n\n/**\n * Helper to convert a basic k/v object to a colors XML object.\n *\n * @param xml\n * @returns\n */\nexport function getObjectAsColorsXml(obj: Record<string, string>): ResourceXML {\n  return {\n    resources: {\n      color: getObjectAsResourceItems(obj),\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AASO,SAASG,4BAA4BA,CAC1CC,WAAmB,EACnB;EAAEC;AAA8B,CAAC,GAAG,CAAC,CAAC,EACtC;EACA,OAAO,IAAAC,gCAAuB,EAACF,WAAW,EAAE;IAAEC,IAAI;IAAEE,IAAI,EAAE;EAAS,CAAC,CAAC;AACvE;AAEO,SAASC,YAAYA,CAACC,SAA0B,EAAEC,qBAAkC,EAAE;EAAA,IAAAC,qBAAA;EAC3F,KAAAA,qBAAA,GAAID,qBAAqB,CAACE,SAAS,cAAAD,qBAAA,eAA/BA,qBAAA,CAAiCE,KAAK,EAAE;IAC1C,MAAMC,eAAe,GAAGJ,qBAAqB,CAACE,SAAS,CAACC,KAAK,CAACE,MAAM,CACjEC,CAAkB,IAAKA,CAAC,CAACC,CAAC,CAACV,IAAI,KAAKE,SAAS,CAACQ,CAAC,CAACV,IAAI,CACtD,CAAC,CAAC,CAAC;IACJ,IAAIO,eAAe,EAAE;MACnBA,eAAe,CAACI,CAAC,GAAGT,SAAS,CAACS,CAAC;IACjC,CAAC,MAAM;MACLR,qBAAqB,CAACE,SAAS,CAACC,KAAK,CAACM,IAAI,CAACV,SAAS,CAAC;IACvD;EACF,CAAC,MAAM;IACL,IAAI,CAACC,qBAAqB,CAACE,SAAS,IAAI,OAAOF,qBAAqB,CAACE,SAAS,KAAK,QAAQ,EAAE;MAC3F;MACAF,qBAAqB,CAACE,SAAS,GAAG,CAAC,CAAC;IACtC;IACAF,qBAAqB,CAACE,SAAS,CAACC,KAAK,GAAG,CAACJ,SAAS,CAAC;EACrD;EACA,OAAOC,qBAAqB;AAC9B;AAEO,SAASU,eAAeA,CAACC,KAAa,EAAEC,QAAqB,EAAE;EAAA,IAAAC,mBAAA;EACpE,KAAAA,mBAAA,GAAID,QAAQ,CAACV,SAAS,cAAAW,mBAAA,eAAlBA,mBAAA,CAAoBV,KAAK,EAAE;IAC7B,MAAMW,KAAK,GAAGF,QAAQ,CAACV,SAAS,CAACC,KAAK,CAACY,SAAS,CAAET,CAAkB,IAAKA,CAAC,CAACC,CAAC,CAACV,IAAI,KAAKc,KAAK,CAAC;IAC5F,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;MACd;MACAF,QAAQ,CAACV,SAAS,CAACC,KAAK,CAACa,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3C;EACF;EACA,OAAOF,QAAQ;AACjB;;AAEA;AACA;AACA;AACO,SAASK,gBAAgBA,CAC9BC,GAAgB,EAChB;EACEC,KAAK;EACLtB;AAIF,CAAC,EACD;EACA,IAAIsB,KAAK,EAAE;IACT,OAAOrB,YAAY,CACjB,IAAAsB,8BAAiB,EAAC;MAChBvB,IAAI;MACJsB;IACF,CAAC,CAAC,EACFD,GAAG,CACJ;EACH;EAEA,OAAOR,eAAe,CAACb,IAAI,EAAEqB,GAAG,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,iBAAiBA,CAACH,GAAgB,EAAiC;EAAA,IAAAI,cAAA;EACjF,IAAI,EAACJ,GAAG,aAAHA,GAAG,gBAAAI,cAAA,GAAHJ,GAAG,CAAEhB,SAAS,cAAAoB,cAAA,eAAdA,cAAA,CAAgBnB,KAAK,GAAE;IAC1B,OAAO,IAAI;EACb;EAEA,OAAO,IAAAoB,qCAAwB,EAACL,GAAG,CAAChB,SAAS,CAACC,KAAK,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASqB,oBAAoBA,CAACC,GAA2B,EAAe;EAC7E,OAAO;IACLvB,SAAS,EAAE;MACTC,KAAK,EAAE,IAAAuB,qCAAwB,EAACD,GAAG;IACrC;EACF,CAAC;AACH"}