{"version": 3, "file": "BuildProperties.js", "names": ["_androidPlugins", "data", "require", "createBuildGradlePropsConfigPlugin", "configToPropertyRules", "name", "withUnknown", "config", "sourceConfig", "withGradleProperties", "modResults", "updateAndroidBuildPropertiesFromConfig", "Object", "defineProperty", "value", "withJsEngineGradleProps", "propName", "propValueGetter", "_ref", "_config$android$jsEng", "_config$android", "android", "jsEngine", "toString", "exports", "gradleProperties", "configToProperty", "updateAndroidBuildProperty", "options", "oldPropIndex", "findIndex", "prop", "type", "key", "newProp", "push", "removePropWhenValueIsNull", "splice"], "sources": ["../../src/android/BuildProperties.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\n\nimport type { ConfigPlugin } from '../Plugin.types';\nimport { withGradleProperties } from '../plugins/android-plugins';\nimport { BuildPropertiesConfig, ConfigToPropertyRuleType } from '../utils/BuildProperties.types';\nimport type { PropertiesItem } from './Properties';\n\n/**\n * Creates a `withGradleProperties` config-plugin based on given config to property mapping rules.\n *\n * The factory supports two modes from generic type inference\n * ```ts\n * // config-plugin without `props`, it will implicitly use the expo config as source config.\n * createBuildGradlePropsConfigPlugin<ExpoConfig>(): ConfigPlugin<void>;\n *\n * // config-plugin with a parameter `props: CustomType`, it will use the `props` as source config.\n * createBuildGradlePropsConfigPlugin<CustomType>(): ConfigPlugin<CustomType>;\n * ```\n *\n * @param configToPropertyRules config to property mapping rules\n * @param name the config plugin name\n */\nexport function createBuildGradlePropsConfigPlugin<SourceConfigType extends BuildPropertiesConfig>(\n  configToPropertyRules: ConfigToPropertyRuleType<SourceConfigType>[],\n  name?: string\n) {\n  const withUnknown: ConfigPlugin<SourceConfigType extends ExpoConfig ? void : SourceConfigType> = (\n    config,\n    sourceConfig\n  ) =>\n    withGradleProperties(config, (config) => {\n      config.modResults = updateAndroidBuildPropertiesFromConfig(\n        (sourceConfig ?? config) as SourceConfigType,\n        config.modResults,\n        configToPropertyRules\n      );\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\n/**\n * A config-plugin to update `android/gradle.properties` from the `jsEngine` in expo config\n */\nexport const withJsEngineGradleProps = createBuildGradlePropsConfigPlugin<ExpoConfig>(\n  [\n    {\n      propName: 'hermesEnabled',\n      propValueGetter: (config) =>\n        ((config.android?.jsEngine ?? config.jsEngine ?? 'hermes') === 'hermes').toString(),\n    },\n  ],\n  'withJsEngineGradleProps'\n);\n\nexport function updateAndroidBuildPropertiesFromConfig<\n  SourceConfigType extends BuildPropertiesConfig\n>(\n  config: SourceConfigType,\n  gradleProperties: PropertiesItem[],\n  configToPropertyRules: ConfigToPropertyRuleType<SourceConfigType>[]\n) {\n  for (const configToProperty of configToPropertyRules) {\n    const value = configToProperty.propValueGetter(config);\n    updateAndroidBuildProperty(gradleProperties, configToProperty.propName, value);\n  }\n\n  return gradleProperties;\n}\n\nexport function updateAndroidBuildProperty(\n  gradleProperties: PropertiesItem[],\n  name: string,\n  value: string | null | undefined,\n  options?: { removePropWhenValueIsNull?: boolean }\n) {\n  const oldPropIndex = gradleProperties.findIndex(\n    (prop) => prop.type === 'property' && prop.key === name\n  );\n\n  if (value) {\n    // found the matched value, add or merge new property\n    const newProp: PropertiesItem = {\n      type: 'property',\n      key: name,\n      value,\n    };\n\n    if (oldPropIndex >= 0) {\n      gradleProperties[oldPropIndex] = newProp;\n    } else {\n      gradleProperties.push(newProp);\n    }\n  } else if (options?.removePropWhenValueIsNull && oldPropIndex >= 0) {\n    gradleProperties.splice(oldPropIndex, 1);\n  }\n\n  return gradleProperties;\n}\n"], "mappings": ";;;;;;;;;AAGA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,kCAAkCA,CAChDC,qBAAmE,EACnEC,IAAa,EACb;EACA,MAAMC,WAAwF,GAAGA,CAC/FC,MAAM,EACNC,YAAY,KAEZ,IAAAC,sCAAoB,EAACF,MAAM,EAAGA,MAAM,IAAK;IACvCA,MAAM,CAACG,UAAU,GAAGC,sCAAsC,CACvDH,YAAY,aAAZA,YAAY,cAAZA,YAAY,GAAID,MAAM,EACvBA,MAAM,CAACG,UAAU,EACjBN,qBAAqB,CACtB;IACD,OAAOG,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRO,MAAM,CAACC,cAAc,CAACP,WAAW,EAAE,MAAM,EAAE;MACzCQ,KAAK,EAAET;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;;AAEA;AACA;AACA;AACO,MAAMS,uBAAuB,GAAGZ,kCAAkC,CACvE,CACE;EACEa,QAAQ,EAAE,eAAe;EACzBC,eAAe,EAAGV,MAAM;IAAA,IAAAW,IAAA,EAAAC,qBAAA,EAAAC,eAAA;IAAA,OACtB,CAAC,EAAAF,IAAA,IAAAC,qBAAA,IAAAC,eAAA,GAACb,MAAM,CAACc,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,QAAQ,cAAAH,qBAAA,cAAAA,qBAAA,GAAIZ,MAAM,CAACe,QAAQ,cAAAJ,IAAA,cAAAA,IAAA,GAAI,QAAQ,MAAM,QAAQ,EAAEK,QAAQ,EAAE;EAAA;AACvF,CAAC,CACF,EACD,yBAAyB,CAC1B;AAACC,OAAA,CAAAT,uBAAA,GAAAA,uBAAA;AAEK,SAASJ,sCAAsCA,CAGpDJ,MAAwB,EACxBkB,gBAAkC,EAClCrB,qBAAmE,EACnE;EACA,KAAK,MAAMsB,gBAAgB,IAAItB,qBAAqB,EAAE;IACpD,MAAMU,KAAK,GAAGY,gBAAgB,CAACT,eAAe,CAACV,MAAM,CAAC;IACtDoB,0BAA0B,CAACF,gBAAgB,EAAEC,gBAAgB,CAACV,QAAQ,EAAEF,KAAK,CAAC;EAChF;EAEA,OAAOW,gBAAgB;AACzB;AAEO,SAASE,0BAA0BA,CACxCF,gBAAkC,EAClCpB,IAAY,EACZS,KAAgC,EAChCc,OAAiD,EACjD;EACA,MAAMC,YAAY,GAAGJ,gBAAgB,CAACK,SAAS,CAC5CC,IAAI,IAAKA,IAAI,CAACC,IAAI,KAAK,UAAU,IAAID,IAAI,CAACE,GAAG,KAAK5B,IAAI,CACxD;EAED,IAAIS,KAAK,EAAE;IACT;IACA,MAAMoB,OAAuB,GAAG;MAC9BF,IAAI,EAAE,UAAU;MAChBC,GAAG,EAAE5B,IAAI;MACTS;IACF,CAAC;IAED,IAAIe,YAAY,IAAI,CAAC,EAAE;MACrBJ,gBAAgB,CAACI,YAAY,CAAC,GAAGK,OAAO;IAC1C,CAAC,MAAM;MACLT,gBAAgB,CAACU,IAAI,CAACD,OAAO,CAAC;IAChC;EACF,CAAC,MAAM,IAAIN,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEQ,yBAAyB,IAAIP,YAAY,IAAI,CAAC,EAAE;IAClEJ,gBAAgB,CAACY,MAAM,CAACR,YAAY,EAAE,CAAC,CAAC;EAC1C;EAEA,OAAOJ,gBAAgB;AACzB"}