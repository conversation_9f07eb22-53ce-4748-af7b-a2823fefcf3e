{"version": 3, "file": "Version.js", "names": ["_androidPlugins", "data", "require", "_warnings", "withVersion", "config", "withAppBuildGradle", "modResults", "language", "contents", "setVersionCode", "setVersionName", "addWarningAndroid", "exports", "withBuildScriptExtMinimumVersion", "props", "withProjectBuildGradle", "setMinBuildScriptExtVersion", "buildGradle", "name", "minVersion", "_buildGradle$match", "regex", "RegExp", "currentVersion", "match", "currentVersionNum", "Number", "replace", "Math", "max", "getVersionName", "_config$version", "version", "versionName", "pattern", "getVersionCode", "_config$android$versi", "_config$android", "android", "versionCode"], "sources": ["../../src/android/Version.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAppBuildGradle, withProjectBuildGradle } from '../plugins/android-plugins';\nimport { addWarningAndroid } from '../utils/warnings';\n\nexport const withVersion: ConfigPlugin = (config) => {\n  return withAppBuildGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = setVersionCode(config, config.modResults.contents);\n      config.modResults.contents = setVersionName(config, config.modResults.contents);\n    } else {\n      addWarningAndroid(\n        'android.versionCode',\n        `Cannot automatically configure app build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\n/** Sets a numeric version for a value in the project.gradle buildscript.ext object to be at least the provided props.minVersion, if the existing value is greater then no change will be made. */\nexport const withBuildScriptExtMinimumVersion: ConfigPlugin<{\n  name: string;\n  minVersion: number;\n}> = (config, props) => {\n  return withProjectBuildGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = setMinBuildScriptExtVersion(config.modResults.contents, props);\n    } else {\n      addWarningAndroid(\n        'withBuildScriptExtVersion',\n        `Cannot automatically configure project build.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\nexport function setMinBuildScriptExtVersion(\n  buildGradle: string,\n  { name, minVersion }: { name: string; minVersion: number }\n) {\n  const regex = new RegExp(`(${name}\\\\s?=\\\\s?)(\\\\d+(?:\\\\.\\\\d+)?)`);\n  const currentVersion = buildGradle.match(regex)?.[2];\n  if (!currentVersion) {\n    addWarningAndroid(\n      'withBuildScriptExtVersion',\n      `Cannot set minimum buildscript.ext.${name} version because the property \"${name}\" cannot be found or does not have a numeric value.`\n    );\n    // TODO: Maybe just add the property...\n    return buildGradle;\n  }\n\n  const currentVersionNum = Number(currentVersion);\n  return buildGradle.replace(regex, `$1${Math.max(minVersion, currentVersionNum)}`);\n}\n\nexport function getVersionName(config: Pick<ExpoConfig, 'version'>) {\n  return config.version ?? null;\n}\n\nexport function setVersionName(config: Pick<ExpoConfig, 'version'>, buildGradle: string) {\n  const versionName = getVersionName(config);\n  if (versionName === null) {\n    return buildGradle;\n  }\n\n  const pattern = new RegExp(`versionName \".*\"`);\n  return buildGradle.replace(pattern, `versionName \"${versionName}\"`);\n}\n\nexport function getVersionCode(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.versionCode ?? 1;\n}\n\nexport function setVersionCode(config: Pick<ExpoConfig, 'android'>, buildGradle: string) {\n  const versionCode = getVersionCode(config);\n  if (versionCode === null) {\n    return buildGradle;\n  }\n\n  const pattern = new RegExp(`versionCode.*`);\n  return buildGradle.replace(pattern, `versionCode ${versionCode}`);\n}\n"], "mappings": ";;;;;;;;;;;AAGA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,oCAAkB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC5C,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGC,cAAc,CAACL,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;MAC/EJ,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGE,cAAc,CAACN,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;IACjF,CAAC,MAAM;MACL,IAAAG,6BAAiB,EACf,qBAAqB,EACpB,oEAAmE,CACrE;IACH;IACA,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAQ,OAAA,CAAAT,WAAA,GAAAA,WAAA;AACO,MAAMU,gCAGX,GAAGA,CAACT,MAAM,EAAEU,KAAK,KAAK;EACtB,OAAO,IAAAC,wCAAsB,EAACX,MAAM,EAAGA,MAAM,IAAK;IAChD,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGQ,2BAA2B,CAACZ,MAAM,CAACE,UAAU,CAACE,QAAQ,EAAEM,KAAK,CAAC;IAC7F,CAAC,MAAM;MACL,IAAAH,6BAAiB,EACf,2BAA2B,EAC1B,wEAAuE,CACzE;IACH;IACA,OAAOP,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACQ,OAAA,CAAAC,gCAAA,GAAAA,gCAAA;AAEK,SAASG,2BAA2BA,CACzCC,WAAmB,EACnB;EAAEC,IAAI;EAAEC;AAAiD,CAAC,EAC1D;EAAA,IAAAC,kBAAA;EACA,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAE,IAAGJ,IAAK,8BAA6B,CAAC;EAChE,MAAMK,cAAc,IAAAH,kBAAA,GAAGH,WAAW,CAACO,KAAK,CAACH,KAAK,CAAC,cAAAD,kBAAA,uBAAxBA,kBAAA,CAA2B,CAAC,CAAC;EACpD,IAAI,CAACG,cAAc,EAAE;IACnB,IAAAZ,6BAAiB,EACf,2BAA2B,EAC1B,sCAAqCO,IAAK,kCAAiCA,IAAK,qDAAoD,CACtI;IACD;IACA,OAAOD,WAAW;EACpB;EAEA,MAAMQ,iBAAiB,GAAGC,MAAM,CAACH,cAAc,CAAC;EAChD,OAAON,WAAW,CAACU,OAAO,CAACN,KAAK,EAAG,KAAIO,IAAI,CAACC,GAAG,CAACV,UAAU,EAAEM,iBAAiB,CAAE,EAAC,CAAC;AACnF;AAEO,SAASK,cAAcA,CAAC1B,MAAmC,EAAE;EAAA,IAAA2B,eAAA;EAClE,QAAAA,eAAA,GAAO3B,MAAM,CAAC4B,OAAO,cAAAD,eAAA,cAAAA,eAAA,GAAI,IAAI;AAC/B;AAEO,SAASrB,cAAcA,CAACN,MAAmC,EAAEa,WAAmB,EAAE;EACvF,MAAMgB,WAAW,GAAGH,cAAc,CAAC1B,MAAM,CAAC;EAC1C,IAAI6B,WAAW,KAAK,IAAI,EAAE;IACxB,OAAOhB,WAAW;EACpB;EAEA,MAAMiB,OAAO,GAAG,IAAIZ,MAAM,CAAE,kBAAiB,CAAC;EAC9C,OAAOL,WAAW,CAACU,OAAO,CAACO,OAAO,EAAG,gBAAeD,WAAY,GAAE,CAAC;AACrE;AAEO,SAASE,cAAcA,CAAC/B,MAAmC,EAAE;EAAA,IAAAgC,qBAAA,EAAAC,eAAA;EAClE,QAAAD,qBAAA,IAAAC,eAAA,GAAOjC,MAAM,CAACkC,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,WAAW,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,CAAC;AACzC;AAEO,SAAS3B,cAAcA,CAACL,MAAmC,EAAEa,WAAmB,EAAE;EACvF,MAAMsB,WAAW,GAAGJ,cAAc,CAAC/B,MAAM,CAAC;EAC1C,IAAImC,WAAW,KAAK,IAAI,EAAE;IACxB,OAAOtB,WAAW;EACpB;EAEA,MAAMiB,OAAO,GAAG,IAAIZ,MAAM,CAAE,eAAc,CAAC;EAC3C,OAAOL,WAAW,CAACU,OAAO,CAACO,OAAO,EAAG,eAAcK,WAAY,EAAC,CAAC;AACnE"}