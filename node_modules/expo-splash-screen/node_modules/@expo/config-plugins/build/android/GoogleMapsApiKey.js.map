{"version": 3, "file": "GoogleMapsApiKey.js", "names": ["_androidPlugins", "data", "require", "_Manifest", "META_API_KEY", "LIB_HTTP", "withGoogleMapsApiKey", "createAndroidManifestPlugin", "setGoogleMapsApiKey", "exports", "getGoogleMapsApiKey", "config", "_config$android$confi", "_config$android", "_config$android$confi2", "_config$android$confi3", "android", "googleMaps", "<PERSON><PERSON><PERSON><PERSON>", "androidManifest", "mainApplication", "getMainApplicationOrThrow", "addMetaDataItemToMainApplication", "addUsesLibraryItemToMainApplication", "name", "required", "removeMetaDataItemFromMainApplication", "removeUsesLibraryItemFromMainApplication"], "sources": ["../../src/android/GoogleMapsApiKey.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\nimport {\n  addMetaDataItemToMainApplication,\n  addUsesLibraryItemToMainApplication,\n  AndroidManifest,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n  removeUsesLibraryItemFromMainApplication,\n} from './Manifest';\n\nconst META_API_KEY = 'com.google.android.geo.API_KEY';\nconst LIB_HTTP = 'org.apache.http.legacy';\n\nexport const withGoogleMapsApiKey = createAndroidManifestPlugin(\n  setGoogleMapsApiKey,\n  'withGoogleMapsApiKey'\n);\n\nexport function getGoogleMapsApiKey(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.config?.googleMaps?.apiKey ?? null;\n}\n\nexport function setGoogleMapsApiKey(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const apiKey = getGoogleMapsApiKey(config);\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  if (apiKey) {\n    // If the item exists, add it back\n    addMetaDataItemToMainApplication(mainApplication, META_API_KEY, apiKey);\n    addUsesLibraryItemToMainApplication(mainApplication, {\n      name: LIB_HTTP,\n      required: false,\n    });\n  } else {\n    // Remove any existing item\n    removeMetaDataItemFromMainApplication(mainApplication, META_API_KEY);\n    removeUsesLibraryItemFromMainApplication(mainApplication, LIB_HTTP);\n  }\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;AAEA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AASA,MAAMG,YAAY,GAAG,gCAAgC;AACrD,MAAMC,QAAQ,GAAG,wBAAwB;AAElC,MAAMC,oBAAoB,GAAG,IAAAC,6CAA2B,EAC7DC,mBAAmB,EACnB,sBAAsB,CACvB;AAACC,OAAA,CAAAH,oBAAA,GAAAA,oBAAA;AAEK,SAASI,mBAAmBA,CAACC,MAAmC,EAAE;EAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACvE,QAAAH,qBAAA,IAAAC,eAAA,GAAOF,MAAM,CAACK,OAAO,cAAAH,eAAA,wBAAAC,sBAAA,GAAdD,eAAA,CAAgBF,MAAM,cAAAG,sBAAA,wBAAAC,sBAAA,GAAtBD,sBAAA,CAAwBG,UAAU,cAAAF,sBAAA,uBAAlCA,sBAAA,CAAoCG,MAAM,cAAAN,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AAC3D;AAEO,SAASJ,mBAAmBA,CACjCG,MAAmC,EACnCQ,eAAgC,EAChC;EACA,MAAMD,MAAM,GAAGR,mBAAmB,CAACC,MAAM,CAAC;EAC1C,MAAMS,eAAe,GAAG,IAAAC,qCAAyB,EAACF,eAAe,CAAC;EAElE,IAAID,MAAM,EAAE;IACV;IACA,IAAAI,4CAAgC,EAACF,eAAe,EAAEhB,YAAY,EAAEc,MAAM,CAAC;IACvE,IAAAK,+CAAmC,EAACH,eAAe,EAAE;MACnDI,IAAI,EAAEnB,QAAQ;MACdoB,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA,IAAAC,iDAAqC,EAACN,eAAe,EAAEhB,YAAY,CAAC;IACpE,IAAAuB,oDAAwC,EAACP,eAAe,EAAEf,QAAQ,CAAC;EACrE;EAEA,OAAOc,eAAe;AACxB"}