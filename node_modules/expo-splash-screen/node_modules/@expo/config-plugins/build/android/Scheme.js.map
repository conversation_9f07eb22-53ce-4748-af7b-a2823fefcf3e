{"version": 3, "file": "Scheme.js", "names": ["_androidPlugins", "data", "require", "_warnings", "withScheme", "createAndroidManifestPlugin", "setScheme", "exports", "getScheme", "config", "Array", "isArray", "scheme", "validate", "value", "filter", "androidManifest", "_config$android", "_config$android2", "schemes", "android", "package", "push", "length", "ensureManifestHasValidIntentFilter", "addWarningAndroid", "currentSchemes", "getSchemesFromManifest", "uri", "index", "indexOf", "splice", "appendScheme", "isValidRedirectIntentFilter", "actions", "categories", "includes", "propertiesFromIntentFilter", "<PERSON><PERSON><PERSON><PERSON>", "_intentFilter$action$", "_intentFilter$action", "_intentFilter$categor", "_intentFilter$categor2", "_intentFilter$data$fi", "_intentFilter$data", "_intentFilter$data$fi2", "action", "map", "_data$$", "$", "category", "_data$$2", "_data$$3", "_data$$4", "_data$$5", "host", "getSingleTaskIntentFilters", "manifest", "application", "outputSchemes", "activity", "activities", "singleTaskActivities", "_activity$$", "intentFilters", "concat", "requestedHost", "singleTaskIntentFilters", "properties", "_activity$$2", "hasScheme", "_activity$$3", "removeScheme", "_activity$$4", "dataKey", "_intentFilter$data2", "_data$$6", "_intentFilter$data3"], "sources": ["../../src/android/Scheme.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\nimport { addWarningAndroid } from '../utils/warnings';\nimport { AndroidManifest, ManifestActivity } from './Manifest';\n\nexport type IntentFilterProps = {\n  actions: string[];\n  categories: string[];\n  data: {\n    scheme: string;\n    host?: string;\n  }[];\n};\n\nexport const withScheme = createAndroidManifestPlugin(setScheme, 'withScheme');\n\nexport function getScheme(config: { scheme?: string | string[] }): string[] {\n  if (Array.isArray(config.scheme)) {\n    const validate = (value: any): value is string => typeof value === 'string';\n\n    return config.scheme.filter<string>(validate);\n  } else if (typeof config.scheme === 'string') {\n    return [config.scheme];\n  }\n  return [];\n}\n\n// This plugin used to remove the unused schemes but this is unpredictable because other plugins could add schemes.\n// The only way to reliably remove schemes from the project is to nuke the file and regenerate the code (`npx expo prebuild --clean`).\n// Regardless, having extra schemes isn't a fatal issue and therefore a tolerable compromise is to just add new schemes that aren't currently present.\nexport function setScheme(\n  config: Pick<ExpoConfig, 'scheme' | 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const schemes = [\n    ...getScheme(config),\n    // @ts-ignore: TODO: android.scheme is an unreleased -- harder to add to turtle v1.\n    ...getScheme(config.android ?? {}),\n  ];\n  // Add the package name to the list of schemes for easier Google auth and parity with Turtle v1.\n  if (config.android?.package) {\n    schemes.push(config.android.package);\n  }\n  if (schemes.length === 0) {\n    return androidManifest;\n  }\n\n  if (!ensureManifestHasValidIntentFilter(androidManifest)) {\n    addWarningAndroid(\n      'scheme',\n      `Cannot add schemes because the provided manifest does not have a valid Activity with \\`android:launchMode=\"singleTask\"\\``,\n      'https://expo.fyi/setup-android-uri-scheme'\n    );\n    return androidManifest;\n  }\n\n  // Get the current schemes and remove them from the list of schemes to add.\n  const currentSchemes = getSchemesFromManifest(androidManifest);\n  for (const uri of currentSchemes) {\n    const index = schemes.indexOf(uri);\n    if (index > -1) schemes.splice(index, 1);\n  }\n\n  // Now add all of the remaining schemes.\n  for (const uri of schemes) {\n    androidManifest = appendScheme(uri, androidManifest);\n  }\n\n  return androidManifest;\n}\n\nfunction isValidRedirectIntentFilter({ actions, categories }: IntentFilterProps): boolean {\n  return (\n    actions.includes('android.intent.action.VIEW') &&\n    !categories.includes('android.intent.category.LAUNCHER')\n  );\n}\n\nfunction propertiesFromIntentFilter(intentFilter: any): IntentFilterProps {\n  const actions = intentFilter?.action?.map((data: any) => data?.$?.['android:name']) ?? [];\n  const categories = intentFilter?.category?.map((data: any) => data?.$?.['android:name']) ?? [];\n  const data =\n    intentFilter?.data\n      ?.filter((data: any) => data?.$?.['android:scheme'])\n      ?.map((data: any) => ({\n        scheme: data?.$?.['android:scheme'],\n        host: data?.$?.['android:host'],\n      })) ?? [];\n  return {\n    actions,\n    categories,\n    data,\n  };\n}\n\nfunction getSingleTaskIntentFilters(androidManifest: AndroidManifest): any[] {\n  if (!Array.isArray(androidManifest.manifest.application)) return [];\n\n  let outputSchemes: any[] = [];\n  for (const application of androidManifest.manifest.application) {\n    const { activity } = application;\n    // @ts-ignore\n    const activities = Array.isArray(activity) ? activity : [activity];\n    const singleTaskActivities = (activities as ManifestActivity[]).filter(\n      (activity) => activity?.$?.['android:launchMode'] === 'singleTask'\n    );\n    for (const activity of singleTaskActivities) {\n      const intentFilters = activity['intent-filter'];\n      outputSchemes = outputSchemes.concat(intentFilters);\n    }\n  }\n  return outputSchemes;\n}\n\nexport function getSchemesFromManifest(\n  androidManifest: AndroidManifest,\n  requestedHost: string | null = null\n): string[] {\n  const outputSchemes: string[] = [];\n\n  const singleTaskIntentFilters = getSingleTaskIntentFilters(androidManifest);\n  for (const intentFilter of singleTaskIntentFilters) {\n    const properties = propertiesFromIntentFilter(intentFilter);\n    if (isValidRedirectIntentFilter(properties) && properties.data) {\n      for (const { scheme, host } of properties.data) {\n        if (requestedHost === null || !host || host === requestedHost) {\n          outputSchemes.push(scheme);\n        }\n      }\n    }\n  }\n\n  return outputSchemes;\n}\n\nexport function ensureManifestHasValidIntentFilter(androidManifest: AndroidManifest): boolean {\n  if (!Array.isArray(androidManifest.manifest.application)) {\n    return false;\n  }\n\n  for (const application of androidManifest.manifest.application) {\n    for (const activity of application.activity || []) {\n      if (activity?.$?.['android:launchMode'] === 'singleTask') {\n        for (const intentFilter of activity['intent-filter'] || []) {\n          // Parse valid intent filters...\n          const properties = propertiesFromIntentFilter(intentFilter);\n          if (isValidRedirectIntentFilter(properties)) {\n            return true;\n          }\n        }\n        if (!activity['intent-filter']) {\n          activity['intent-filter'] = [];\n        }\n\n        activity['intent-filter'].push({\n          action: [{ $: { 'android:name': 'android.intent.action.VIEW' } }],\n          category: [\n            { $: { 'android:name': 'android.intent.category.DEFAULT' } },\n            { $: { 'android:name': 'android.intent.category.BROWSABLE' } },\n          ],\n        });\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\nexport function hasScheme(scheme: string, androidManifest: AndroidManifest): boolean {\n  const schemes = getSchemesFromManifest(androidManifest);\n  return schemes.includes(scheme);\n}\n\nexport function appendScheme(scheme: string, androidManifest: AndroidManifest): AndroidManifest {\n  if (!Array.isArray(androidManifest.manifest.application)) {\n    return androidManifest;\n  }\n\n  for (const application of androidManifest.manifest.application) {\n    for (const activity of application.activity || []) {\n      if (activity?.$?.['android:launchMode'] === 'singleTask') {\n        for (const intentFilter of activity['intent-filter'] || []) {\n          const properties = propertiesFromIntentFilter(intentFilter);\n          if (isValidRedirectIntentFilter(properties)) {\n            if (!intentFilter.data) intentFilter.data = [];\n            intentFilter.data.push({\n              $: { 'android:scheme': scheme },\n            });\n          }\n        }\n        break;\n      }\n    }\n  }\n  return androidManifest;\n}\n\nexport function removeScheme(scheme: string, androidManifest: AndroidManifest): AndroidManifest {\n  if (!Array.isArray(androidManifest.manifest.application)) {\n    return androidManifest;\n  }\n\n  for (const application of androidManifest.manifest.application) {\n    for (const activity of application.activity || []) {\n      if (activity?.$?.['android:launchMode'] === 'singleTask') {\n        for (const intentFilter of activity['intent-filter'] || []) {\n          // Parse valid intent filters...\n          const properties = propertiesFromIntentFilter(intentFilter);\n          if (isValidRedirectIntentFilter(properties)) {\n            for (const dataKey in intentFilter?.data || []) {\n              const data = intentFilter.data?.[dataKey];\n              if (data?.$?.['android:scheme'] === scheme) {\n                delete intentFilter.data?.[dataKey];\n              }\n            }\n          }\n        }\n        break;\n      }\n    }\n  }\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;;;;;;AAEA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAYO,MAAMG,UAAU,GAAG,IAAAC,6CAA2B,EAACC,SAAS,EAAE,YAAY,CAAC;AAACC,OAAA,CAAAH,UAAA,GAAAA,UAAA;AAExE,SAASI,SAASA,CAACC,MAAsC,EAAY;EAC1E,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAACG,MAAM,CAAC,EAAE;IAChC,MAAMC,QAAQ,GAAIC,KAAU,IAAsB,OAAOA,KAAK,KAAK,QAAQ;IAE3E,OAAOL,MAAM,CAACG,MAAM,CAACG,MAAM,CAASF,QAAQ,CAAC;EAC/C,CAAC,MAAM,IAAI,OAAOJ,MAAM,CAACG,MAAM,KAAK,QAAQ,EAAE;IAC5C,OAAO,CAACH,MAAM,CAACG,MAAM,CAAC;EACxB;EACA,OAAO,EAAE;AACX;;AAEA;AACA;AACA;AACO,SAASN,SAASA,CACvBG,MAA8C,EAC9CO,eAAgC,EAChC;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACA,MAAMC,OAAO,GAAG,CACd,GAAGX,SAAS,CAACC,MAAM,CAAC;EACpB;EACA,GAAGD,SAAS,EAAAS,eAAA,GAACR,MAAM,CAACW,OAAO,cAAAH,eAAA,cAAAA,eAAA,GAAI,CAAC,CAAC,CAAC,CACnC;EACD;EACA,KAAAC,gBAAA,GAAIT,MAAM,CAACW,OAAO,cAAAF,gBAAA,eAAdA,gBAAA,CAAgBG,OAAO,EAAE;IAC3BF,OAAO,CAACG,IAAI,CAACb,MAAM,CAACW,OAAO,CAACC,OAAO,CAAC;EACtC;EACA,IAAIF,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;IACxB,OAAOP,eAAe;EACxB;EAEA,IAAI,CAACQ,kCAAkC,CAACR,eAAe,CAAC,EAAE;IACxD,IAAAS,6BAAiB,EACf,QAAQ,EACP,0HAAyH,EAC1H,2CAA2C,CAC5C;IACD,OAAOT,eAAe;EACxB;;EAEA;EACA,MAAMU,cAAc,GAAGC,sBAAsB,CAACX,eAAe,CAAC;EAC9D,KAAK,MAAMY,GAAG,IAAIF,cAAc,EAAE;IAChC,MAAMG,KAAK,GAAGV,OAAO,CAACW,OAAO,CAACF,GAAG,CAAC;IAClC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAEV,OAAO,CAACY,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAC1C;;EAEA;EACA,KAAK,MAAMD,GAAG,IAAIT,OAAO,EAAE;IACzBH,eAAe,GAAGgB,YAAY,CAACJ,GAAG,EAAEZ,eAAe,CAAC;EACtD;EAEA,OAAOA,eAAe;AACxB;AAEA,SAASiB,2BAA2BA,CAAC;EAAEC,OAAO;EAAEC;AAA8B,CAAC,EAAW;EACxF,OACED,OAAO,CAACE,QAAQ,CAAC,4BAA4B,CAAC,IAC9C,CAACD,UAAU,CAACC,QAAQ,CAAC,kCAAkC,CAAC;AAE5D;AAEA,SAASC,0BAA0BA,CAACC,YAAiB,EAAqB;EAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA;EACxE,MAAMX,OAAO,IAAAK,qBAAA,GAAGD,YAAY,aAAZA,YAAY,wBAAAE,oBAAA,GAAZF,YAAY,CAAEQ,MAAM,cAAAN,oBAAA,uBAApBA,oBAAA,CAAsBO,GAAG,CAAE9C,IAAS;IAAA,IAAA+C,OAAA;IAAA,OAAK/C,IAAI,aAAJA,IAAI,wBAAA+C,OAAA,GAAJ/C,IAAI,CAAEgD,CAAC,cAAAD,OAAA,uBAAPA,OAAA,CAAU,cAAc,CAAC;EAAA,EAAC,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,EAAE;EACzF,MAAMJ,UAAU,IAAAM,qBAAA,GAAGH,YAAY,aAAZA,YAAY,wBAAAI,sBAAA,GAAZJ,YAAY,CAAEY,QAAQ,cAAAR,sBAAA,uBAAtBA,sBAAA,CAAwBK,GAAG,CAAE9C,IAAS;IAAA,IAAAkD,QAAA;IAAA,OAAKlD,IAAI,aAAJA,IAAI,wBAAAkD,QAAA,GAAJlD,IAAI,CAAEgD,CAAC,cAAAE,QAAA,uBAAPA,QAAA,CAAU,cAAc,CAAC;EAAA,EAAC,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,EAAE;EAC9F,MAAMxC,IAAI,IAAA0C,qBAAA,GACRL,YAAY,aAAZA,YAAY,wBAAAM,kBAAA,GAAZN,YAAY,CAAErC,IAAI,cAAA2C,kBAAA,wBAAAC,sBAAA,GAAlBD,kBAAA,CACI7B,MAAM,CAAEd,IAAS;IAAA,IAAAmD,QAAA;IAAA,OAAKnD,IAAI,aAAJA,IAAI,wBAAAmD,QAAA,GAAJnD,IAAI,CAAEgD,CAAC,cAAAG,QAAA,uBAAPA,QAAA,CAAU,gBAAgB,CAAC;EAAA,EAAC,cAAAP,sBAAA,uBADtDA,sBAAA,CAEIE,GAAG,CAAE9C,IAAS;IAAA,IAAAoD,QAAA,EAAAC,QAAA;IAAA,OAAM;MACpB1C,MAAM,EAAEX,IAAI,aAAJA,IAAI,wBAAAoD,QAAA,GAAJpD,IAAI,CAAEgD,CAAC,cAAAI,QAAA,uBAAPA,QAAA,CAAU,gBAAgB,CAAC;MACnCE,IAAI,EAAEtD,IAAI,aAAJA,IAAI,wBAAAqD,QAAA,GAAJrD,IAAI,CAAEgD,CAAC,cAAAK,QAAA,uBAAPA,QAAA,CAAU,cAAc;IAChC,CAAC;EAAA,CAAC,CAAC,cAAAX,qBAAA,cAAAA,qBAAA,GAAI,EAAE;EACb,OAAO;IACLT,OAAO;IACPC,UAAU;IACVlC;EACF,CAAC;AACH;AAEA,SAASuD,0BAA0BA,CAACxC,eAAgC,EAAS;EAC3E,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,eAAe,CAACyC,QAAQ,CAACC,WAAW,CAAC,EAAE,OAAO,EAAE;EAEnE,IAAIC,aAAoB,GAAG,EAAE;EAC7B,KAAK,MAAMD,WAAW,IAAI1C,eAAe,CAACyC,QAAQ,CAACC,WAAW,EAAE;IAC9D,MAAM;MAAEE;IAAS,CAAC,GAAGF,WAAW;IAChC;IACA,MAAMG,UAAU,GAAGnD,KAAK,CAACC,OAAO,CAACiD,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAClE,MAAME,oBAAoB,GAAID,UAAU,CAAwB9C,MAAM,CACnE6C,QAAQ;MAAA,IAAAG,WAAA;MAAA,OAAK,CAAAH,QAAQ,aAARA,QAAQ,wBAAAG,WAAA,GAARH,QAAQ,CAAEX,CAAC,cAAAc,WAAA,uBAAXA,WAAA,CAAc,oBAAoB,CAAC,MAAK,YAAY;IAAA,EACnE;IACD,KAAK,MAAMH,QAAQ,IAAIE,oBAAoB,EAAE;MAC3C,MAAME,aAAa,GAAGJ,QAAQ,CAAC,eAAe,CAAC;MAC/CD,aAAa,GAAGA,aAAa,CAACM,MAAM,CAACD,aAAa,CAAC;IACrD;EACF;EACA,OAAOL,aAAa;AACtB;AAEO,SAAShC,sBAAsBA,CACpCX,eAAgC,EAChCkD,aAA4B,GAAG,IAAI,EACzB;EACV,MAAMP,aAAuB,GAAG,EAAE;EAElC,MAAMQ,uBAAuB,GAAGX,0BAA0B,CAACxC,eAAe,CAAC;EAC3E,KAAK,MAAMsB,YAAY,IAAI6B,uBAAuB,EAAE;IAClD,MAAMC,UAAU,GAAG/B,0BAA0B,CAACC,YAAY,CAAC;IAC3D,IAAIL,2BAA2B,CAACmC,UAAU,CAAC,IAAIA,UAAU,CAACnE,IAAI,EAAE;MAC9D,KAAK,MAAM;QAAEW,MAAM;QAAE2C;MAAK,CAAC,IAAIa,UAAU,CAACnE,IAAI,EAAE;QAC9C,IAAIiE,aAAa,KAAK,IAAI,IAAI,CAACX,IAAI,IAAIA,IAAI,KAAKW,aAAa,EAAE;UAC7DP,aAAa,CAACrC,IAAI,CAACV,MAAM,CAAC;QAC5B;MACF;IACF;EACF;EAEA,OAAO+C,aAAa;AACtB;AAEO,SAASnC,kCAAkCA,CAACR,eAAgC,EAAW;EAC5F,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,eAAe,CAACyC,QAAQ,CAACC,WAAW,CAAC,EAAE;IACxD,OAAO,KAAK;EACd;EAEA,KAAK,MAAMA,WAAW,IAAI1C,eAAe,CAACyC,QAAQ,CAACC,WAAW,EAAE;IAC9D,KAAK,MAAME,QAAQ,IAAIF,WAAW,CAACE,QAAQ,IAAI,EAAE,EAAE;MAAA,IAAAS,YAAA;MACjD,IAAI,CAAAT,QAAQ,aAARA,QAAQ,wBAAAS,YAAA,GAART,QAAQ,CAAEX,CAAC,cAAAoB,YAAA,uBAAXA,YAAA,CAAc,oBAAoB,CAAC,MAAK,YAAY,EAAE;QACxD,KAAK,MAAM/B,YAAY,IAAIsB,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE;UAC1D;UACA,MAAMQ,UAAU,GAAG/B,0BAA0B,CAACC,YAAY,CAAC;UAC3D,IAAIL,2BAA2B,CAACmC,UAAU,CAAC,EAAE;YAC3C,OAAO,IAAI;UACb;QACF;QACA,IAAI,CAACR,QAAQ,CAAC,eAAe,CAAC,EAAE;UAC9BA,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE;QAChC;QAEAA,QAAQ,CAAC,eAAe,CAAC,CAACtC,IAAI,CAAC;UAC7BwB,MAAM,EAAE,CAAC;YAAEG,CAAC,EAAE;cAAE,cAAc,EAAE;YAA6B;UAAE,CAAC,CAAC;UACjEC,QAAQ,EAAE,CACR;YAAED,CAAC,EAAE;cAAE,cAAc,EAAE;YAAkC;UAAE,CAAC,EAC5D;YAAEA,CAAC,EAAE;cAAE,cAAc,EAAE;YAAoC;UAAE,CAAC;QAElE,CAAC,CAAC;QACF,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd;AAEO,SAASqB,SAASA,CAAC1D,MAAc,EAAEI,eAAgC,EAAW;EACnF,MAAMG,OAAO,GAAGQ,sBAAsB,CAACX,eAAe,CAAC;EACvD,OAAOG,OAAO,CAACiB,QAAQ,CAACxB,MAAM,CAAC;AACjC;AAEO,SAASoB,YAAYA,CAACpB,MAAc,EAAEI,eAAgC,EAAmB;EAC9F,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,eAAe,CAACyC,QAAQ,CAACC,WAAW,CAAC,EAAE;IACxD,OAAO1C,eAAe;EACxB;EAEA,KAAK,MAAM0C,WAAW,IAAI1C,eAAe,CAACyC,QAAQ,CAACC,WAAW,EAAE;IAC9D,KAAK,MAAME,QAAQ,IAAIF,WAAW,CAACE,QAAQ,IAAI,EAAE,EAAE;MAAA,IAAAW,YAAA;MACjD,IAAI,CAAAX,QAAQ,aAARA,QAAQ,wBAAAW,YAAA,GAARX,QAAQ,CAAEX,CAAC,cAAAsB,YAAA,uBAAXA,YAAA,CAAc,oBAAoB,CAAC,MAAK,YAAY,EAAE;QACxD,KAAK,MAAMjC,YAAY,IAAIsB,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE;UAC1D,MAAMQ,UAAU,GAAG/B,0BAA0B,CAACC,YAAY,CAAC;UAC3D,IAAIL,2BAA2B,CAACmC,UAAU,CAAC,EAAE;YAC3C,IAAI,CAAC9B,YAAY,CAACrC,IAAI,EAAEqC,YAAY,CAACrC,IAAI,GAAG,EAAE;YAC9CqC,YAAY,CAACrC,IAAI,CAACqB,IAAI,CAAC;cACrB2B,CAAC,EAAE;gBAAE,gBAAgB,EAAErC;cAAO;YAChC,CAAC,CAAC;UACJ;QACF;QACA;MACF;IACF;EACF;EACA,OAAOI,eAAe;AACxB;AAEO,SAASwD,YAAYA,CAAC5D,MAAc,EAAEI,eAAgC,EAAmB;EAC9F,IAAI,CAACN,KAAK,CAACC,OAAO,CAACK,eAAe,CAACyC,QAAQ,CAACC,WAAW,CAAC,EAAE;IACxD,OAAO1C,eAAe;EACxB;EAEA,KAAK,MAAM0C,WAAW,IAAI1C,eAAe,CAACyC,QAAQ,CAACC,WAAW,EAAE;IAC9D,KAAK,MAAME,QAAQ,IAAIF,WAAW,CAACE,QAAQ,IAAI,EAAE,EAAE;MAAA,IAAAa,YAAA;MACjD,IAAI,CAAAb,QAAQ,aAARA,QAAQ,wBAAAa,YAAA,GAARb,QAAQ,CAAEX,CAAC,cAAAwB,YAAA,uBAAXA,YAAA,CAAc,oBAAoB,CAAC,MAAK,YAAY,EAAE;QACxD,KAAK,MAAMnC,YAAY,IAAIsB,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE;UAC1D;UACA,MAAMQ,UAAU,GAAG/B,0BAA0B,CAACC,YAAY,CAAC;UAC3D,IAAIL,2BAA2B,CAACmC,UAAU,CAAC,EAAE;YAC3C,KAAK,MAAMM,OAAO,IAAI,CAAApC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAErC,IAAI,KAAI,EAAE,EAAE;cAAA,IAAA0E,mBAAA,EAAAC,QAAA;cAC9C,MAAM3E,IAAI,IAAA0E,mBAAA,GAAGrC,YAAY,CAACrC,IAAI,cAAA0E,mBAAA,uBAAjBA,mBAAA,CAAoBD,OAAO,CAAC;cACzC,IAAI,CAAAzE,IAAI,aAAJA,IAAI,wBAAA2E,QAAA,GAAJ3E,IAAI,CAAEgD,CAAC,cAAA2B,QAAA,uBAAPA,QAAA,CAAU,gBAAgB,CAAC,MAAKhE,MAAM,EAAE;gBAAA,IAAAiE,mBAAA;gBAC1C,CAAAA,mBAAA,GAAOvC,YAAY,CAACrC,IAAI,cAAA4E,mBAAA,qBAAxB,OAAOA,mBAAA,CAAoBH,OAAO,CAAC;cACrC;YACF;UACF;QACF;QACA;MACF;IACF;EACF;EAEA,OAAO1D,eAAe;AACxB"}