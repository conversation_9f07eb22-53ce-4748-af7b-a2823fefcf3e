{"version": 3, "file": "Permissions.js", "names": ["_androidPlugins", "data", "require", "_Manifest", "USES_PERMISSION", "withPermissions", "config", "permissions", "Array", "isArray", "filter", "Boolean", "android", "Set", "concat", "withAndroidManifest", "modResults", "setAndroidPermissions", "exports", "withBlockedPermissions", "_config$android", "resolvedPermissions", "prefixAndroidPermissionsIfNecessary", "permission", "includes", "ensureToolsAvailable", "addBlockedPermissions", "withInternalBlockedPermissions", "_config$android2", "_config$android2$bloc", "blockedPermissions", "length", "androidManifest", "manifest", "ensureBlockedPermission", "manifestPermissions", "e", "$", "push", "map", "getAndroidPermissions", "_config$android$permi", "_config$android3", "_androidManifest$mani", "providedPermissions", "permissionsToAdd", "hasOwnProperty", "for<PERSON>ach", "isPermissionAlreadyRequested", "addPermissionToManifest", "some", "removePermissions", "permissionNames", "targetNames", "ensurePermissionNameFormat", "nextPermissions", "attribute", "value", "name", "addPermission", "permissionName", "usesPermissions", "ensurePermissions", "getPermissions", "results", "targetName", "ensurePermission", "com", "split", "pop", "toUpperCase", "join", "permissionObject"], "sources": ["../../src/android/Permissions.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAndroidManifest } from '../plugins/android-plugins';\nimport { AndroidManifest, ensureToolsAvailable, ManifestUsesPermission } from './Manifest';\n\nconst USES_PERMISSION = 'uses-permission';\n\nexport const withPermissions: ConfigPlugin<string[] | void> = (config, permissions) => {\n  if (Array.isArray(permissions)) {\n    permissions = permissions.filter(Boolean);\n    if (!config.android) config.android = {};\n    if (!config.android.permissions) config.android.permissions = [];\n    config.android.permissions = [\n      // @ts-ignore\n      ...new Set(config.android.permissions.concat(permissions)),\n    ];\n  }\n  return withAndroidManifest(config, async (config) => {\n    config.modResults = await setAndroidPermissions(config, config.modResults);\n    return config;\n  });\n};\n\n/** Given a permission or list of permissions, block permissions in the final `AndroidManifest.xml` to ensure no installed library or plugin can add them. */\nexport const withBlockedPermissions: ConfigPlugin<string[] | string> = (config, permissions) => {\n  const resolvedPermissions = prefixAndroidPermissionsIfNecessary(\n    (Array.isArray(permissions) ? permissions : [permissions]).filter(Boolean)\n  );\n\n  if (config?.android?.permissions && Array.isArray(config.android.permissions)) {\n    // Remove any static config permissions\n    config.android.permissions = prefixAndroidPermissionsIfNecessary(\n      config.android.permissions\n    ).filter((permission) => !resolvedPermissions.includes(permission));\n  }\n\n  return withAndroidManifest(config, async (config) => {\n    config.modResults = ensureToolsAvailable(config.modResults);\n    config.modResults = addBlockedPermissions(config.modResults, resolvedPermissions);\n    return config;\n  });\n};\n\nexport const withInternalBlockedPermissions: ConfigPlugin = (config) => {\n  // Only add permissions if the user defined the property and added some values\n  // this ensures we don't add the `tools:*` namespace extraneously.\n  if (config.android?.blockedPermissions?.length) {\n    return withBlockedPermissions(config, config.android.blockedPermissions);\n  }\n\n  return config;\n};\n\nexport function addBlockedPermissions(androidManifest: AndroidManifest, permissions: string[]) {\n  if (!Array.isArray(androidManifest.manifest['uses-permission'])) {\n    androidManifest.manifest['uses-permission'] = [];\n  }\n\n  for (const permission of prefixAndroidPermissionsIfNecessary(permissions)) {\n    androidManifest.manifest['uses-permission'] = ensureBlockedPermission(\n      androidManifest.manifest['uses-permission'],\n      permission\n    );\n  }\n\n  return androidManifest;\n}\n\n/**\n * Filter any existing permissions matching the provided permission name, then add a\n * restricted permission to overwrite any extra permissions that may be added in a\n * third-party package's AndroidManifest.xml.\n *\n * @param manifestPermissions manifest `uses-permissions` array.\n * @param permission `android:name` of the permission to restrict\n * @returns\n */\nfunction ensureBlockedPermission(\n  manifestPermissions: ManifestUsesPermission[],\n  permission: string\n) {\n  // Remove permission if it currently exists\n  manifestPermissions = manifestPermissions.filter((e) => e.$['android:name'] !== permission);\n\n  // Add a permission with tools:node to overwrite any existing permission and ensure it's removed upon building.\n  manifestPermissions.push({\n    $: { 'android:name': permission, 'tools:node': 'remove' },\n  });\n  return manifestPermissions;\n}\n\nfunction prefixAndroidPermissionsIfNecessary(permissions: string[]): string[] {\n  return permissions.map((permission) => {\n    if (!permission.includes('.')) {\n      return `android.permission.${permission}`;\n    }\n    return permission;\n  });\n}\n\nexport function getAndroidPermissions(config: Pick<ExpoConfig, 'android'>): string[] {\n  return config.android?.permissions ?? [];\n}\n\nexport function setAndroidPermissions(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const permissions = getAndroidPermissions(config);\n  const providedPermissions = prefixAndroidPermissionsIfNecessary(permissions);\n  const permissionsToAdd = [...providedPermissions];\n\n  if (!androidManifest.manifest.hasOwnProperty('uses-permission')) {\n    androidManifest.manifest['uses-permission'] = [];\n  }\n  // manifest.manifest['uses-permission'] = [];\n\n  const manifestPermissions = androidManifest.manifest['uses-permission'] ?? [];\n\n  permissionsToAdd.forEach((permission) => {\n    if (!isPermissionAlreadyRequested(permission, manifestPermissions)) {\n      addPermissionToManifest(permission, manifestPermissions);\n    }\n  });\n\n  return androidManifest;\n}\n\nexport function isPermissionAlreadyRequested(\n  permission: string,\n  manifestPermissions: ManifestUsesPermission[]\n): boolean {\n  return manifestPermissions.some((e) => e.$['android:name'] === permission);\n}\n\nexport function addPermissionToManifest(\n  permission: string,\n  manifestPermissions: ManifestUsesPermission[]\n) {\n  manifestPermissions.push({ $: { 'android:name': permission } });\n  return manifestPermissions;\n}\n\nexport function removePermissions(androidManifest: AndroidManifest, permissionNames?: string[]) {\n  const targetNames = permissionNames ? permissionNames.map(ensurePermissionNameFormat) : null;\n  const permissions = androidManifest.manifest[USES_PERMISSION] || [];\n  const nextPermissions = [];\n  for (const attribute of permissions) {\n    if (targetNames) {\n      // @ts-ignore: name isn't part of the type\n      const value = attribute.$['android:name'] || attribute.$.name;\n      if (!targetNames.includes(value)) {\n        nextPermissions.push(attribute);\n      }\n    }\n  }\n\n  androidManifest.manifest[USES_PERMISSION] = nextPermissions;\n}\n\nexport function addPermission(androidManifest: AndroidManifest, permissionName: string): void {\n  const usesPermissions: ManifestUsesPermission[] = androidManifest.manifest[USES_PERMISSION] || [];\n  usesPermissions.push({\n    $: { 'android:name': permissionName },\n  });\n  androidManifest.manifest[USES_PERMISSION] = usesPermissions;\n}\n\nexport function ensurePermissions(\n  androidManifest: AndroidManifest,\n  permissionNames: string[]\n): { [permission: string]: boolean } {\n  const permissions = getPermissions(androidManifest);\n\n  const results: { [permission: string]: boolean } = {};\n  for (const permissionName of permissionNames) {\n    const targetName = ensurePermissionNameFormat(permissionName);\n    if (!permissions.includes(targetName)) {\n      addPermission(androidManifest, targetName);\n      results[permissionName] = true;\n    } else {\n      results[permissionName] = false;\n    }\n  }\n  return results;\n}\n\nexport function ensurePermission(\n  androidManifest: AndroidManifest,\n  permissionName: string\n): boolean {\n  const permissions = getPermissions(androidManifest);\n  const targetName = ensurePermissionNameFormat(permissionName);\n\n  if (!permissions.includes(targetName)) {\n    addPermission(androidManifest, targetName);\n    return true;\n  }\n  return false;\n}\n\nexport function ensurePermissionNameFormat(permissionName: string): string {\n  if (permissionName.includes('.')) {\n    const com = permissionName.split('.');\n    const name = com.pop() as string;\n    return [...com, name.toUpperCase()].join('.');\n  } else {\n    // If shorthand form like `WRITE_CONTACTS` is provided, expand it to `android.permission.WRITE_CONTACTS`.\n    return ensurePermissionNameFormat(`android.permission.${permissionName}`);\n  }\n}\n\nexport function getPermissions(androidManifest: AndroidManifest): string[] {\n  const usesPermissions: { [key: string]: any }[] = androidManifest.manifest[USES_PERMISSION] || [];\n  const permissions = usesPermissions.map((permissionObject) => {\n    return permissionObject.$['android:name'] || permissionObject.$.name;\n  });\n  return permissions;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAGA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMG,eAAe,GAAG,iBAAiB;AAElC,MAAMC,eAA8C,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAK;EACrF,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;IAC9BA,WAAW,GAAGA,WAAW,CAACG,MAAM,CAACC,OAAO,CAAC;IACzC,IAAI,CAACL,MAAM,CAACM,OAAO,EAAEN,MAAM,CAACM,OAAO,GAAG,CAAC,CAAC;IACxC,IAAI,CAACN,MAAM,CAACM,OAAO,CAACL,WAAW,EAAED,MAAM,CAACM,OAAO,CAACL,WAAW,GAAG,EAAE;IAChED,MAAM,CAACM,OAAO,CAACL,WAAW,GAAG;IAC3B;IACA,GAAG,IAAIM,GAAG,CAACP,MAAM,CAACM,OAAO,CAACL,WAAW,CAACO,MAAM,CAACP,WAAW,CAAC,CAAC,CAC3D;EACH;EACA,OAAO,IAAAQ,qCAAmB,EAACT,MAAM,EAAE,MAAOA,MAAM,IAAK;IACnDA,MAAM,CAACU,UAAU,GAAG,MAAMC,qBAAqB,CAACX,MAAM,EAAEA,MAAM,CAACU,UAAU,CAAC;IAC1E,OAAOV,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAY,OAAA,CAAAb,eAAA,GAAAA,eAAA;AACO,MAAMc,sBAAuD,GAAGA,CAACb,MAAM,EAAEC,WAAW,KAAK;EAAA,IAAAa,eAAA;EAC9F,MAAMC,mBAAmB,GAAGC,mCAAmC,CAC7D,CAACd,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC,EAAEG,MAAM,CAACC,OAAO,CAAC,CAC3E;EAED,IAAIL,MAAM,aAANA,MAAM,gBAAAc,eAAA,GAANd,MAAM,CAAEM,OAAO,cAAAQ,eAAA,eAAfA,eAAA,CAAiBb,WAAW,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAACM,OAAO,CAACL,WAAW,CAAC,EAAE;IAC7E;IACAD,MAAM,CAACM,OAAO,CAACL,WAAW,GAAGe,mCAAmC,CAC9DhB,MAAM,CAACM,OAAO,CAACL,WAAW,CAC3B,CAACG,MAAM,CAAEa,UAAU,IAAK,CAACF,mBAAmB,CAACG,QAAQ,CAACD,UAAU,CAAC,CAAC;EACrE;EAEA,OAAO,IAAAR,qCAAmB,EAACT,MAAM,EAAE,MAAOA,MAAM,IAAK;IACnDA,MAAM,CAACU,UAAU,GAAG,IAAAS,gCAAoB,EAACnB,MAAM,CAACU,UAAU,CAAC;IAC3DV,MAAM,CAACU,UAAU,GAAGU,qBAAqB,CAACpB,MAAM,CAACU,UAAU,EAAEK,mBAAmB,CAAC;IACjF,OAAOf,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACY,OAAA,CAAAC,sBAAA,GAAAA,sBAAA;AAEK,MAAMQ,8BAA4C,GAAIrB,MAAM,IAAK;EAAA,IAAAsB,gBAAA,EAAAC,qBAAA;EACtE;EACA;EACA,KAAAD,gBAAA,GAAItB,MAAM,CAACM,OAAO,cAAAgB,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBE,kBAAkB,cAAAD,qBAAA,eAAlCA,qBAAA,CAAoCE,MAAM,EAAE;IAC9C,OAAOZ,sBAAsB,CAACb,MAAM,EAAEA,MAAM,CAACM,OAAO,CAACkB,kBAAkB,CAAC;EAC1E;EAEA,OAAOxB,MAAM;AACf,CAAC;AAACY,OAAA,CAAAS,8BAAA,GAAAA,8BAAA;AAEK,SAASD,qBAAqBA,CAACM,eAAgC,EAAEzB,WAAqB,EAAE;EAC7F,IAAI,CAACC,KAAK,CAACC,OAAO,CAACuB,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;IAC/DD,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE;EAClD;EAEA,KAAK,MAAMV,UAAU,IAAID,mCAAmC,CAACf,WAAW,CAAC,EAAE;IACzEyB,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,GAAGC,uBAAuB,CACnEF,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAC3CV,UAAU,CACX;EACH;EAEA,OAAOS,eAAe;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAC9BC,mBAA6C,EAC7CZ,UAAkB,EAClB;EACA;EACAY,mBAAmB,GAAGA,mBAAmB,CAACzB,MAAM,CAAE0B,CAAC,IAAKA,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,KAAKd,UAAU,CAAC;;EAE3F;EACAY,mBAAmB,CAACG,IAAI,CAAC;IACvBD,CAAC,EAAE;MAAE,cAAc,EAAEd,UAAU;MAAE,YAAY,EAAE;IAAS;EAC1D,CAAC,CAAC;EACF,OAAOY,mBAAmB;AAC5B;AAEA,SAASb,mCAAmCA,CAACf,WAAqB,EAAY;EAC5E,OAAOA,WAAW,CAACgC,GAAG,CAAEhB,UAAU,IAAK;IACrC,IAAI,CAACA,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7B,OAAQ,sBAAqBD,UAAW,EAAC;IAC3C;IACA,OAAOA,UAAU;EACnB,CAAC,CAAC;AACJ;AAEO,SAASiB,qBAAqBA,CAAClC,MAAmC,EAAY;EAAA,IAAAmC,qBAAA,EAAAC,gBAAA;EACnF,QAAAD,qBAAA,IAAAC,gBAAA,GAAOpC,MAAM,CAACM,OAAO,cAAA8B,gBAAA,uBAAdA,gBAAA,CAAgBnC,WAAW,cAAAkC,qBAAA,cAAAA,qBAAA,GAAI,EAAE;AAC1C;AAEO,SAASxB,qBAAqBA,CACnCX,MAAmC,EACnC0B,eAAgC,EAChC;EAAA,IAAAW,qBAAA;EACA,MAAMpC,WAAW,GAAGiC,qBAAqB,CAAClC,MAAM,CAAC;EACjD,MAAMsC,mBAAmB,GAAGtB,mCAAmC,CAACf,WAAW,CAAC;EAC5E,MAAMsC,gBAAgB,GAAG,CAAC,GAAGD,mBAAmB,CAAC;EAEjD,IAAI,CAACZ,eAAe,CAACC,QAAQ,CAACa,cAAc,CAAC,iBAAiB,CAAC,EAAE;IAC/Dd,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE;EAClD;EACA;;EAEA,MAAME,mBAAmB,IAAAQ,qBAAA,GAAGX,eAAe,CAACC,QAAQ,CAAC,iBAAiB,CAAC,cAAAU,qBAAA,cAAAA,qBAAA,GAAI,EAAE;EAE7EE,gBAAgB,CAACE,OAAO,CAAExB,UAAU,IAAK;IACvC,IAAI,CAACyB,4BAA4B,CAACzB,UAAU,EAAEY,mBAAmB,CAAC,EAAE;MAClEc,uBAAuB,CAAC1B,UAAU,EAAEY,mBAAmB,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF,OAAOH,eAAe;AACxB;AAEO,SAASgB,4BAA4BA,CAC1CzB,UAAkB,EAClBY,mBAA6C,EACpC;EACT,OAAOA,mBAAmB,CAACe,IAAI,CAAEd,CAAC,IAAKA,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,KAAKd,UAAU,CAAC;AAC5E;AAEO,SAAS0B,uBAAuBA,CACrC1B,UAAkB,EAClBY,mBAA6C,EAC7C;EACAA,mBAAmB,CAACG,IAAI,CAAC;IAAED,CAAC,EAAE;MAAE,cAAc,EAAEd;IAAW;EAAE,CAAC,CAAC;EAC/D,OAAOY,mBAAmB;AAC5B;AAEO,SAASgB,iBAAiBA,CAACnB,eAAgC,EAAEoB,eAA0B,EAAE;EAC9F,MAAMC,WAAW,GAAGD,eAAe,GAAGA,eAAe,CAACb,GAAG,CAACe,0BAA0B,CAAC,GAAG,IAAI;EAC5F,MAAM/C,WAAW,GAAGyB,eAAe,CAACC,QAAQ,CAAC7B,eAAe,CAAC,IAAI,EAAE;EACnE,MAAMmD,eAAe,GAAG,EAAE;EAC1B,KAAK,MAAMC,SAAS,IAAIjD,WAAW,EAAE;IACnC,IAAI8C,WAAW,EAAE;MACf;MACA,MAAMI,KAAK,GAAGD,SAAS,CAACnB,CAAC,CAAC,cAAc,CAAC,IAAImB,SAAS,CAACnB,CAAC,CAACqB,IAAI;MAC7D,IAAI,CAACL,WAAW,CAAC7B,QAAQ,CAACiC,KAAK,CAAC,EAAE;QAChCF,eAAe,CAACjB,IAAI,CAACkB,SAAS,CAAC;MACjC;IACF;EACF;EAEAxB,eAAe,CAACC,QAAQ,CAAC7B,eAAe,CAAC,GAAGmD,eAAe;AAC7D;AAEO,SAASI,aAAaA,CAAC3B,eAAgC,EAAE4B,cAAsB,EAAQ;EAC5F,MAAMC,eAAyC,GAAG7B,eAAe,CAACC,QAAQ,CAAC7B,eAAe,CAAC,IAAI,EAAE;EACjGyD,eAAe,CAACvB,IAAI,CAAC;IACnBD,CAAC,EAAE;MAAE,cAAc,EAAEuB;IAAe;EACtC,CAAC,CAAC;EACF5B,eAAe,CAACC,QAAQ,CAAC7B,eAAe,CAAC,GAAGyD,eAAe;AAC7D;AAEO,SAASC,iBAAiBA,CAC/B9B,eAAgC,EAChCoB,eAAyB,EACU;EACnC,MAAM7C,WAAW,GAAGwD,cAAc,CAAC/B,eAAe,CAAC;EAEnD,MAAMgC,OAA0C,GAAG,CAAC,CAAC;EACrD,KAAK,MAAMJ,cAAc,IAAIR,eAAe,EAAE;IAC5C,MAAMa,UAAU,GAAGX,0BAA0B,CAACM,cAAc,CAAC;IAC7D,IAAI,CAACrD,WAAW,CAACiB,QAAQ,CAACyC,UAAU,CAAC,EAAE;MACrCN,aAAa,CAAC3B,eAAe,EAAEiC,UAAU,CAAC;MAC1CD,OAAO,CAACJ,cAAc,CAAC,GAAG,IAAI;IAChC,CAAC,MAAM;MACLI,OAAO,CAACJ,cAAc,CAAC,GAAG,KAAK;IACjC;EACF;EACA,OAAOI,OAAO;AAChB;AAEO,SAASE,gBAAgBA,CAC9BlC,eAAgC,EAChC4B,cAAsB,EACb;EACT,MAAMrD,WAAW,GAAGwD,cAAc,CAAC/B,eAAe,CAAC;EACnD,MAAMiC,UAAU,GAAGX,0BAA0B,CAACM,cAAc,CAAC;EAE7D,IAAI,CAACrD,WAAW,CAACiB,QAAQ,CAACyC,UAAU,CAAC,EAAE;IACrCN,aAAa,CAAC3B,eAAe,EAAEiC,UAAU,CAAC;IAC1C,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;AAEO,SAASX,0BAA0BA,CAACM,cAAsB,EAAU;EACzE,IAAIA,cAAc,CAACpC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAChC,MAAM2C,GAAG,GAAGP,cAAc,CAACQ,KAAK,CAAC,GAAG,CAAC;IACrC,MAAMV,IAAI,GAAGS,GAAG,CAACE,GAAG,EAAY;IAChC,OAAO,CAAC,GAAGF,GAAG,EAAET,IAAI,CAACY,WAAW,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAC/C,CAAC,MAAM;IACL;IACA,OAAOjB,0BAA0B,CAAE,sBAAqBM,cAAe,EAAC,CAAC;EAC3E;AACF;AAEO,SAASG,cAAcA,CAAC/B,eAAgC,EAAY;EACzE,MAAM6B,eAAyC,GAAG7B,eAAe,CAACC,QAAQ,CAAC7B,eAAe,CAAC,IAAI,EAAE;EACjG,MAAMG,WAAW,GAAGsD,eAAe,CAACtB,GAAG,CAAEiC,gBAAgB,IAAK;IAC5D,OAAOA,gBAAgB,CAACnC,CAAC,CAAC,cAAc,CAAC,IAAImC,gBAAgB,CAACnC,CAAC,CAACqB,IAAI;EACtE,CAAC,CAAC;EACF,OAAOnD,WAAW;AACpB"}