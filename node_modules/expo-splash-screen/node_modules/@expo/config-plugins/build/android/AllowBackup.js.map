{"version": 3, "file": "AllowBackup.js", "names": ["_androidPlugins", "data", "require", "_Manifest", "withA<PERSON>Backup", "createAndroidManifestPlugin", "setAllowBackup", "exports", "getAllowBackup", "config", "_config$android$allow", "_config$android", "android", "allowBackup", "androidManifest", "mainApplication", "getMainApplication", "$", "String", "getAllowBackupFromManifest"], "sources": ["../../src/android/AllowBackup.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\nimport { AndroidManifest, getMainApplication, StringBoolean } from './Manifest';\n\nexport const withAllowBackup = createAndroidManifestPlugin(setAllowBackup, 'withAllowBackup');\n\nexport function getAllowBackup(config: Pick<ExpoConfig, 'android'>) {\n  // Defaults to true.\n  // https://docs.expo.dev/versions/latest/config/app/#allowbackup\n  return config.android?.allowBackup ?? true;\n}\n\nexport function setAllowBackup(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n) {\n  const allowBackup = getAllowBackup(config);\n\n  const mainApplication = getMainApplication(androidManifest);\n  if (mainApplication?.$) {\n    mainApplication.$['android:allowBackup'] = String(allowBackup) as StringBoolean;\n  }\n\n  return androidManifest;\n}\n\nexport function getAllowBackupFromManifest(androidManifest: AndroidManifest): boolean | null {\n  const mainApplication = getMainApplication(androidManifest);\n\n  if (mainApplication?.$) {\n    return String(mainApplication.$['android:allowBackup']) === 'true';\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,eAAe,GAAG,IAAAC,6CAA2B,EAACC,cAAc,EAAE,iBAAiB,CAAC;AAACC,OAAA,CAAAH,eAAA,GAAAA,eAAA;AAEvF,SAASI,cAAcA,CAACC,MAAmC,EAAE;EAAA,IAAAC,qBAAA,EAAAC,eAAA;EAClE;EACA;EACA,QAAAD,qBAAA,IAAAC,eAAA,GAAOF,MAAM,CAACG,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,WAAW,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AAC5C;AAEO,SAASJ,cAAcA,CAC5BG,MAAmC,EACnCK,eAAgC,EAChC;EACA,MAAMD,WAAW,GAAGL,cAAc,CAACC,MAAM,CAAC;EAE1C,MAAMM,eAAe,GAAG,IAAAC,8BAAkB,EAACF,eAAe,CAAC;EAC3D,IAAIC,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEE,CAAC,EAAE;IACtBF,eAAe,CAACE,CAAC,CAAC,qBAAqB,CAAC,GAAGC,MAAM,CAACL,WAAW,CAAkB;EACjF;EAEA,OAAOC,eAAe;AACxB;AAEO,SAASK,0BAA0BA,CAACL,eAAgC,EAAkB;EAC3F,MAAMC,eAAe,GAAG,IAAAC,8BAAkB,EAACF,eAAe,CAAC;EAE3D,IAAIC,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEE,CAAC,EAAE;IACtB,OAAOC,MAAM,CAACH,eAAe,CAACE,CAAC,CAAC,qBAAqB,CAAC,CAAC,KAAK,MAAM;EACpE;EAEA,OAAO,IAAI;AACb"}