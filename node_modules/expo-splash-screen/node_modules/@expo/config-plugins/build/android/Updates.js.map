{"version": 3, "file": "Updates.js", "names": ["_path", "data", "_interopRequireDefault", "require", "_resolveFrom", "_androidPlugins", "_withPlugins", "_Updates", "_Manifest", "_Resources", "_Strings", "obj", "__esModule", "default", "CREATE_MANIFEST_ANDROID_PATH", "Config", "exports", "withUpdates", "config", "expoUsername", "with<PERSON><PERSON><PERSON>", "withUpdatesManifest", "withRuntimeVersionResource", "withAndroidManifest", "projectRoot", "modRequest", "expoUpdatesPackageVersion", "getExpoUpdatesPackageVersion", "modResults", "setUpdatesConfig", "createStringsXmlPlugin", "applyRuntimeVersionFromConfig", "stringsJSON", "runtimeVersion", "getRuntimeVersionNullable", "setStringItem", "buildResourceItem", "name", "value", "removeStringItem", "androidManifest", "username", "mainApplication", "getMainApplicationOrThrow", "addMetaDataItemToMainApplication", "ENABLED", "String", "getUpdatesEnabled", "CHECK_ON_LAUNCH", "getUpdatesCheckOnLaunch", "LAUNCH_WAIT_MS", "getUpdatesTimeout", "updateUrl", "getUpdateUrl", "UPDATE_URL", "removeMetaDataItemFromMainApplication", "codeSigningCertificate", "getUpdatesCodeSigningCertificate", "CODE_SIGNING_CERTIFICATE", "codeSigningMetadata", "getUpdatesCodeSigningMetadataStringified", "CODE_SIGNING_METADATA", "requestHeaders", "getUpdatesRequestHeadersStringified", "UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY", "setVersionsConfig", "findMetaDataItem", "RUNTIME_VERSION", "Error", "sdkVersion", "getSDKVersion", "SDK_VERSION", "ensureBuildGradleContainsConfigurationScript", "buildGradleContents", "isBuildGradleConfigured", "cleanedUpBuildGradleContents", "isBuildGradleMisconfigured", "split", "some", "line", "includes", "replace", "RegExp", "gradleScriptApply", "formatApplyLineForBuildGradle", "updatesGradleScriptPath", "resolveFrom", "silent", "relativePath", "path", "relative", "join", "posixPath", "process", "platform", "androidBuildScript", "isMainApplicationMetaDataSet", "getMainApplicationMetaDataValue", "Boolean", "isMainApplicationMetaDataSynced", "areVersionsSynced", "expectedRuntimeVersion", "expectedSdkVersion", "currentRuntimeVersion", "currentSdkVersion"], "sources": ["../../src/android/Updates.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { createStringsXmlPlugin, withAndroidManifest } from '../plugins/android-plugins';\nimport { withPlugins } from '../plugins/withPlugins';\nimport {\n  ExpoConfigUpdates,\n  getExpoUpdatesPackageVersion,\n  getRuntimeVersionNullable,\n  getSDKVersion,\n  getUpdatesCheckOnLaunch,\n  getUpdatesCodeSigningCertificate,\n  getUpdatesCodeSigningMetadataStringified,\n  getUpdatesRequestHeadersStringified,\n  getUpdatesEnabled,\n  getUpdatesTimeout,\n  getUpdateUrl,\n} from '../utils/Updates';\nimport {\n  addMetaDataItemToMainApplication,\n  AndroidManifest,\n  findMetaDataItem,\n  getMainApplicationMetaDataValue,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n} from './Manifest';\nimport { buildResourceItem, ResourceXML } from './Resources';\nimport { removeStringItem, setStringItem } from './Strings';\n\nconst CREATE_MANIFEST_ANDROID_PATH = 'expo-updates/scripts/create-manifest-android.gradle';\n\nexport enum Config {\n  ENABLED = 'expo.modules.updates.ENABLED',\n  CHECK_ON_LAUNCH = 'expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH',\n  LAUNCH_WAIT_MS = 'expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS',\n  SDK_VERSION = 'expo.modules.updates.EXPO_SDK_VERSION',\n  RUNTIME_VERSION = 'expo.modules.updates.EXPO_RUNTIME_VERSION',\n  UPDATE_URL = 'expo.modules.updates.EXPO_UPDATE_URL',\n  RELEASE_CHANNEL = 'expo.modules.updates.EXPO_RELEASE_CHANNEL',\n  UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY = 'expo.modules.updates.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY',\n  CODE_SIGNING_CERTIFICATE = 'expo.modules.updates.CODE_SIGNING_CERTIFICATE',\n  CODE_SIGNING_METADATA = 'expo.modules.updates.CODE_SIGNING_METADATA',\n}\n\n// when making changes to this config plugin, ensure the same changes are also made in eas-cli and build-tools\n// Also ensure the docs are up-to-date: https://docs.expo.dev/bare/installing-updates/\n\nexport const withUpdates: ConfigPlugin<{ expoUsername: string | null }> = (\n  config,\n  { expoUsername }\n) => {\n  return withPlugins(config, [[withUpdatesManifest, { expoUsername }], withRuntimeVersionResource]);\n};\n\nconst withUpdatesManifest: ConfigPlugin<{ expoUsername: string | null }> = (\n  config,\n  { expoUsername }\n) => {\n  return withAndroidManifest(config, (config) => {\n    const projectRoot = config.modRequest.projectRoot;\n    const expoUpdatesPackageVersion = getExpoUpdatesPackageVersion(projectRoot);\n    config.modResults = setUpdatesConfig(\n      projectRoot,\n      config,\n      config.modResults,\n      expoUsername,\n      expoUpdatesPackageVersion\n    );\n    return config;\n  });\n};\n\nconst withRuntimeVersionResource = createStringsXmlPlugin(\n  applyRuntimeVersionFromConfig,\n  'withRuntimeVersionResource'\n);\n\nexport function applyRuntimeVersionFromConfig(\n  config: Pick<ExpoConfigUpdates, 'sdkVersion' | 'runtimeVersion'>,\n  stringsJSON: ResourceXML\n): ResourceXML {\n  const runtimeVersion = getRuntimeVersionNullable(config, 'android');\n  if (runtimeVersion) {\n    return setStringItem(\n      [buildResourceItem({ name: 'expo_runtime_version', value: runtimeVersion })],\n      stringsJSON\n    );\n  }\n  return removeStringItem('expo_runtime_version', stringsJSON);\n}\n\nexport function setUpdatesConfig(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  androidManifest: AndroidManifest,\n  username: string | null,\n  expoUpdatesPackageVersion?: string | null\n): AndroidManifest {\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  addMetaDataItemToMainApplication(\n    mainApplication,\n    Config.ENABLED,\n    String(getUpdatesEnabled(config, username))\n  );\n  addMetaDataItemToMainApplication(\n    mainApplication,\n    Config.CHECK_ON_LAUNCH,\n    getUpdatesCheckOnLaunch(config, expoUpdatesPackageVersion)\n  );\n  addMetaDataItemToMainApplication(\n    mainApplication,\n    Config.LAUNCH_WAIT_MS,\n    String(getUpdatesTimeout(config))\n  );\n\n  const updateUrl = getUpdateUrl(config, username);\n  if (updateUrl) {\n    addMetaDataItemToMainApplication(mainApplication, Config.UPDATE_URL, updateUrl);\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.UPDATE_URL);\n  }\n\n  const codeSigningCertificate = getUpdatesCodeSigningCertificate(projectRoot, config);\n  if (codeSigningCertificate) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.CODE_SIGNING_CERTIFICATE,\n      codeSigningCertificate\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.CODE_SIGNING_CERTIFICATE);\n  }\n\n  const codeSigningMetadata = getUpdatesCodeSigningMetadataStringified(config);\n  if (codeSigningMetadata) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.CODE_SIGNING_METADATA,\n      codeSigningMetadata\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.CODE_SIGNING_METADATA);\n  }\n\n  const requestHeaders = getUpdatesRequestHeadersStringified(config);\n  if (requestHeaders) {\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY,\n      requestHeaders\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(\n      mainApplication,\n      Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY\n    );\n  }\n\n  return setVersionsConfig(config, androidManifest);\n}\n\nexport function setVersionsConfig(\n  config: Pick<ExpoConfigUpdates, 'sdkVersion' | 'runtimeVersion'>,\n  androidManifest: AndroidManifest\n): AndroidManifest {\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  const runtimeVersion = getRuntimeVersionNullable(config, 'android');\n  if (!runtimeVersion && findMetaDataItem(mainApplication, Config.RUNTIME_VERSION) > -1) {\n    throw new Error(\n      'A runtime version is set in your AndroidManifest.xml, but is missing from your app.json/app.config.js. Please either set runtimeVersion in your app.json/app.config.js or remove expo.modules.updates.EXPO_RUNTIME_VERSION from your AndroidManifest.xml.'\n    );\n  }\n  const sdkVersion = getSDKVersion(config);\n  if (runtimeVersion) {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.SDK_VERSION);\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      Config.RUNTIME_VERSION,\n      '@string/expo_runtime_version'\n    );\n  } else if (sdkVersion) {\n    /**\n     * runtime version maybe null in projects using classic updates. In that\n     * case we use SDK version\n     */\n    removeMetaDataItemFromMainApplication(mainApplication, Config.RUNTIME_VERSION);\n    addMetaDataItemToMainApplication(mainApplication, Config.SDK_VERSION, sdkVersion);\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, Config.RUNTIME_VERSION);\n    removeMetaDataItemFromMainApplication(mainApplication, Config.SDK_VERSION);\n  }\n\n  return androidManifest;\n}\nexport function ensureBuildGradleContainsConfigurationScript(\n  projectRoot: string,\n  buildGradleContents: string\n): string {\n  if (!isBuildGradleConfigured(projectRoot, buildGradleContents)) {\n    let cleanedUpBuildGradleContents;\n\n    const isBuildGradleMisconfigured = buildGradleContents\n      .split('\\n')\n      .some((line) => line.includes(CREATE_MANIFEST_ANDROID_PATH));\n    if (isBuildGradleMisconfigured) {\n      cleanedUpBuildGradleContents = buildGradleContents.replace(\n        new RegExp(`(\\n// Integration with Expo updates)?\\n.*${CREATE_MANIFEST_ANDROID_PATH}.*\\n`),\n        ''\n      );\n    } else {\n      cleanedUpBuildGradleContents = buildGradleContents;\n    }\n\n    const gradleScriptApply = formatApplyLineForBuildGradle(projectRoot);\n    return `${cleanedUpBuildGradleContents}\\n// Integration with Expo updates\\n${gradleScriptApply}\\n`;\n  } else {\n    return buildGradleContents;\n  }\n}\n\nexport function formatApplyLineForBuildGradle(projectRoot: string): string {\n  const updatesGradleScriptPath = resolveFrom.silent(projectRoot, CREATE_MANIFEST_ANDROID_PATH);\n\n  if (!updatesGradleScriptPath) {\n    throw new Error(\n      \"Could not find the build script for Android. This could happen in case of outdated 'node_modules'. Run 'npm install' to make sure that it's up-to-date.\"\n    );\n  }\n\n  const relativePath = path.relative(\n    path.join(projectRoot, 'android', 'app'),\n    updatesGradleScriptPath\n  );\n  const posixPath = process.platform === 'win32' ? relativePath.replace(/\\\\/g, '/') : relativePath;\n\n  return `apply from: \"${posixPath}\"`;\n}\n\nexport function isBuildGradleConfigured(projectRoot: string, buildGradleContents: string): boolean {\n  const androidBuildScript = formatApplyLineForBuildGradle(projectRoot);\n\n  return (\n    buildGradleContents\n      .replace(/\\r\\n/g, '\\n')\n      .split('\\n')\n      // Check for both single and double quotes\n      .some((line) => line === androidBuildScript || line === androidBuildScript.replace(/\"/g, \"'\"))\n  );\n}\n\nexport function isMainApplicationMetaDataSet(androidManifest: AndroidManifest): boolean {\n  const updateUrl = getMainApplicationMetaDataValue(androidManifest, Config.UPDATE_URL);\n  const runtimeVersion = getMainApplicationMetaDataValue(androidManifest, Config.RUNTIME_VERSION);\n  const sdkVersion = getMainApplicationMetaDataValue(androidManifest, Config.SDK_VERSION);\n\n  return Boolean(updateUrl && (sdkVersion || runtimeVersion));\n}\n\nexport function isMainApplicationMetaDataSynced(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  androidManifest: AndroidManifest,\n  username: string | null\n): boolean {\n  return (\n    getUpdateUrl(config, username) ===\n      getMainApplicationMetaDataValue(androidManifest, Config.UPDATE_URL) &&\n    String(getUpdatesEnabled(config, username)) ===\n      getMainApplicationMetaDataValue(androidManifest, Config.ENABLED) &&\n    String(getUpdatesTimeout(config)) ===\n      getMainApplicationMetaDataValue(androidManifest, Config.LAUNCH_WAIT_MS) &&\n    getUpdatesCheckOnLaunch(config) ===\n      getMainApplicationMetaDataValue(androidManifest, Config.CHECK_ON_LAUNCH) &&\n    getUpdatesCodeSigningCertificate(projectRoot, config) ===\n      getMainApplicationMetaDataValue(androidManifest, Config.CODE_SIGNING_CERTIFICATE) &&\n    getUpdatesCodeSigningMetadataStringified(config) ===\n      getMainApplicationMetaDataValue(androidManifest, Config.CODE_SIGNING_METADATA) &&\n    areVersionsSynced(config, androidManifest)\n  );\n}\n\nexport function areVersionsSynced(\n  config: Pick<ExpoConfigUpdates, 'runtimeVersion' | 'sdkVersion'>,\n  androidManifest: AndroidManifest\n): boolean {\n  const expectedRuntimeVersion = getRuntimeVersionNullable(config, 'android');\n  const expectedSdkVersion = getSDKVersion(config);\n\n  const currentRuntimeVersion = getMainApplicationMetaDataValue(\n    androidManifest,\n    Config.RUNTIME_VERSION\n  );\n  const currentSdkVersion = getMainApplicationMetaDataValue(androidManifest, Config.SDK_VERSION);\n\n  if (expectedRuntimeVersion !== null) {\n    return currentRuntimeVersion === expectedRuntimeVersion && currentSdkVersion === null;\n  } else if (expectedSdkVersion !== null) {\n    return currentSdkVersion === expectedSdkVersion && currentRuntimeVersion === null;\n  } else {\n    return true;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,aAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,YAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,gBAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,eAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,aAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,YAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,SAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAaA,SAAAO,UAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,SAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAQ,WAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,UAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,SAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,QAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4D,SAAAC,uBAAAS,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE5D,MAAMG,4BAA4B,GAAG,qDAAqD;AAAC,IAE/EC,MAAM,EAalB;AACA;AAAAC,OAAA,CAAAD,MAAA,GAAAA,MAAA;AAAA,WAdYA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;AAAA,GAANA,MAAM,KAAAC,OAAA,CAAAD,MAAA,GAANA,MAAM;AAgBX,MAAME,WAA0D,GAAGA,CACxEC,MAAM,EACN;EAAEC;AAAa,CAAC,KACb;EACH,OAAO,IAAAC,0BAAW,EAACF,MAAM,EAAE,CAAC,CAACG,mBAAmB,EAAE;IAAEF;EAAa,CAAC,CAAC,EAAEG,0BAA0B,CAAC,CAAC;AACnG,CAAC;AAACN,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAEF,MAAMI,mBAAkE,GAAGA,CACzEH,MAAM,EACN;EAAEC;AAAa,CAAC,KACb;EACH,OAAO,IAAAI,qCAAmB,EAACL,MAAM,EAAGA,MAAM,IAAK;IAC7C,MAAMM,WAAW,GAAGN,MAAM,CAACO,UAAU,CAACD,WAAW;IACjD,MAAME,yBAAyB,GAAG,IAAAC,uCAA4B,EAACH,WAAW,CAAC;IAC3EN,MAAM,CAACU,UAAU,GAAGC,gBAAgB,CAClCL,WAAW,EACXN,MAAM,EACNA,MAAM,CAACU,UAAU,EACjBT,YAAY,EACZO,yBAAyB,CAC1B;IACD,OAAOR,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAMI,0BAA0B,GAAG,IAAAQ,wCAAsB,EACvDC,6BAA6B,EAC7B,4BAA4B,CAC7B;AAEM,SAASA,6BAA6BA,CAC3Cb,MAAgE,EAChEc,WAAwB,EACX;EACb,MAAMC,cAAc,GAAG,IAAAC,oCAAyB,EAAChB,MAAM,EAAE,SAAS,CAAC;EACnE,IAAIe,cAAc,EAAE;IAClB,OAAO,IAAAE,wBAAa,EAClB,CAAC,IAAAC,8BAAiB,EAAC;MAAEC,IAAI,EAAE,sBAAsB;MAAEC,KAAK,EAAEL;IAAe,CAAC,CAAC,CAAC,EAC5ED,WAAW,CACZ;EACH;EACA,OAAO,IAAAO,2BAAgB,EAAC,sBAAsB,EAAEP,WAAW,CAAC;AAC9D;AAEO,SAASH,gBAAgBA,CAC9BL,WAAmB,EACnBN,MAAyB,EACzBsB,eAAgC,EAChCC,QAAuB,EACvBf,yBAAyC,EACxB;EACjB,MAAMgB,eAAe,GAAG,IAAAC,qCAAyB,EAACH,eAAe,CAAC;EAElE,IAAAI,4CAAgC,EAC9BF,eAAe,EACf3B,MAAM,CAAC8B,OAAO,EACdC,MAAM,CAAC,IAAAC,4BAAiB,EAAC7B,MAAM,EAAEuB,QAAQ,CAAC,CAAC,CAC5C;EACD,IAAAG,4CAAgC,EAC9BF,eAAe,EACf3B,MAAM,CAACiC,eAAe,EACtB,IAAAC,kCAAuB,EAAC/B,MAAM,EAAEQ,yBAAyB,CAAC,CAC3D;EACD,IAAAkB,4CAAgC,EAC9BF,eAAe,EACf3B,MAAM,CAACmC,cAAc,EACrBJ,MAAM,CAAC,IAAAK,4BAAiB,EAACjC,MAAM,CAAC,CAAC,CAClC;EAED,MAAMkC,SAAS,GAAG,IAAAC,uBAAY,EAACnC,MAAM,EAAEuB,QAAQ,CAAC;EAChD,IAAIW,SAAS,EAAE;IACb,IAAAR,4CAAgC,EAACF,eAAe,EAAE3B,MAAM,CAACuC,UAAU,EAAEF,SAAS,CAAC;EACjF,CAAC,MAAM;IACL,IAAAG,iDAAqC,EAACb,eAAe,EAAE3B,MAAM,CAACuC,UAAU,CAAC;EAC3E;EAEA,MAAME,sBAAsB,GAAG,IAAAC,2CAAgC,EAACjC,WAAW,EAAEN,MAAM,CAAC;EACpF,IAAIsC,sBAAsB,EAAE;IAC1B,IAAAZ,4CAAgC,EAC9BF,eAAe,EACf3B,MAAM,CAAC2C,wBAAwB,EAC/BF,sBAAsB,CACvB;EACH,CAAC,MAAM;IACL,IAAAD,iDAAqC,EAACb,eAAe,EAAE3B,MAAM,CAAC2C,wBAAwB,CAAC;EACzF;EAEA,MAAMC,mBAAmB,GAAG,IAAAC,mDAAwC,EAAC1C,MAAM,CAAC;EAC5E,IAAIyC,mBAAmB,EAAE;IACvB,IAAAf,4CAAgC,EAC9BF,eAAe,EACf3B,MAAM,CAAC8C,qBAAqB,EAC5BF,mBAAmB,CACpB;EACH,CAAC,MAAM;IACL,IAAAJ,iDAAqC,EAACb,eAAe,EAAE3B,MAAM,CAAC8C,qBAAqB,CAAC;EACtF;EAEA,MAAMC,cAAc,GAAG,IAAAC,8CAAmC,EAAC7C,MAAM,CAAC;EAClE,IAAI4C,cAAc,EAAE;IAClB,IAAAlB,4CAAgC,EAC9BF,eAAe,EACf3B,MAAM,CAACiD,yCAAyC,EAChDF,cAAc,CACf;EACH,CAAC,MAAM;IACL,IAAAP,iDAAqC,EACnCb,eAAe,EACf3B,MAAM,CAACiD,yCAAyC,CACjD;EACH;EAEA,OAAOC,iBAAiB,CAAC/C,MAAM,EAAEsB,eAAe,CAAC;AACnD;AAEO,SAASyB,iBAAiBA,CAC/B/C,MAAgE,EAChEsB,eAAgC,EACf;EACjB,MAAME,eAAe,GAAG,IAAAC,qCAAyB,EAACH,eAAe,CAAC;EAElE,MAAMP,cAAc,GAAG,IAAAC,oCAAyB,EAAChB,MAAM,EAAE,SAAS,CAAC;EACnE,IAAI,CAACe,cAAc,IAAI,IAAAiC,4BAAgB,EAACxB,eAAe,EAAE3B,MAAM,CAACoD,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE;IACrF,MAAM,IAAIC,KAAK,CACb,2PAA2P,CAC5P;EACH;EACA,MAAMC,UAAU,GAAG,IAAAC,wBAAa,EAACpD,MAAM,CAAC;EACxC,IAAIe,cAAc,EAAE;IAClB,IAAAsB,iDAAqC,EAACb,eAAe,EAAE3B,MAAM,CAACwD,WAAW,CAAC;IAC1E,IAAA3B,4CAAgC,EAC9BF,eAAe,EACf3B,MAAM,CAACoD,eAAe,EACtB,8BAA8B,CAC/B;EACH,CAAC,MAAM,IAAIE,UAAU,EAAE;IACrB;AACJ;AACA;AACA;IACI,IAAAd,iDAAqC,EAACb,eAAe,EAAE3B,MAAM,CAACoD,eAAe,CAAC;IAC9E,IAAAvB,4CAAgC,EAACF,eAAe,EAAE3B,MAAM,CAACwD,WAAW,EAAEF,UAAU,CAAC;EACnF,CAAC,MAAM;IACL,IAAAd,iDAAqC,EAACb,eAAe,EAAE3B,MAAM,CAACoD,eAAe,CAAC;IAC9E,IAAAZ,iDAAqC,EAACb,eAAe,EAAE3B,MAAM,CAACwD,WAAW,CAAC;EAC5E;EAEA,OAAO/B,eAAe;AACxB;AACO,SAASgC,4CAA4CA,CAC1DhD,WAAmB,EACnBiD,mBAA2B,EACnB;EACR,IAAI,CAACC,uBAAuB,CAAClD,WAAW,EAAEiD,mBAAmB,CAAC,EAAE;IAC9D,IAAIE,4BAA4B;IAEhC,MAAMC,0BAA0B,GAAGH,mBAAmB,CACnDI,KAAK,CAAC,IAAI,CAAC,CACXC,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAClE,4BAA4B,CAAC,CAAC;IAC9D,IAAI8D,0BAA0B,EAAE;MAC9BD,4BAA4B,GAAGF,mBAAmB,CAACQ,OAAO,CACxD,IAAIC,MAAM,CAAE,4CAA2CpE,4BAA6B,MAAK,CAAC,EAC1F,EAAE,CACH;IACH,CAAC,MAAM;MACL6D,4BAA4B,GAAGF,mBAAmB;IACpD;IAEA,MAAMU,iBAAiB,GAAGC,6BAA6B,CAAC5D,WAAW,CAAC;IACpE,OAAQ,GAAEmD,4BAA6B,uCAAsCQ,iBAAkB,IAAG;EACpG,CAAC,MAAM;IACL,OAAOV,mBAAmB;EAC5B;AACF;AAEO,SAASW,6BAA6BA,CAAC5D,WAAmB,EAAU;EACzE,MAAM6D,uBAAuB,GAAGC,sBAAW,CAACC,MAAM,CAAC/D,WAAW,EAAEV,4BAA4B,CAAC;EAE7F,IAAI,CAACuE,uBAAuB,EAAE;IAC5B,MAAM,IAAIjB,KAAK,CACb,yJAAyJ,CAC1J;EACH;EAEA,MAAMoB,YAAY,GAAGC,eAAI,CAACC,QAAQ,CAChCD,eAAI,CAACE,IAAI,CAACnE,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,EACxC6D,uBAAuB,CACxB;EACD,MAAMO,SAAS,GAAGC,OAAO,CAACC,QAAQ,KAAK,OAAO,GAAGN,YAAY,CAACP,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAAGO,YAAY;EAEhG,OAAQ,gBAAeI,SAAU,GAAE;AACrC;AAEO,SAASlB,uBAAuBA,CAAClD,WAAmB,EAAEiD,mBAA2B,EAAW;EACjG,MAAMsB,kBAAkB,GAAGX,6BAA6B,CAAC5D,WAAW,CAAC;EAErE,OACEiD,mBAAmB,CAChBQ,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CACtBJ,KAAK,CAAC,IAAI;EACX;EAAA,CACCC,IAAI,CAAEC,IAAI,IAAKA,IAAI,KAAKgB,kBAAkB,IAAIhB,IAAI,KAAKgB,kBAAkB,CAACd,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAEpG;AAEO,SAASe,4BAA4BA,CAACxD,eAAgC,EAAW;EACtF,MAAMY,SAAS,GAAG,IAAA6C,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAACuC,UAAU,CAAC;EACrF,MAAMrB,cAAc,GAAG,IAAAgE,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAACoD,eAAe,CAAC;EAC/F,MAAME,UAAU,GAAG,IAAA4B,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAACwD,WAAW,CAAC;EAEvF,OAAO2B,OAAO,CAAC9C,SAAS,KAAKiB,UAAU,IAAIpC,cAAc,CAAC,CAAC;AAC7D;AAEO,SAASkE,+BAA+BA,CAC7C3E,WAAmB,EACnBN,MAAyB,EACzBsB,eAAgC,EAChCC,QAAuB,EACd;EACT,OACE,IAAAY,uBAAY,EAACnC,MAAM,EAAEuB,QAAQ,CAAC,KAC5B,IAAAwD,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAACuC,UAAU,CAAC,IACrER,MAAM,CAAC,IAAAC,4BAAiB,EAAC7B,MAAM,EAAEuB,QAAQ,CAAC,CAAC,KACzC,IAAAwD,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAAC8B,OAAO,CAAC,IAClEC,MAAM,CAAC,IAAAK,4BAAiB,EAACjC,MAAM,CAAC,CAAC,KAC/B,IAAA+E,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAACmC,cAAc,CAAC,IACzE,IAAAD,kCAAuB,EAAC/B,MAAM,CAAC,KAC7B,IAAA+E,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAACiC,eAAe,CAAC,IAC1E,IAAAS,2CAAgC,EAACjC,WAAW,EAAEN,MAAM,CAAC,KACnD,IAAA+E,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAAC2C,wBAAwB,CAAC,IACnF,IAAAE,mDAAwC,EAAC1C,MAAM,CAAC,KAC9C,IAAA+E,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAAC8C,qBAAqB,CAAC,IAChFuC,iBAAiB,CAAClF,MAAM,EAAEsB,eAAe,CAAC;AAE9C;AAEO,SAAS4D,iBAAiBA,CAC/BlF,MAAgE,EAChEsB,eAAgC,EACvB;EACT,MAAM6D,sBAAsB,GAAG,IAAAnE,oCAAyB,EAAChB,MAAM,EAAE,SAAS,CAAC;EAC3E,MAAMoF,kBAAkB,GAAG,IAAAhC,wBAAa,EAACpD,MAAM,CAAC;EAEhD,MAAMqF,qBAAqB,GAAG,IAAAN,2CAA+B,EAC3DzD,eAAe,EACfzB,MAAM,CAACoD,eAAe,CACvB;EACD,MAAMqC,iBAAiB,GAAG,IAAAP,2CAA+B,EAACzD,eAAe,EAAEzB,MAAM,CAACwD,WAAW,CAAC;EAE9F,IAAI8B,sBAAsB,KAAK,IAAI,EAAE;IACnC,OAAOE,qBAAqB,KAAKF,sBAAsB,IAAIG,iBAAiB,KAAK,IAAI;EACvF,CAAC,MAAM,IAAIF,kBAAkB,KAAK,IAAI,EAAE;IACtC,OAAOE,iBAAiB,KAAKF,kBAAkB,IAAIC,qBAAqB,KAAK,IAAI;EACnF,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF"}