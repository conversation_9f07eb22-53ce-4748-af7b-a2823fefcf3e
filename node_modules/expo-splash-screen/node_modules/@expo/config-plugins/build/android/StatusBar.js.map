{"version": 3, "file": "StatusBar.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_androidPlugins", "_Colors", "_Styles", "obj", "__esModule", "default", "COLOR_PRIMARY_DARK_KEY", "WINDOW_TRANSLUCENT_STATUS", "WINDOW_LIGHT_STATUS_BAR", "withStatusBar", "config", "withStatusBarColors", "withStatusBarStyles", "exports", "withAndroidColors", "modResults", "setStatusBarColors", "withAndroidStyles", "setStatusBarStyles", "colors", "assignColorValue", "name", "value", "getStatusBarColor", "styles", "hexString", "floatElement", "getStatusBarTranslucent", "assignStylesValue", "parent", "getAppThemeLightNoActionBarGroup", "targetApi", "add", "getStatusBarStyle", "_config$androidStatus", "backgroundColor", "androidStatusBar", "assert", "_config$androidStatus2", "_config$androidStatus3", "translucent", "_config$androidStatus4", "barStyle"], "sources": ["../../src/android/StatusBar.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport assert from 'assert';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withAndroidColors, withAndroidStyles } from '../plugins/android-plugins';\nimport { assignColorValue } from './Colors';\nimport { ResourceXML } from './Resources';\nimport { assignStylesValue, getAppThemeLightNoActionBarGroup } from './Styles';\n\n// https://developer.android.com/reference/android/R.attr#colorPrimaryDark\nconst COLOR_PRIMARY_DARK_KEY = 'colorPrimaryDark';\n// https://developer.android.com/reference/android/R.attr#windowTranslucentStatus\nconst WINDOW_TRANSLUCENT_STATUS = 'android:windowTranslucentStatus';\n// https://developer.android.com/reference/android/R.attr#windowLightStatusBar\nconst WINDOW_LIGHT_STATUS_BAR = 'android:windowLightStatusBar';\n\nexport const withStatusBar: ConfigPlugin = (config) => {\n  config = withStatusBarColors(config);\n  config = withStatusBarStyles(config);\n  return config;\n};\n\nconst withStatusBarColors: ConfigPlugin = (config) => {\n  return withAndroidColors(config, (config) => {\n    config.modResults = setStatusBarColors(config, config.modResults);\n    return config;\n  });\n};\n\nconst withStatusBarStyles: ConfigPlugin = (config) => {\n  return withAndroidStyles(config, (config) => {\n    config.modResults = setStatusBarStyles(config, config.modResults);\n    return config;\n  });\n};\n\nexport function setStatusBarColors(\n  config: Pick<ExpoConfig, 'androidStatusBar'>,\n  colors: ResourceXML\n): ResourceXML {\n  return assignColorValue(colors, {\n    name: COLOR_PRIMARY_DARK_KEY,\n    value: getStatusBarColor(config),\n  });\n}\n\nexport function setStatusBarStyles(\n  config: Pick<ExpoConfig, 'androidStatusBar'>,\n  styles: ResourceXML\n): ResourceXML {\n  const hexString = getStatusBarColor(config);\n  const floatElement = getStatusBarTranslucent(config);\n\n  styles = assignStylesValue(styles, {\n    parent: getAppThemeLightNoActionBarGroup(),\n    name: WINDOW_LIGHT_STATUS_BAR,\n    targetApi: '23',\n    value: 'true',\n    // Default is light-content, don't need to do anything to set it\n    add: getStatusBarStyle(config) === 'dark-content',\n  });\n\n  styles = assignStylesValue(styles, {\n    parent: getAppThemeLightNoActionBarGroup(),\n    name: WINDOW_TRANSLUCENT_STATUS,\n    value: 'true',\n    // translucent status bar set in theme\n    add: floatElement,\n  });\n\n  styles = assignStylesValue(styles, {\n    parent: getAppThemeLightNoActionBarGroup(),\n    name: COLOR_PRIMARY_DARK_KEY,\n    value: `@color/${COLOR_PRIMARY_DARK_KEY}`,\n    // Remove the color if translucent is used\n    add: !!hexString,\n  });\n\n  return styles;\n}\n\nexport function getStatusBarColor(config: Pick<ExpoConfig, 'androidStatusBar'>) {\n  const backgroundColor = config.androidStatusBar?.backgroundColor;\n  if (backgroundColor) {\n    // Drop support for translucent\n    assert(\n      backgroundColor !== 'translucent',\n      `androidStatusBar.backgroundColor must be a valid hex string, instead got: \"${backgroundColor}\"`\n    );\n  }\n  return backgroundColor;\n}\n\n/**\n * Specifies whether the status bar should be \"translucent\". When true, the status bar is drawn with `position: absolute` and a gray underlay, when false `position: relative` (pushes content down).\n *\n * @default false\n * @param config\n * @returns\n */\nexport function getStatusBarTranslucent(config: Pick<ExpoConfig, 'androidStatusBar'>): boolean {\n  return config.androidStatusBar?.translucent ?? false;\n}\n\nexport function getStatusBarStyle(config: Pick<ExpoConfig, 'androidStatusBar'>) {\n  return config.androidStatusBar?.barStyle || 'light-content';\n}\n"], "mappings": ";;;;;;;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAG,gBAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,eAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+E,SAAAC,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE/E;AACA,MAAMG,sBAAsB,GAAG,kBAAkB;AACjD;AACA,MAAMC,yBAAyB,GAAG,iCAAiC;AACnE;AACA,MAAMC,uBAAuB,GAAG,8BAA8B;AAEvD,MAAMC,aAA2B,GAAIC,MAAM,IAAK;EACrDA,MAAM,GAAGC,mBAAmB,CAACD,MAAM,CAAC;EACpCA,MAAM,GAAGE,mBAAmB,CAACF,MAAM,CAAC;EACpC,OAAOA,MAAM;AACf,CAAC;AAACG,OAAA,CAAAJ,aAAA,GAAAA,aAAA;AAEF,MAAME,mBAAiC,GAAID,MAAM,IAAK;EACpD,OAAO,IAAAI,mCAAiB,EAACJ,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACK,UAAU,GAAGC,kBAAkB,CAACN,MAAM,EAAEA,MAAM,CAACK,UAAU,CAAC;IACjE,OAAOL,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAME,mBAAiC,GAAIF,MAAM,IAAK;EACpD,OAAO,IAAAO,mCAAiB,EAACP,MAAM,EAAGA,MAAM,IAAK;IAC3CA,MAAM,CAACK,UAAU,GAAGG,kBAAkB,CAACR,MAAM,EAAEA,MAAM,CAACK,UAAU,CAAC;IACjE,OAAOL,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAEM,SAASM,kBAAkBA,CAChCN,MAA4C,EAC5CS,MAAmB,EACN;EACb,OAAO,IAAAC,0BAAgB,EAACD,MAAM,EAAE;IAC9BE,IAAI,EAAEf,sBAAsB;IAC5BgB,KAAK,EAAEC,iBAAiB,CAACb,MAAM;EACjC,CAAC,CAAC;AACJ;AAEO,SAASQ,kBAAkBA,CAChCR,MAA4C,EAC5Cc,MAAmB,EACN;EACb,MAAMC,SAAS,GAAGF,iBAAiB,CAACb,MAAM,CAAC;EAC3C,MAAMgB,YAAY,GAAGC,uBAAuB,CAACjB,MAAM,CAAC;EAEpDc,MAAM,GAAG,IAAAI,2BAAiB,EAACJ,MAAM,EAAE;IACjCK,MAAM,EAAE,IAAAC,0CAAgC,GAAE;IAC1CT,IAAI,EAAEb,uBAAuB;IAC7BuB,SAAS,EAAE,IAAI;IACfT,KAAK,EAAE,MAAM;IACb;IACAU,GAAG,EAAEC,iBAAiB,CAACvB,MAAM,CAAC,KAAK;EACrC,CAAC,CAAC;EAEFc,MAAM,GAAG,IAAAI,2BAAiB,EAACJ,MAAM,EAAE;IACjCK,MAAM,EAAE,IAAAC,0CAAgC,GAAE;IAC1CT,IAAI,EAAEd,yBAAyB;IAC/Be,KAAK,EAAE,MAAM;IACb;IACAU,GAAG,EAAEN;EACP,CAAC,CAAC;EAEFF,MAAM,GAAG,IAAAI,2BAAiB,EAACJ,MAAM,EAAE;IACjCK,MAAM,EAAE,IAAAC,0CAAgC,GAAE;IAC1CT,IAAI,EAAEf,sBAAsB;IAC5BgB,KAAK,EAAG,UAAShB,sBAAuB,EAAC;IACzC;IACA0B,GAAG,EAAE,CAAC,CAACP;EACT,CAAC,CAAC;EAEF,OAAOD,MAAM;AACf;AAEO,SAASD,iBAAiBA,CAACb,MAA4C,EAAE;EAAA,IAAAwB,qBAAA;EAC9E,MAAMC,eAAe,IAAAD,qBAAA,GAAGxB,MAAM,CAAC0B,gBAAgB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBC,eAAe;EAChE,IAAIA,eAAe,EAAE;IACnB;IACA,IAAAE,iBAAM,EACJF,eAAe,KAAK,aAAa,EAChC,8EAA6EA,eAAgB,GAAE,CACjG;EACH;EACA,OAAOA,eAAe;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASR,uBAAuBA,CAACjB,MAA4C,EAAW;EAAA,IAAA4B,sBAAA,EAAAC,sBAAA;EAC7F,QAAAD,sBAAA,IAAAC,sBAAA,GAAO7B,MAAM,CAAC0B,gBAAgB,cAAAG,sBAAA,uBAAvBA,sBAAA,CAAyBC,WAAW,cAAAF,sBAAA,cAAAA,sBAAA,GAAI,KAAK;AACtD;AAEO,SAASL,iBAAiBA,CAACvB,MAA4C,EAAE;EAAA,IAAA+B,sBAAA;EAC9E,OAAO,EAAAA,sBAAA,GAAA/B,MAAM,CAAC0B,gBAAgB,cAAAK,sBAAA,uBAAvBA,sBAAA,CAAyBC,QAAQ,KAAI,eAAe;AAC7D"}