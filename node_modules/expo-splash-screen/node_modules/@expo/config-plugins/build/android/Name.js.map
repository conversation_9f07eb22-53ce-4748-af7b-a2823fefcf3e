{"version": 3, "file": "Name.js", "names": ["_androidPlugins", "data", "require", "_warnings", "_Resources", "_Strings", "sanitizeNameForGradle", "name", "replace", "with<PERSON><PERSON>", "createStringsXmlPlugin", "applyNameFromConfig", "exports", "withNameSettingsGradle", "config", "withSettingsGradle", "modResults", "language", "contents", "applyNameSettingsGradle", "addWarningAndroid", "getName", "stringsJSON", "setStringItem", "buildResourceItem", "value", "removeStringItem", "<PERSON><PERSON><PERSON><PERSON>", "_getName"], "sources": ["../../src/android/Name.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { createStringsXmlPlugin, withSettingsGradle } from '../plugins/android-plugins';\nimport { addWarningAndroid } from '../utils/warnings';\nimport { buildResourceItem, ResourceXML } from './Resources';\nimport { removeStringItem, setStringItem } from './Strings';\n\n/**\n * Sanitize a name, this should be used for files and gradle names.\n * - `[/, \\, :, <, >, \", ?, *, |]` are not allowed\n * https://docs.gradle.org/4.2/release-notes.html#path-separator-characters-in-names-are-deprecated\n *\n * @param name\n */\nexport function sanitizeNameForGradle(name: string): string {\n  // Remove escape characters which are valid in XML names but not in gradle.\n  name = name.replace(/[\\n\\r\\t]/g, '');\n\n  // Gradle disallows these:\n  // The project name 'My-Special 😃 Co/ol_Project' must not contain any of the following characters: [/, \\, :, <, >, \", ?, *, |]. Set the 'rootProject.name' or adjust the 'include' statement (see https://docs.gradle.org/6.2/dsl/org.gradle.api.initialization.Settings.html#org.gradle.api.initialization.Settings:include(java.lang.String[]) for more details).\n  return name.replace(/(\\/|\\\\|:|<|>|\"|\\?|\\*|\\|)/g, '');\n}\n\nexport const withName = createStringsXmlPlugin(applyNameFromConfig, 'withName');\n\nexport const withNameSettingsGradle: ConfigPlugin = (config) => {\n  return withSettingsGradle(config, (config) => {\n    if (config.modResults.language === 'groovy') {\n      config.modResults.contents = applyNameSettingsGradle(config, config.modResults.contents);\n    } else {\n      addWarningAndroid(\n        'name',\n        `Cannot automatically configure settings.gradle if it's not groovy`\n      );\n    }\n    return config;\n  });\n};\n\nexport function getName(config: Pick<ExpoConfig, 'name'>) {\n  return typeof config.name === 'string' ? config.name : null;\n}\n\nfunction applyNameFromConfig(\n  config: Pick<ExpoConfig, 'name'>,\n  stringsJSON: ResourceXML\n): ResourceXML {\n  const name = getName(config);\n  if (name) {\n    return setStringItem([buildResourceItem({ name: 'app_name', value: name })], stringsJSON);\n  }\n  return removeStringItem('app_name', stringsJSON);\n}\n\n/**\n * Regex a name change -- fragile.\n *\n * @param config\n * @param settingsGradle\n */\nexport function applyNameSettingsGradle(config: Pick<ExpoConfig, 'name'>, settingsGradle: string) {\n  const name = sanitizeNameForGradle(getName(config) ?? '');\n\n  // Select rootProject.name = '***' and replace the contents between the quotes.\n  return settingsGradle.replace(\n    /rootProject.name\\s?=\\s?([\"'])(?:(?=(\\\\?))\\2.)*?\\1/g,\n    `rootProject.name = '${name.replace(/'/g, \"\\\\'\")}'`\n  );\n}\n"], "mappings": ";;;;;;;;;AAGA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,qBAAqBA,CAACC,IAAY,EAAU;EAC1D;EACAA,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;;EAEpC;EACA;EACA,OAAOD,IAAI,CAACC,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;AACtD;AAEO,MAAMC,QAAQ,GAAG,IAAAC,wCAAsB,EAACC,mBAAmB,EAAE,UAAU,CAAC;AAACC,OAAA,CAAAH,QAAA,GAAAA,QAAA;AAEzE,MAAMI,sBAAoC,GAAIC,MAAM,IAAK;EAC9D,OAAO,IAAAC,oCAAkB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC5C,IAAIA,MAAM,CAACE,UAAU,CAACC,QAAQ,KAAK,QAAQ,EAAE;MAC3CH,MAAM,CAACE,UAAU,CAACE,QAAQ,GAAGC,uBAAuB,CAACL,MAAM,EAAEA,MAAM,CAACE,UAAU,CAACE,QAAQ,CAAC;IAC1F,CAAC,MAAM;MACL,IAAAE,6BAAiB,EACf,MAAM,EACL,mEAAkE,CACpE;IACH;IACA,OAAON,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACF,OAAA,CAAAC,sBAAA,GAAAA,sBAAA;AAEK,SAASQ,OAAOA,CAACP,MAAgC,EAAE;EACxD,OAAO,OAAOA,MAAM,CAACP,IAAI,KAAK,QAAQ,GAAGO,MAAM,CAACP,IAAI,GAAG,IAAI;AAC7D;AAEA,SAASI,mBAAmBA,CAC1BG,MAAgC,EAChCQ,WAAwB,EACX;EACb,MAAMf,IAAI,GAAGc,OAAO,CAACP,MAAM,CAAC;EAC5B,IAAIP,IAAI,EAAE;IACR,OAAO,IAAAgB,wBAAa,EAAC,CAAC,IAAAC,8BAAiB,EAAC;MAAEjB,IAAI,EAAE,UAAU;MAAEkB,KAAK,EAAElB;IAAK,CAAC,CAAC,CAAC,EAAEe,WAAW,CAAC;EAC3F;EACA,OAAO,IAAAI,2BAAgB,EAAC,UAAU,EAAEJ,WAAW,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASH,uBAAuBA,CAACL,MAAgC,EAAEa,cAAsB,EAAE;EAAA,IAAAC,QAAA;EAChG,MAAMrB,IAAI,GAAGD,qBAAqB,EAAAsB,QAAA,GAACP,OAAO,CAACP,MAAM,CAAC,cAAAc,QAAA,cAAAA,QAAA,GAAI,EAAE,CAAC;;EAEzD;EACA,OAAOD,cAAc,CAACnB,OAAO,CAC3B,oDAAoD,EACnD,uBAAsBD,IAAI,CAACC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAE,GAAE,CACpD;AACH"}