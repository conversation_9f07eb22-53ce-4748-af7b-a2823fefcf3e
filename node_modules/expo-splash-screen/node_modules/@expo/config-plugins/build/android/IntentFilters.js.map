{"version": 3, "file": "IntentFilters.js", "names": ["_androidPlugins", "data", "require", "_Manifest", "GENERATED_TAG", "withAndroidIntentFilters", "createAndroidManifestPlugin", "setAndroidIntentFilters", "exports", "getIntentFilters", "config", "_config$android$inten", "_config$android", "android", "intentFilters", "androidManifest", "_mainActivity$intent", "_mainActivity$intent2", "mainActivity", "getMainActivityOrThrow", "length", "filter", "value", "_value$$", "$", "concat", "renderIntentFilters", "map", "<PERSON><PERSON><PERSON><PERSON>", "autoVerify", "undefined", "action", "renderIntentFilterData", "category", "renderIntentFilterCategory", "Array", "isArray", "Boolean", "datum", "Object", "entries", "reduce", "prev", "key", "cat"], "sources": ["../../src/android/IntentFilters.ts"], "sourcesContent": ["import { Android, AndroidIntentFiltersData, ExpoConfig } from '@expo/config-types';\n\nimport { createAndroidManifestPlugin } from '../plugins/android-plugins';\nimport { AndroidManifest, getMainActivityOrThrow, ManifestIntentFilter } from './Manifest';\n\ntype AndroidIntentFilters = NonNullable<Android['intentFilters']>;\n\nconst GENERATED_TAG = 'data-generated';\n\nexport const withAndroidIntentFilters = createAndroidManifestPlugin(\n  setAndroidIntentFilters,\n  'withAndroidIntentFilters'\n);\n\nexport function getIntentFilters(config: Pick<ExpoConfig, 'android'>): AndroidIntentFilters {\n  return config.android?.intentFilters ?? [];\n}\n\nexport function setAndroidIntentFilters(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidManifest\n): AndroidManifest {\n  // Always ensure old tags are removed.\n  const mainActivity = getMainActivityOrThrow(androidManifest);\n  // Remove all generated tags from previous runs...\n  if (mainActivity['intent-filter']?.length) {\n    mainActivity['intent-filter'] = mainActivity['intent-filter'].filter(\n      (value) => value.$?.[GENERATED_TAG] !== 'true'\n    );\n  }\n\n  const intentFilters = getIntentFilters(config);\n  if (!intentFilters.length) {\n    return androidManifest;\n  }\n\n  mainActivity['intent-filter'] = mainActivity['intent-filter']?.concat(\n    renderIntentFilters(intentFilters)\n  );\n\n  return androidManifest;\n}\n\nexport default function renderIntentFilters(\n  intentFilters: AndroidIntentFilters\n): ManifestIntentFilter[] {\n  return intentFilters.map((intentFilter) => {\n    // <intent-filter>\n    return {\n      $: {\n        'android:autoVerify': intentFilter.autoVerify ? 'true' : undefined,\n        // Add a custom \"generated\" tag that we can query later to remove.\n        [GENERATED_TAG]: 'true',\n      },\n      action: [\n        // <action android:name=\"android.intent.action.VIEW\"/>\n        {\n          $: {\n            'android:name': `android.intent.action.${intentFilter.action}`,\n          },\n        },\n      ],\n      data: renderIntentFilterData(intentFilter.data),\n      category: renderIntentFilterCategory(intentFilter.category),\n    };\n  });\n}\n\n/** Like `<data android:scheme=\"exp\"/>` */\nfunction renderIntentFilterData(data?: AndroidIntentFiltersData | AndroidIntentFiltersData[]) {\n  return (Array.isArray(data) ? data : [data]).filter(Boolean).map((datum) => ({\n    $: Object.entries(datum ?? {}).reduce(\n      (prev, [key, value]) => ({ ...prev, [`android:${key}`]: value }),\n      {}\n    ),\n  }));\n}\n\n/** Like `<category android:name=\"android.intent.category.DEFAULT\"/>` */\nfunction renderIntentFilterCategory(category?: string | string[]) {\n  return (Array.isArray(category) ? category : [category]).filter(Boolean).map((cat) => ({\n    $: {\n      'android:name': `android.intent.category.${cat}`,\n    },\n  }));\n}\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,gBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,eAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,MAAMG,aAAa,GAAG,gBAAgB;AAE/B,MAAMC,wBAAwB,GAAG,IAAAC,6CAA2B,EACjEC,uBAAuB,EACvB,0BAA0B,CAC3B;AAACC,OAAA,CAAAH,wBAAA,GAAAA,wBAAA;AAEK,SAASI,gBAAgBA,CAACC,MAAmC,EAAwB;EAAA,IAAAC,qBAAA,EAAAC,eAAA;EAC1F,QAAAD,qBAAA,IAAAC,eAAA,GAAOF,MAAM,CAACG,OAAO,cAAAD,eAAA,uBAAdA,eAAA,CAAgBE,aAAa,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,EAAE;AAC5C;AAEO,SAASJ,uBAAuBA,CACrCG,MAAmC,EACnCK,eAAgC,EACf;EAAA,IAAAC,oBAAA,EAAAC,qBAAA;EACjB;EACA,MAAMC,YAAY,GAAG,IAAAC,kCAAsB,EAACJ,eAAe,CAAC;EAC5D;EACA,KAAAC,oBAAA,GAAIE,YAAY,CAAC,eAAe,CAAC,cAAAF,oBAAA,eAA7BA,oBAAA,CAA+BI,MAAM,EAAE;IACzCF,YAAY,CAAC,eAAe,CAAC,GAAGA,YAAY,CAAC,eAAe,CAAC,CAACG,MAAM,CACjEC,KAAK;MAAA,IAAAC,QAAA;MAAA,OAAK,EAAAA,QAAA,GAAAD,KAAK,CAACE,CAAC,cAAAD,QAAA,uBAAPA,QAAA,CAAUnB,aAAa,CAAC,MAAK,MAAM;IAAA,EAC/C;EACH;EAEA,MAAMU,aAAa,GAAGL,gBAAgB,CAACC,MAAM,CAAC;EAC9C,IAAI,CAACI,aAAa,CAACM,MAAM,EAAE;IACzB,OAAOL,eAAe;EACxB;EAEAG,YAAY,CAAC,eAAe,CAAC,IAAAD,qBAAA,GAAGC,YAAY,CAAC,eAAe,CAAC,cAAAD,qBAAA,uBAA7BA,qBAAA,CAA+BQ,MAAM,CACnEC,mBAAmB,CAACZ,aAAa,CAAC,CACnC;EAED,OAAOC,eAAe;AACxB;AAEe,SAASW,mBAAmBA,CACzCZ,aAAmC,EACX;EACxB,OAAOA,aAAa,CAACa,GAAG,CAAEC,YAAY,IAAK;IACzC;IACA,OAAO;MACLJ,CAAC,EAAE;QACD,oBAAoB,EAAEI,YAAY,CAACC,UAAU,GAAG,MAAM,GAAGC,SAAS;QAClE;QACA,CAAC1B,aAAa,GAAG;MACnB,CAAC;MACD2B,MAAM,EAAE;MACN;MACA;QACEP,CAAC,EAAE;UACD,cAAc,EAAG,yBAAwBI,YAAY,CAACG,MAAO;QAC/D;MACF,CAAC,CACF;MACD9B,IAAI,EAAE+B,sBAAsB,CAACJ,YAAY,CAAC3B,IAAI,CAAC;MAC/CgC,QAAQ,EAAEC,0BAA0B,CAACN,YAAY,CAACK,QAAQ;IAC5D,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,SAASD,sBAAsBA,CAAC/B,IAA4D,EAAE;EAC5F,OAAO,CAACkC,KAAK,CAACC,OAAO,CAACnC,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC,EAAEoB,MAAM,CAACgB,OAAO,CAAC,CAACV,GAAG,CAAEW,KAAK,KAAM;IAC3Ed,CAAC,EAAEe,MAAM,CAACC,OAAO,CAACF,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,CAAC,CAAC,CAAC,CAACG,MAAM,CACnC,CAACC,IAAI,EAAE,CAACC,GAAG,EAAErB,KAAK,CAAC,MAAM;MAAE,GAAGoB,IAAI;MAAE,CAAE,WAAUC,GAAI,EAAC,GAAGrB;IAAM,CAAC,CAAC,EAChE,CAAC,CAAC;EAEN,CAAC,CAAC,CAAC;AACL;;AAEA;AACA,SAASY,0BAA0BA,CAACD,QAA4B,EAAE;EAChE,OAAO,CAACE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC,EAAEZ,MAAM,CAACgB,OAAO,CAAC,CAACV,GAAG,CAAEiB,GAAG,KAAM;IACrFpB,CAAC,EAAE;MACD,cAAc,EAAG,2BAA0BoB,GAAI;IACjD;EACF,CAAC,CAAC,CAAC;AACL"}