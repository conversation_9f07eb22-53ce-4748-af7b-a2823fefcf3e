{"version": 3, "file": "UsesNonExemptEncryption.js", "names": ["_iosPlugins", "data", "require", "withUsesNonExemptEncryption", "createInfoPlistPluginWithPropertyGuard", "setUsesNonExemptEncryption", "infoPlistProperty", "expoConfigProperty", "exports", "getUsesNonExemptEncryption", "config", "_config$ios$config$us", "_config$ios", "_config$ios$config", "ios", "usesNonExemptEncryption", "ITSAppUsesNonExemptEncryption", "infoPlist"], "sources": ["../../src/ios/UsesNonExemptEncryption.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\nimport { InfoPlist } from './IosConfig.types';\n\nexport const withUsesNonExemptEncryption = createInfoPlistPluginWithPropertyGuard(\n  setUsesNonExemptEncryption,\n  {\n    infoPlistProperty: 'ITSAppUsesNonExemptEncryption',\n    expoConfigProperty: 'ios.config.usesNonExemptEncryption',\n  },\n  'withUsesNonExemptEncryption'\n);\n\nexport function getUsesNonExemptEncryption(config: Pick<ExpoConfig, 'ios'>) {\n  return config?.ios?.config?.usesNonExemptEncryption ?? null;\n}\n\nexport function setUsesNonExemptEncryption(\n  config: Pick<ExpoConfig, 'ios'>,\n  { ITSAppUsesNonExemptEncryption, ...infoPlist }: InfoPlist\n): InfoPlist {\n  const usesNonExemptEncryption = getUsesNonExemptEncryption(config);\n\n  // Make no changes if the key is left blank\n  if (usesNonExemptEncryption === null) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    ITSAppUsesNonExemptEncryption: usesNonExemptEncryption,\n  };\n}\n"], "mappings": ";;;;;;;;AAEA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,MAAME,2BAA2B,GAAG,IAAAC,oDAAsC,EAC/EC,0BAA0B,EAC1B;EACEC,iBAAiB,EAAE,+BAA+B;EAClDC,kBAAkB,EAAE;AACtB,CAAC,EACD,6BAA6B,CAC9B;AAACC,OAAA,CAAAL,2BAAA,GAAAA,2BAAA;AAEK,SAASM,0BAA0BA,CAACC,MAA+B,EAAE;EAAA,IAAAC,qBAAA,EAAAC,WAAA,EAAAC,kBAAA;EAC1E,QAAAF,qBAAA,GAAOD,MAAM,aAANA,MAAM,wBAAAE,WAAA,GAANF,MAAM,CAAEI,GAAG,cAAAF,WAAA,wBAAAC,kBAAA,GAAXD,WAAA,CAAaF,MAAM,cAAAG,kBAAA,uBAAnBA,kBAAA,CAAqBE,uBAAuB,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AAC7D;AAEO,SAASN,0BAA0BA,CACxCK,MAA+B,EAC/B;EAAEM,6BAA6B;EAAE,GAAGC;AAAqB,CAAC,EAC/C;EACX,MAAMF,uBAAuB,GAAGN,0BAA0B,CAACC,MAAM,CAAC;;EAElE;EACA,IAAIK,uBAAuB,KAAK,IAAI,EAAE;IACpC,OAAOE,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZD,6BAA6B,EAAED;EACjC,CAAC;AACH"}