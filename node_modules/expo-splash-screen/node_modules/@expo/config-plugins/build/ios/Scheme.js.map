{"version": 3, "file": "Scheme.js", "names": ["_iosPlugins", "data", "require", "withScheme", "createInfoPlistPluginWithPropertyGuard", "setScheme", "infoPlistProperty", "expoConfigProperty", "exports", "getScheme", "config", "Array", "isArray", "scheme", "validate", "value", "filter", "infoPlist", "_config$ios", "_config$ios2", "ios", "bundleIdentifier", "push", "length", "CFBundleURLTypes", "CFBundleURLSchemes", "appendScheme", "_infoPlist$CFBundleUR", "existingSchemes", "some", "includes", "removeScheme", "map", "bundleUrlType", "index", "indexOf", "splice", "undefined", "Boolean", "hasScheme", "schemes", "getSchemesFromPlist", "reduce"], "sources": ["../../src/ios/Scheme.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\nimport { InfoPlist, URLScheme } from './IosConfig.types';\n\nexport const withScheme = createInfoPlistPluginWithPropertyGuard(\n  setScheme,\n  {\n    infoPlistProperty: 'CFBundleURLTypes',\n    expoConfigProperty: 'scheme',\n  },\n  'withScheme'\n);\n\nexport function getScheme(config: { scheme?: string | string[] }): string[] {\n  if (Array.isArray(config.scheme)) {\n    const validate = (value: any): value is string => {\n      return typeof value === 'string';\n    };\n    return config.scheme.filter<string>(validate);\n  } else if (typeof config.scheme === 'string') {\n    return [config.scheme];\n  }\n  return [];\n}\n\nexport function setScheme(\n  config: Partial<Pick<ExpoConfig, 'scheme' | 'ios'>>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  const scheme = [\n    ...getScheme(config),\n    // @ts-ignore: TODO: ios.scheme is an unreleased -- harder to add to turtle v1.\n    ...getScheme(config.ios ?? {}),\n  ];\n  // Add the bundle identifier to the list of schemes for easier Google auth and parity with Turtle v1.\n  if (config.ios?.bundleIdentifier) {\n    scheme.push(config.ios.bundleIdentifier);\n  }\n  if (scheme.length === 0) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    CFBundleURLTypes: [{ CFBundleURLSchemes: scheme }],\n  };\n}\n\nexport function appendScheme(scheme: string | null, infoPlist: InfoPlist): InfoPlist {\n  if (!scheme) {\n    return infoPlist;\n  }\n\n  const existingSchemes = infoPlist.CFBundleURLTypes ?? [];\n  if (existingSchemes?.some(({ CFBundleURLSchemes }) => CFBundleURLSchemes.includes(scheme))) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    CFBundleURLTypes: [\n      ...existingSchemes,\n      {\n        CFBundleURLSchemes: [scheme],\n      },\n    ],\n  };\n}\n\nexport function removeScheme(scheme: string | null, infoPlist: InfoPlist): InfoPlist {\n  if (!scheme) {\n    return infoPlist;\n  }\n\n  // No need to remove if we don't have any\n  if (!infoPlist.CFBundleURLTypes) {\n    return infoPlist;\n  }\n\n  infoPlist.CFBundleURLTypes = infoPlist.CFBundleURLTypes.map((bundleUrlType) => {\n    const index = bundleUrlType.CFBundleURLSchemes.indexOf(scheme);\n    if (index > -1) {\n      bundleUrlType.CFBundleURLSchemes.splice(index, 1);\n      if (bundleUrlType.CFBundleURLSchemes.length === 0) {\n        return undefined;\n      }\n    }\n    return bundleUrlType;\n  }).filter(Boolean) as URLScheme[];\n\n  return infoPlist;\n}\n\nexport function hasScheme(scheme: string, infoPlist: InfoPlist): boolean {\n  const existingSchemes = infoPlist.CFBundleURLTypes;\n\n  if (!Array.isArray(existingSchemes)) return false;\n\n  return existingSchemes?.some(({ CFBundleURLSchemes: schemes }: any) =>\n    Array.isArray(schemes) ? schemes.includes(scheme) : false\n  );\n}\n\nexport function getSchemesFromPlist(infoPlist: InfoPlist): string[] {\n  if (Array.isArray(infoPlist.CFBundleURLTypes)) {\n    return infoPlist.CFBundleURLTypes.reduce<string[]>((schemes, { CFBundleURLSchemes }) => {\n      if (Array.isArray(CFBundleURLSchemes)) {\n        return [...schemes, ...CFBundleURLSchemes];\n      }\n      return schemes;\n    }, []);\n  }\n  return [];\n}\n"], "mappings": ";;;;;;;;;;;;AAEA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,MAAME,UAAU,GAAG,IAAAC,oDAAsC,EAC9DC,SAAS,EACT;EACEC,iBAAiB,EAAE,kBAAkB;EACrCC,kBAAkB,EAAE;AACtB,CAAC,EACD,YAAY,CACb;AAACC,OAAA,CAAAL,UAAA,GAAAA,UAAA;AAEK,SAASM,SAASA,CAACC,MAAsC,EAAY;EAC1E,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAACG,MAAM,CAAC,EAAE;IAChC,MAAMC,QAAQ,GAAIC,KAAU,IAAsB;MAChD,OAAO,OAAOA,KAAK,KAAK,QAAQ;IAClC,CAAC;IACD,OAAOL,MAAM,CAACG,MAAM,CAACG,MAAM,CAASF,QAAQ,CAAC;EAC/C,CAAC,MAAM,IAAI,OAAOJ,MAAM,CAACG,MAAM,KAAK,QAAQ,EAAE;IAC5C,OAAO,CAACH,MAAM,CAACG,MAAM,CAAC;EACxB;EACA,OAAO,EAAE;AACX;AAEO,SAASR,SAASA,CACvBK,MAAmD,EACnDO,SAAoB,EACT;EAAA,IAAAC,WAAA,EAAAC,YAAA;EACX,MAAMN,MAAM,GAAG,CACb,GAAGJ,SAAS,CAACC,MAAM,CAAC;EACpB;EACA,GAAGD,SAAS,EAAAS,WAAA,GAACR,MAAM,CAACU,GAAG,cAAAF,WAAA,cAAAA,WAAA,GAAI,CAAC,CAAC,CAAC,CAC/B;EACD;EACA,KAAAC,YAAA,GAAIT,MAAM,CAACU,GAAG,cAAAD,YAAA,eAAVA,YAAA,CAAYE,gBAAgB,EAAE;IAChCR,MAAM,CAACS,IAAI,CAACZ,MAAM,CAACU,GAAG,CAACC,gBAAgB,CAAC;EAC1C;EACA,IAAIR,MAAM,CAACU,MAAM,KAAK,CAAC,EAAE;IACvB,OAAON,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZO,gBAAgB,EAAE,CAAC;MAAEC,kBAAkB,EAAEZ;IAAO,CAAC;EACnD,CAAC;AACH;AAEO,SAASa,YAAYA,CAACb,MAAqB,EAAEI,SAAoB,EAAa;EAAA,IAAAU,qBAAA;EACnF,IAAI,CAACd,MAAM,EAAE;IACX,OAAOI,SAAS;EAClB;EAEA,MAAMW,eAAe,IAAAD,qBAAA,GAAGV,SAAS,CAACO,gBAAgB,cAAAG,qBAAA,cAAAA,qBAAA,GAAI,EAAE;EACxD,IAAIC,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEC,IAAI,CAAC,CAAC;IAAEJ;EAAmB,CAAC,KAAKA,kBAAkB,CAACK,QAAQ,CAACjB,MAAM,CAAC,CAAC,EAAE;IAC1F,OAAOI,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZO,gBAAgB,EAAE,CAChB,GAAGI,eAAe,EAClB;MACEH,kBAAkB,EAAE,CAACZ,MAAM;IAC7B,CAAC;EAEL,CAAC;AACH;AAEO,SAASkB,YAAYA,CAAClB,MAAqB,EAAEI,SAAoB,EAAa;EACnF,IAAI,CAACJ,MAAM,EAAE;IACX,OAAOI,SAAS;EAClB;;EAEA;EACA,IAAI,CAACA,SAAS,CAACO,gBAAgB,EAAE;IAC/B,OAAOP,SAAS;EAClB;EAEAA,SAAS,CAACO,gBAAgB,GAAGP,SAAS,CAACO,gBAAgB,CAACQ,GAAG,CAAEC,aAAa,IAAK;IAC7E,MAAMC,KAAK,GAAGD,aAAa,CAACR,kBAAkB,CAACU,OAAO,CAACtB,MAAM,CAAC;IAC9D,IAAIqB,KAAK,GAAG,CAAC,CAAC,EAAE;MACdD,aAAa,CAACR,kBAAkB,CAACW,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjD,IAAID,aAAa,CAACR,kBAAkB,CAACF,MAAM,KAAK,CAAC,EAAE;QACjD,OAAOc,SAAS;MAClB;IACF;IACA,OAAOJ,aAAa;EACtB,CAAC,CAAC,CAACjB,MAAM,CAACsB,OAAO,CAAgB;EAEjC,OAAOrB,SAAS;AAClB;AAEO,SAASsB,SAASA,CAAC1B,MAAc,EAAEI,SAAoB,EAAW;EACvE,MAAMW,eAAe,GAAGX,SAAS,CAACO,gBAAgB;EAElD,IAAI,CAACb,KAAK,CAACC,OAAO,CAACgB,eAAe,CAAC,EAAE,OAAO,KAAK;EAEjD,OAAOA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEC,IAAI,CAAC,CAAC;IAAEJ,kBAAkB,EAAEe;EAAa,CAAC,KAChE7B,KAAK,CAACC,OAAO,CAAC4B,OAAO,CAAC,GAAGA,OAAO,CAACV,QAAQ,CAACjB,MAAM,CAAC,GAAG,KAAK,CAC1D;AACH;AAEO,SAAS4B,mBAAmBA,CAACxB,SAAoB,EAAY;EAClE,IAAIN,KAAK,CAACC,OAAO,CAACK,SAAS,CAACO,gBAAgB,CAAC,EAAE;IAC7C,OAAOP,SAAS,CAACO,gBAAgB,CAACkB,MAAM,CAAW,CAACF,OAAO,EAAE;MAAEf;IAAmB,CAAC,KAAK;MACtF,IAAId,KAAK,CAACC,OAAO,CAACa,kBAAkB,CAAC,EAAE;QACrC,OAAO,CAAC,GAAGe,OAAO,EAAE,GAAGf,kBAAkB,CAAC;MAC5C;MACA,OAAOe,OAAO;IAChB,CAAC,EAAE,EAAE,CAAC;EACR;EACA,OAAO,EAAE;AACX"}