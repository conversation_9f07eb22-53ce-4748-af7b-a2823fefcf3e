{"version": 3, "file": "ProvisioningProfile.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_Target", "_Xcodeproj", "_string", "obj", "__esModule", "default", "setProvisioningProfileForPbxproj", "projectRoot", "targetName", "profileName", "appleTeamId", "buildConfiguration", "project", "getPbxproj", "nativeTargetEntry", "findNativeTargetByName", "findFirstNativeTarget", "nativeTargetId", "nativeTarget", "quotedAppleTeamId", "ensureQuotes", "getBuildConfigurationsForListId", "buildConfigurationList", "filter", "item", "trimQuotes", "name", "for<PERSON>ach", "buildSettings", "PROVISIONING_PROFILE_SPECIFIER", "DEVELOPMENT_TEAM", "CODE_SIGN_IDENTITY", "CODE_SIGN_STYLE", "Object", "entries", "getProjectSection", "isNotComment", "attributes", "TargetAttributes", "DevelopmentTeam", "ProvisioningStyle", "fs", "writeFileSync", "filepath", "writeSync", "value", "match"], "sources": ["../../src/ios/ProvisioningProfile.ts"], "sourcesContent": ["import fs from 'fs';\n\nimport { findFirstNativeTarget, findNativeTargetByName } from './Target';\nimport {\n  ConfigurationSectionEntry,\n  getBuildConfigurationsForListId,\n  getPbxproj,\n  getProjectSection,\n  isNotComment,\n  ProjectSectionEntry,\n} from './utils/Xcodeproj';\nimport { trimQuotes } from './utils/string';\n\ntype ProvisioningProfileSettings = {\n  targetName?: string;\n  appleTeamId: string;\n  profileName: string;\n  buildConfiguration?: string;\n};\n\nexport function setProvisioningProfileForPbxproj(\n  projectRoot: string,\n  {\n    targetName,\n    profileName,\n    appleTeamId,\n    buildConfiguration = 'Release',\n  }: ProvisioningProfileSettings\n): void {\n  const project = getPbxproj(projectRoot);\n\n  const nativeTargetEntry = targetName\n    ? findNativeTargetByName(project, targetName)\n    : findFirstNativeTarget(project);\n  const [nativeTargetId, nativeTarget] = nativeTargetEntry;\n\n  const quotedAppleTeamId = ensureQuotes(appleTeamId);\n\n  getBuildConfigurationsForListId(project, nativeTarget.buildConfigurationList)\n    .filter(([, item]: ConfigurationSectionEntry) => trimQuotes(item.name) === buildConfiguration)\n    .forEach(([, item]: ConfigurationSectionEntry) => {\n      item.buildSettings.PROVISIONING_PROFILE_SPECIFIER = `\"${profileName}\"`;\n      item.buildSettings.DEVELOPMENT_TEAM = quotedAppleTeamId;\n      item.buildSettings.CODE_SIGN_IDENTITY = '\"iPhone Distribution\"';\n      item.buildSettings.CODE_SIGN_STYLE = 'Manual';\n    });\n\n  Object.entries(getProjectSection(project))\n    .filter(isNotComment)\n    .forEach(([, item]: ProjectSectionEntry) => {\n      if (!item.attributes.TargetAttributes[nativeTargetId]) {\n        item.attributes.TargetAttributes[nativeTargetId] = {};\n      }\n      item.attributes.TargetAttributes[nativeTargetId].DevelopmentTeam = quotedAppleTeamId;\n      item.attributes.TargetAttributes[nativeTargetId].ProvisioningStyle = 'Manual';\n    });\n\n  fs.writeFileSync(project.filepath, project.writeSync());\n}\n\nconst ensureQuotes = (value: string) => {\n  if (!value.match(/^['\"]/)) {\n    return `\"${value}\"`;\n  }\n  return value;\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,WAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4C,SAAAC,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AASrC,SAASG,gCAAgCA,CAC9CC,WAAmB,EACnB;EACEC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,kBAAkB,GAAG;AACM,CAAC,EACxB;EACN,MAAMC,OAAO,GAAG,IAAAC,uBAAU,EAACN,WAAW,CAAC;EAEvC,MAAMO,iBAAiB,GAAGN,UAAU,GAChC,IAAAO,gCAAsB,EAACH,OAAO,EAAEJ,UAAU,CAAC,GAC3C,IAAAQ,+BAAqB,EAACJ,OAAO,CAAC;EAClC,MAAM,CAACK,cAAc,EAAEC,YAAY,CAAC,GAAGJ,iBAAiB;EAExD,MAAMK,iBAAiB,GAAGC,YAAY,CAACV,WAAW,CAAC;EAEnD,IAAAW,4CAA+B,EAACT,OAAO,EAAEM,YAAY,CAACI,sBAAsB,CAAC,CAC1EC,MAAM,CAAC,CAAC,GAAGC,IAAI,CAA4B,KAAK,IAAAC,oBAAU,EAACD,IAAI,CAACE,IAAI,CAAC,KAAKf,kBAAkB,CAAC,CAC7FgB,OAAO,CAAC,CAAC,GAAGH,IAAI,CAA4B,KAAK;IAChDA,IAAI,CAACI,aAAa,CAACC,8BAA8B,GAAI,IAAGpB,WAAY,GAAE;IACtEe,IAAI,CAACI,aAAa,CAACE,gBAAgB,GAAGX,iBAAiB;IACvDK,IAAI,CAACI,aAAa,CAACG,kBAAkB,GAAG,uBAAuB;IAC/DP,IAAI,CAACI,aAAa,CAACI,eAAe,GAAG,QAAQ;EAC/C,CAAC,CAAC;EAEJC,MAAM,CAACC,OAAO,CAAC,IAAAC,8BAAiB,EAACvB,OAAO,CAAC,CAAC,CACvCW,MAAM,CAACa,yBAAY,CAAC,CACpBT,OAAO,CAAC,CAAC,GAAGH,IAAI,CAAsB,KAAK;IAC1C,IAAI,CAACA,IAAI,CAACa,UAAU,CAACC,gBAAgB,CAACrB,cAAc,CAAC,EAAE;MACrDO,IAAI,CAACa,UAAU,CAACC,gBAAgB,CAACrB,cAAc,CAAC,GAAG,CAAC,CAAC;IACvD;IACAO,IAAI,CAACa,UAAU,CAACC,gBAAgB,CAACrB,cAAc,CAAC,CAACsB,eAAe,GAAGpB,iBAAiB;IACpFK,IAAI,CAACa,UAAU,CAACC,gBAAgB,CAACrB,cAAc,CAAC,CAACuB,iBAAiB,GAAG,QAAQ;EAC/E,CAAC,CAAC;EAEJC,aAAE,CAACC,aAAa,CAAC9B,OAAO,CAAC+B,QAAQ,EAAE/B,OAAO,CAACgC,SAAS,EAAE,CAAC;AACzD;AAEA,MAAMxB,YAAY,GAAIyB,KAAa,IAAK;EACtC,IAAI,CAACA,KAAK,CAACC,KAAK,CAAC,OAAO,CAAC,EAAE;IACzB,OAAQ,IAAGD,KAAM,GAAE;EACrB;EACA,OAAOA,KAAK;AACd,CAAC"}