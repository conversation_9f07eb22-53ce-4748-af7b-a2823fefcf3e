{"version": 3, "file": "RequiresFullScreen.js", "names": ["_iosPlugins", "data", "require", "_versions", "_warnings", "withRequiresFullScreen", "createInfoPlistPlugin", "setRequiresFullScreen", "exports", "getRequiresFullScreen", "config", "_config$ios", "ios", "hasOwnProperty", "requireFullScreen", "gteSdkVersion", "iPadInterfaceKey", "requiredIPadInterface", "isStringArray", "value", "Array", "isArray", "every", "hasMinimumOrientations", "masks", "mask", "includes", "resolveExistingIpadInterfaceOrientations", "interfaceOrientations", "length", "existingList", "join", "addWarningIOS", "infoPlist", "requiresFullScreen", "existing", "Set", "concat", "UIRequiresFullScreen"], "sources": ["../../src/ios/RequiresFullScreen.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createInfoPlistPlugin } from '../plugins/ios-plugins';\nimport { gteSdkVersion } from '../utils/versions';\nimport { addWarningIOS } from '../utils/warnings';\nimport { InfoPlist } from './IosConfig.types';\n\nexport const withRequiresFullScreen = createInfoPlistPlugin(\n  setRequiresFullScreen,\n  'withRequiresFullScreen'\n);\n\n// NOTES: This is defaulted to `true` for now to match the behavior prior to SDK\n// 34, but will change to `false` in SDK +43.\nexport function getRequiresFullScreen(config: Pick<ExpoConfig, 'ios' | 'sdkVersion'>) {\n  // Yes, the property is called ios.requireFullScreen, without the s - not \"requires\"\n  // This is confusing indeed because the actual property name does have the s\n  if (config.ios?.hasOwnProperty('requireFullScreen')) {\n    return !!config.ios.requireFullScreen;\n  } else {\n    // In SDK 43, the `requireFullScreen` default has been changed to false.\n    if (\n      gteSdkVersion(config, '43.0.0')\n      // TODO: Uncomment after SDK 43 is released.\n      // || !config.sdkVersion\n    ) {\n      return false;\n    }\n    return true;\n  }\n}\n\nconst iPadInterfaceKey = 'UISupportedInterfaceOrientations~ipad';\n\nconst requiredIPadInterface = [\n  'UIInterfaceOrientationPortrait',\n  'UIInterfaceOrientationPortraitUpsideDown',\n  'UIInterfaceOrientationLandscapeLeft',\n  'UIInterfaceOrientationLandscapeRight',\n];\n\nfunction isStringArray(value: any): value is string[] {\n  return Array.isArray(value) && value.every((value) => typeof value === 'string');\n}\n\nfunction hasMinimumOrientations(masks: string[]): boolean {\n  return requiredIPadInterface.every((mask) => masks.includes(mask));\n}\n\n/**\n * Require full screen being disabled requires all ipad interfaces to to be added,\n * otherwise submissions to the iOS App Store will fail.\n *\n * ERROR ITMS-90474: \"Invalid Bundle. iPad Multitasking support requires these orientations: 'UIInterfaceOrientationPortrait,UIInterfaceOrientationPortraitUpsideDown,UIInterfaceOrientationLandscapeLeft,UIInterfaceOrientationLandscapeRight'. Found 'UIInterfaceOrientationPortrait,UIInterfaceOrientationPortraitUpsideDown' in bundle 'com.bacon.app'.\"\n *\n * @param interfaceOrientations\n * @returns\n */\nfunction resolveExistingIpadInterfaceOrientations(interfaceOrientations: any): string[] {\n  if (\n    // Ensure type.\n    isStringArray(interfaceOrientations) &&\n    // Don't warn if it's an empty array, this is invalid regardless.\n    interfaceOrientations.length &&\n    // Check if the minimum requirements are met.\n    !hasMinimumOrientations(interfaceOrientations)\n  ) {\n    const existingList = interfaceOrientations!.join(', ');\n    addWarningIOS(\n      'ios.requireFullScreen',\n      `iPad multitasking requires all \\`${iPadInterfaceKey}\\` orientations to be defined in the Info.plist. The Info.plist currently defines values that are incompatible with multitasking, these will be overwritten to prevent submission failure. Existing: ${existingList}`\n    );\n    return interfaceOrientations;\n  }\n  return [];\n}\n\n// Whether requires full screen on iPad\nexport function setRequiresFullScreen(\n  config: Pick<ExpoConfig, 'ios'>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  const requiresFullScreen = getRequiresFullScreen(config);\n  if (!requiresFullScreen) {\n    const existing = resolveExistingIpadInterfaceOrientations(infoPlist[iPadInterfaceKey]);\n\n    // There currently exists no mechanism to safely undo this feature besides `npx expo prebuild --clear`,\n    // this seems ok though because anyone using `UISupportedInterfaceOrientations~ipad` probably\n    // wants them to be defined to this value anyways. This is also the default value used in the Xcode iOS template.\n\n    // Merge any previous interfaces with the required interfaces.\n    infoPlist[iPadInterfaceKey] = [...new Set(existing.concat(requiredIPadInterface))];\n  }\n\n  return {\n    ...infoPlist,\n    UIRequiresFullScreen: requiresFullScreen,\n  };\n}\n"], "mappings": ";;;;;;;;AAEA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,UAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,SAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,MAAMI,sBAAsB,GAAG,IAAAC,mCAAqB,EACzDC,qBAAqB,EACrB,wBAAwB,CACzB;;AAED;AACA;AAAAC,OAAA,CAAAH,sBAAA,GAAAA,sBAAA;AACO,SAASI,qBAAqBA,CAACC,MAA8C,EAAE;EAAA,IAAAC,WAAA;EACpF;EACA;EACA,KAAAA,WAAA,GAAID,MAAM,CAACE,GAAG,cAAAD,WAAA,eAAVA,WAAA,CAAYE,cAAc,CAAC,mBAAmB,CAAC,EAAE;IACnD,OAAO,CAAC,CAACH,MAAM,CAACE,GAAG,CAACE,iBAAiB;EACvC,CAAC,MAAM;IACL;IACA,IACE,IAAAC,yBAAa,EAACL,MAAM,EAAE,QAAQ;IAC9B;IACA;IAAA,EACA;MACA,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;AACF;AAEA,MAAMM,gBAAgB,GAAG,uCAAuC;AAEhE,MAAMC,qBAAqB,GAAG,CAC5B,gCAAgC,EAChC,0CAA0C,EAC1C,qCAAqC,EACrC,sCAAsC,CACvC;AAED,SAASC,aAAaA,CAACC,KAAU,EAAqB;EACpD,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,KAAK,CAAEH,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,CAAC;AAClF;AAEA,SAASI,sBAAsBA,CAACC,KAAe,EAAW;EACxD,OAAOP,qBAAqB,CAACK,KAAK,CAAEG,IAAI,IAAKD,KAAK,CAACE,QAAQ,CAACD,IAAI,CAAC,CAAC;AACpE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,wCAAwCA,CAACC,qBAA0B,EAAY;EACtF;EACE;EACAV,aAAa,CAACU,qBAAqB,CAAC;EACpC;EACAA,qBAAqB,CAACC,MAAM;EAC5B;EACA,CAACN,sBAAsB,CAACK,qBAAqB,CAAC,EAC9C;IACA,MAAME,YAAY,GAAGF,qBAAqB,CAAEG,IAAI,CAAC,IAAI,CAAC;IACtD,IAAAC,yBAAa,EACX,uBAAuB,EACtB,oCAAmChB,gBAAiB,wMAAuMc,YAAa,EAAC,CAC3Q;IACD,OAAOF,qBAAqB;EAC9B;EACA,OAAO,EAAE;AACX;;AAEA;AACO,SAASrB,qBAAqBA,CACnCG,MAA+B,EAC/BuB,SAAoB,EACT;EACX,MAAMC,kBAAkB,GAAGzB,qBAAqB,CAACC,MAAM,CAAC;EACxD,IAAI,CAACwB,kBAAkB,EAAE;IACvB,MAAMC,QAAQ,GAAGR,wCAAwC,CAACM,SAAS,CAACjB,gBAAgB,CAAC,CAAC;;IAEtF;IACA;IACA;;IAEA;IACAiB,SAAS,CAACjB,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAIoB,GAAG,CAACD,QAAQ,CAACE,MAAM,CAACpB,qBAAqB,CAAC,CAAC,CAAC;EACpF;EAEA,OAAO;IACL,GAAGgB,SAAS;IACZK,oBAAoB,EAAEJ;EACxB,CAAC;AACH"}