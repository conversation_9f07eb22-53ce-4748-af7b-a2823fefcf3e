{"version": 3, "file": "BuildScheme.js", "names": ["_XML", "data", "require", "_Paths", "_Target", "_Xcodeproj", "getSchemesFromXcodeproj", "projectRoot", "findSchemeNames", "getRunnableSchemesFromXcodeproj", "configuration", "project", "getPbxproj", "findSignableTargets", "map", "target", "osType", "type", "unquote", "productType", "TargetType", "WATCH", "startsWith", "APPLICATION", "xcConfigurationList", "hash", "objects", "XCConfigurationList", "buildConfigurationList", "buildConfiguration", "buildConfigurations", "find", "value", "comment", "_project$hash$project", "xcBuildConfiguration", "XCBuildConfiguration", "buildSdkRoot", "buildSettings", "SDKROOT", "name", "readSchemeAsync", "scheme", "allSchemePaths", "findSchemePaths", "re", "RegExp", "schemePath", "i", "exec", "readXMLAsync", "path", "Error", "getApplicationTargetNameForSchemeAsync", "_schemeXML$Scheme", "_schemeXML$Scheme$Bui", "_schemeXML$Scheme$Bui2", "_schemeXML$Scheme$Bui3", "_schemeXML$Scheme$Bui4", "schemeXML", "buildActionEntry", "Scheme", "BuildAction", "BuildActionEntries", "BuildActionEntry", "targetName", "length", "getBlueprintName", "entry", "_entry$BuildableRefer", "_entry$BuildableRefer2", "_entry$BuildableRefer3", "_entry$BuildableRefer4", "BuildableReference", "BuildableName", "endsWith", "getArchiveBuildConfigurationForSchemeAsync", "_schemeXML$Scheme2", "_schemeXML$Scheme2$Ar", "_schemeXML$Scheme2$Ar2", "_schemeXML$Scheme2$Ar3", "ArchiveAction", "_entry$BuildableRefer5", "_entry$BuildableRefer6", "_entry$BuildableRefer7", "BlueprintName"], "sources": ["../../src/ios/BuildScheme.ts"], "sourcesContent": ["import { readXMLAsync } from '../utils/XML';\nimport { findSchemeNames, findSchemePaths } from './Paths';\nimport { findSignableTargets, TargetType } from './Target';\nimport { getPbxproj, unquote } from './utils/Xcodeproj';\n\ninterface SchemeXML {\n  Scheme?: {\n    BuildAction?: {\n      BuildActionEntries?: {\n        BuildActionEntry?: BuildActionEntryType[];\n      }[];\n    }[];\n    ArchiveAction?: {\n      $?: {\n        buildConfiguration?: string;\n      };\n    }[];\n  };\n}\n\ninterface BuildActionEntryType {\n  BuildableReference?: {\n    $?: {\n      BlueprintName?: string;\n      BuildableName?: string;\n    };\n  }[];\n}\n\nexport function getSchemesFromXcodeproj(projectRoot: string): string[] {\n  return findSchemeNames(projectRoot);\n}\n\nexport function getRunnableSchemesFromXcodeproj(\n  projectRoot: string,\n  { configuration = 'Debug' }: { configuration?: 'Debug' | 'Release' } = {}\n): { name: string; osType: string; type: string }[] {\n  const project = getPbxproj(projectRoot);\n\n  return findSignableTargets(project).map(([, target]) => {\n    let osType = 'iOS';\n    const type = unquote(target.productType);\n\n    if (type === TargetType.WATCH) {\n      osType = 'watchOS';\n    } else if (\n      // (apps) com.apple.product-type.application\n      // (app clips) com.apple.product-type.application.on-demand-install-capable\n      // NOTE(EvanBacon): This matches against `watchOS` as well so we check for watch first.\n      type.startsWith(TargetType.APPLICATION)\n    ) {\n      // Attempt to resolve the platform SDK for each target so we can filter devices.\n      const xcConfigurationList =\n        project.hash.project.objects.XCConfigurationList[target.buildConfigurationList];\n\n      if (xcConfigurationList) {\n        const buildConfiguration =\n          xcConfigurationList.buildConfigurations.find(\n            (value: { comment: string; value: string }) => value.comment === configuration\n          ) || xcConfigurationList.buildConfigurations[0];\n        if (buildConfiguration?.value) {\n          const xcBuildConfiguration =\n            project.hash.project.objects.XCBuildConfiguration?.[buildConfiguration.value];\n\n          const buildSdkRoot = xcBuildConfiguration.buildSettings.SDKROOT;\n          if (\n            buildSdkRoot === 'appletvos' ||\n            'TVOS_DEPLOYMENT_TARGET' in xcBuildConfiguration.buildSettings\n          ) {\n            // Is a TV app...\n            osType = 'tvOS';\n          } else if (buildSdkRoot === 'iphoneos') {\n            osType = 'iOS';\n          }\n        }\n      }\n    }\n\n    return {\n      name: unquote(target.name),\n      osType,\n      type: unquote(target.productType),\n    };\n  });\n}\n\nasync function readSchemeAsync(\n  projectRoot: string,\n  scheme: string\n): Promise<SchemeXML | undefined> {\n  const allSchemePaths = findSchemePaths(projectRoot);\n  const re = new RegExp(`/${scheme}.xcscheme`, 'i');\n  const schemePath = allSchemePaths.find((i) => re.exec(i));\n  if (schemePath) {\n    return (await readXMLAsync({ path: schemePath })) as unknown as SchemeXML | undefined;\n  } else {\n    throw new Error(`scheme '${scheme}' does not exist, make sure it's marked as shared`);\n  }\n}\n\nexport async function getApplicationTargetNameForSchemeAsync(\n  projectRoot: string,\n  scheme: string\n): Promise<string> {\n  const schemeXML = await readSchemeAsync(projectRoot, scheme);\n  const buildActionEntry =\n    schemeXML?.Scheme?.BuildAction?.[0]?.BuildActionEntries?.[0]?.BuildActionEntry;\n  const targetName =\n    buildActionEntry?.length === 1\n      ? getBlueprintName(buildActionEntry[0])\n      : getBlueprintName(\n          buildActionEntry?.find((entry) => {\n            return entry.BuildableReference?.[0]?.['$']?.BuildableName?.endsWith('.app');\n          })\n        );\n  if (!targetName) {\n    throw new Error(`${scheme}.xcscheme seems to be corrupted`);\n  }\n  return targetName;\n}\n\nexport async function getArchiveBuildConfigurationForSchemeAsync(\n  projectRoot: string,\n  scheme: string\n): Promise<string> {\n  const schemeXML = await readSchemeAsync(projectRoot, scheme);\n  const buildConfiguration = schemeXML?.Scheme?.ArchiveAction?.[0]?.['$']?.buildConfiguration;\n  if (!buildConfiguration) {\n    throw new Error(`${scheme}.xcscheme seems to be corrupted`);\n  }\n  return buildConfiguration;\n}\n\nfunction getBlueprintName(entry?: BuildActionEntryType): string | undefined {\n  return entry?.BuildableReference?.[0]?.['$']?.BlueprintName;\n}\n"], "mappings": ";;;;;;;;;AAAA,SAAAA,KAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,IAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,WAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,UAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AA0BO,SAASK,uBAAuBA,CAACC,WAAmB,EAAY;EACrE,OAAO,IAAAC,wBAAe,EAACD,WAAW,CAAC;AACrC;AAEO,SAASE,+BAA+BA,CAC7CF,WAAmB,EACnB;EAAEG,aAAa,GAAG;AAAiD,CAAC,GAAG,CAAC,CAAC,EACvB;EAClD,MAAMC,OAAO,GAAG,IAAAC,uBAAU,EAACL,WAAW,CAAC;EAEvC,OAAO,IAAAM,6BAAmB,EAACF,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,GAAGC,MAAM,CAAC,KAAK;IACtD,IAAIC,MAAM,GAAG,KAAK;IAClB,MAAMC,IAAI,GAAG,IAAAC,oBAAO,EAACH,MAAM,CAACI,WAAW,CAAC;IAExC,IAAIF,IAAI,KAAKG,oBAAU,CAACC,KAAK,EAAE;MAC7BL,MAAM,GAAG,SAAS;IACpB,CAAC,MAAM;IACL;IACA;IACA;IACAC,IAAI,CAACK,UAAU,CAACF,oBAAU,CAACG,WAAW,CAAC,EACvC;MACA;MACA,MAAMC,mBAAmB,GACvBb,OAAO,CAACc,IAAI,CAACd,OAAO,CAACe,OAAO,CAACC,mBAAmB,CAACZ,MAAM,CAACa,sBAAsB,CAAC;MAEjF,IAAIJ,mBAAmB,EAAE;QACvB,MAAMK,kBAAkB,GACtBL,mBAAmB,CAACM,mBAAmB,CAACC,IAAI,CACzCC,KAAyC,IAAKA,KAAK,CAACC,OAAO,KAAKvB,aAAa,CAC/E,IAAIc,mBAAmB,CAACM,mBAAmB,CAAC,CAAC,CAAC;QACjD,IAAID,kBAAkB,aAAlBA,kBAAkB,eAAlBA,kBAAkB,CAAEG,KAAK,EAAE;UAAA,IAAAE,qBAAA;UAC7B,MAAMC,oBAAoB,IAAAD,qBAAA,GACxBvB,OAAO,CAACc,IAAI,CAACd,OAAO,CAACe,OAAO,CAACU,oBAAoB,cAAAF,qBAAA,uBAAjDA,qBAAA,CAAoDL,kBAAkB,CAACG,KAAK,CAAC;UAE/E,MAAMK,YAAY,GAAGF,oBAAoB,CAACG,aAAa,CAACC,OAAO;UAC/D,IACEF,YAAY,KAAK,WAAW,IAC5B,wBAAwB,IAAIF,oBAAoB,CAACG,aAAa,EAC9D;YACA;YACAtB,MAAM,GAAG,MAAM;UACjB,CAAC,MAAM,IAAIqB,YAAY,KAAK,UAAU,EAAE;YACtCrB,MAAM,GAAG,KAAK;UAChB;QACF;MACF;IACF;IAEA,OAAO;MACLwB,IAAI,EAAE,IAAAtB,oBAAO,EAACH,MAAM,CAACyB,IAAI,CAAC;MAC1BxB,MAAM;MACNC,IAAI,EAAE,IAAAC,oBAAO,EAACH,MAAM,CAACI,WAAW;IAClC,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,eAAesB,eAAeA,CAC5BlC,WAAmB,EACnBmC,MAAc,EACkB;EAChC,MAAMC,cAAc,GAAG,IAAAC,wBAAe,EAACrC,WAAW,CAAC;EACnD,MAAMsC,EAAE,GAAG,IAAIC,MAAM,CAAE,IAAGJ,MAAO,WAAU,EAAE,GAAG,CAAC;EACjD,MAAMK,UAAU,GAAGJ,cAAc,CAACZ,IAAI,CAAEiB,CAAC,IAAKH,EAAE,CAACI,IAAI,CAACD,CAAC,CAAC,CAAC;EACzD,IAAID,UAAU,EAAE;IACd,OAAQ,MAAM,IAAAG,mBAAY,EAAC;MAAEC,IAAI,EAAEJ;IAAW,CAAC,CAAC;EAClD,CAAC,MAAM;IACL,MAAM,IAAIK,KAAK,CAAE,WAAUV,MAAO,mDAAkD,CAAC;EACvF;AACF;AAEO,eAAeW,sCAAsCA,CAC1D9C,WAAmB,EACnBmC,MAAc,EACG;EAAA,IAAAY,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACjB,MAAMC,SAAS,GAAG,MAAMlB,eAAe,CAAClC,WAAW,EAAEmC,MAAM,CAAC;EAC5D,MAAMkB,gBAAgB,GACpBD,SAAS,aAATA,SAAS,wBAAAL,iBAAA,GAATK,SAAS,CAAEE,MAAM,cAAAP,iBAAA,wBAAAC,qBAAA,GAAjBD,iBAAA,CAAmBQ,WAAW,cAAAP,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAiC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAnCD,sBAAA,CAAqCO,kBAAkB,cAAAN,sBAAA,wBAAAC,sBAAA,GAAvDD,sBAAA,CAA0D,CAAC,CAAC,cAAAC,sBAAA,uBAA5DA,sBAAA,CAA8DM,gBAAgB;EAChF,MAAMC,UAAU,GACd,CAAAL,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEM,MAAM,MAAK,CAAC,GAC1BC,gBAAgB,CAACP,gBAAgB,CAAC,CAAC,CAAC,CAAC,GACrCO,gBAAgB,CACdP,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE7B,IAAI,CAAEqC,KAAK,IAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAChC,QAAAH,qBAAA,GAAOD,KAAK,CAACK,kBAAkB,cAAAJ,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA2B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAAgC,GAAG,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCG,aAAa,cAAAF,sBAAA,uBAAnDA,sBAAA,CAAqDG,QAAQ,CAAC,MAAM,CAAC;EAC9E,CAAC,CAAC,CACH;EACP,IAAI,CAACV,UAAU,EAAE;IACf,MAAM,IAAIb,KAAK,CAAE,GAAEV,MAAO,iCAAgC,CAAC;EAC7D;EACA,OAAOuB,UAAU;AACnB;AAEO,eAAeW,0CAA0CA,CAC9DrE,WAAmB,EACnBmC,MAAc,EACG;EAAA,IAAAmC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACjB,MAAMrB,SAAS,GAAG,MAAMlB,eAAe,CAAClC,WAAW,EAAEmC,MAAM,CAAC;EAC5D,MAAMb,kBAAkB,GAAG8B,SAAS,aAATA,SAAS,wBAAAkB,kBAAA,GAATlB,SAAS,CAAEE,MAAM,cAAAgB,kBAAA,wBAAAC,qBAAA,GAAjBD,kBAAA,CAAmBI,aAAa,cAAAH,qBAAA,wBAAAC,sBAAA,GAAhCD,qBAAA,CAAmC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAwC,GAAG,CAAC,cAAAC,sBAAA,uBAA5CA,sBAAA,CAA8CnD,kBAAkB;EAC3F,IAAI,CAACA,kBAAkB,EAAE;IACvB,MAAM,IAAIuB,KAAK,CAAE,GAAEV,MAAO,iCAAgC,CAAC;EAC7D;EACA,OAAOb,kBAAkB;AAC3B;AAEA,SAASsC,gBAAgBA,CAACC,KAA4B,EAAsB;EAAA,IAAAc,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC1E,OAAOhB,KAAK,aAALA,KAAK,wBAAAc,sBAAA,GAALd,KAAK,CAAEK,kBAAkB,cAAAS,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAiC,GAAG,CAAC,cAAAC,sBAAA,uBAArCA,sBAAA,CAAuCC,aAAa;AAC7D"}