{"version": 3, "file": "Version.js", "names": ["_iosPlugins", "data", "require", "withVersion", "createInfoPlistPluginWithPropertyGuard", "setVersion", "infoPlistProperty", "expoConfigProperty", "exports", "withBuildNumber", "setBuildNumber", "getVersion", "config", "version", "infoPlist", "CFBundleShortVersionString", "getBuildNumber", "_config$ios", "ios", "buildNumber", "CFBundleVersion"], "sources": ["../../src/ios/Version.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\nimport { InfoPlist } from './IosConfig.types';\n\nexport const withVersion = createInfoPlistPluginWithPropertyGuard(\n  setVersion,\n  {\n    infoPlistProperty: 'CFBundleShortVersionString',\n    expoConfigProperty: 'version',\n  },\n  'withVersion'\n);\n\nexport const withBuildNumber = createInfoPlistPluginWithPropertyGuard(\n  setBuildNumber,\n  {\n    infoPlistProperty: 'CFBundleVersion',\n    expoConfigProperty: 'ios.buildNumber',\n  },\n  'withBuildNumber'\n);\n\nexport function getVersion(config: Pick<ExpoConfig, 'version'>) {\n  return config.version || '1.0.0';\n}\n\nexport function setVersion(config: Pick<ExpoConfig, 'version'>, infoPlist: InfoPlist): InfoPlist {\n  return {\n    ...infoPlist,\n    CFBundleShortVersionString: getVersion(config),\n  };\n}\n\nexport function getBuildNumber(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.buildNumber ? config.ios.buildNumber : '1';\n}\n\nexport function setBuildNumber(config: Pick<ExpoConfig, 'ios'>, infoPlist: InfoPlist): InfoPlist {\n  return {\n    ...infoPlist,\n    CFBundleVersion: getBuildNumber(config),\n  };\n}\n"], "mappings": ";;;;;;;;;;AAEA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGO,MAAME,WAAW,GAAG,IAAAC,oDAAsC,EAC/DC,UAAU,EACV;EACEC,iBAAiB,EAAE,4BAA4B;EAC/CC,kBAAkB,EAAE;AACtB,CAAC,EACD,aAAa,CACd;AAACC,OAAA,CAAAL,WAAA,GAAAA,WAAA;AAEK,MAAMM,eAAe,GAAG,IAAAL,oDAAsC,EACnEM,cAAc,EACd;EACEJ,iBAAiB,EAAE,iBAAiB;EACpCC,kBAAkB,EAAE;AACtB,CAAC,EACD,iBAAiB,CAClB;AAACC,OAAA,CAAAC,eAAA,GAAAA,eAAA;AAEK,SAASE,UAAUA,CAACC,MAAmC,EAAE;EAC9D,OAAOA,MAAM,CAACC,OAAO,IAAI,OAAO;AAClC;AAEO,SAASR,UAAUA,CAACO,MAAmC,EAAEE,SAAoB,EAAa;EAC/F,OAAO;IACL,GAAGA,SAAS;IACZC,0BAA0B,EAAEJ,UAAU,CAACC,MAAM;EAC/C,CAAC;AACH;AAEO,SAASI,cAAcA,CAACJ,MAA+B,EAAE;EAAA,IAAAK,WAAA;EAC9D,OAAO,CAAAA,WAAA,GAAAL,MAAM,CAACM,GAAG,cAAAD,WAAA,eAAVA,WAAA,CAAYE,WAAW,GAAGP,MAAM,CAACM,GAAG,CAACC,WAAW,GAAG,GAAG;AAC/D;AAEO,SAAST,cAAcA,CAACE,MAA+B,EAAEE,SAAoB,EAAa;EAC/F,OAAO;IACL,GAAGA,SAAS;IACZM,eAAe,EAAEJ,cAAc,CAACJ,MAAM;EACxC,CAAC;AACH"}