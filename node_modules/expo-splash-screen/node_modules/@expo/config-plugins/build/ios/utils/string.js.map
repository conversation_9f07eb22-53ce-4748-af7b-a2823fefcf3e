{"version": 3, "file": "string.js", "names": ["trimQuotes", "s", "length", "slice"], "sources": ["../../../src/ios/utils/string.ts"], "sourcesContent": ["export function trimQuotes(s: string): string {\n  return s && s[0] === '\"' && s[s.length - 1] === '\"' ? s.slice(1, -1) : s;\n}\n"], "mappings": ";;;;;;AAAO,SAASA,UAAUA,CAACC,CAAS,EAAU;EAC5C,OAAOA,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAGD,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGF,CAAC;AAC1E"}