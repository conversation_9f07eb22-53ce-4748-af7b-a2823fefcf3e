{"version": 3, "file": "Updates.js", "names": ["path", "data", "_interopRequireWildcard", "require", "_resolveFrom", "_interopRequireDefault", "_iosPlugins", "_Updates", "obj", "__esModule", "default", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "CREATE_MANIFEST_IOS_PATH", "Config", "exports", "withUpdates", "config", "expoUsername", "withExpoPlist", "projectRoot", "modRequest", "expoUpdatesPackageVersion", "getExpoUpdatesPackageVersion", "modResults", "setUpdatesConfig", "expoPlist", "username", "newExpoPlist", "ENABLED", "getUpdatesEnabled", "CHECK_ON_LAUNCH", "getUpdatesCheckOnLaunch", "LAUNCH_WAIT_MS", "getUpdatesTimeout", "updateUrl", "getUpdateUrl", "UPDATE_URL", "codeSigningCertificate", "getUpdatesCodeSigningCertificate", "CODE_SIGNING_CERTIFICATE", "codeSigningMetadata", "getUpdatesCodeSigningMetadata", "CODE_SIGNING_METADATA", "requestHeaders", "getUpdatesRequestHeaders", "UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY", "setVersionsConfig", "runtimeVersion", "getRuntimeVersionNullable", "RUNTIME_VERSION", "Error", "sdkVersion", "getSDKVersion", "SDK_VERSION", "formatConfigurationScriptPath", "buildScriptPath", "resolveFrom", "silent", "relativePath", "relative", "join", "process", "platform", "replace", "getBundleReactNativePhase", "project", "shellScriptBuildPhase", "hash", "objects", "PBXShellScriptBuildPhase", "bundleReactNative", "values", "find", "buildPhase", "name", "ensureBundleReactNativePhaseContainsConfigurationScript", "buildPhaseShellScriptPath", "isShellScriptBuildPhaseConfigured", "shellScript", "includes", "RegExp", "isPlistConfigurationSet", "Boolean", "EXUpdatesURL", "EXUpdatesSDKVersion", "EXUpdatesRuntimeVersion", "isPlistConfigurationSynced", "EXUpdatesEnabled", "EXUpdatesLaunchWaitMs", "EXUpdatesCheckOnLaunch", "EXUpdatesCodeSigningCertificate", "EXUpdatesCodeSigningMetadata", "isPlistVersionConfigurationSynced", "_expoPlist$EXUpdatesR", "_expoPlist$EXUpdatesS", "expectedRuntimeVersion", "expectedSdkVersion", "currentRuntimeVersion", "currentSdkVersion"], "sources": ["../../src/ios/Updates.ts"], "sourcesContent": ["import * as path from 'path';\nimport resolveFrom from 'resolve-from';\nimport xcode from 'xcode';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withExpoPlist } from '../plugins/ios-plugins';\nimport {\n  ExpoConfigUpdates,\n  getExpoUpdatesPackageVersion,\n  getRuntimeVersionNullable,\n  getSDKVersion,\n  getUpdatesCheckOnLaunch,\n  getUpdatesCodeSigningCertificate,\n  getUpdatesCodeSigningMetadata,\n  getUpdatesRequestHeaders,\n  getUpdatesEnabled,\n  getUpdatesTimeout,\n  getUpdateUrl,\n} from '../utils/Updates';\nimport { ExpoPlist } from './IosConfig.types';\n\nconst CREATE_MANIFEST_IOS_PATH = 'expo-updates/scripts/create-manifest-ios.sh';\n\nexport enum Config {\n  ENABLED = 'EXUpdatesEnabled',\n  CHECK_ON_LAUNCH = 'EXUpdatesCheckOnLaunch',\n  LAUNCH_WAIT_MS = 'EXUpdatesLaunchWaitMs',\n  RUNTIME_VERSION = 'EXUpdatesRuntimeVersion',\n  SDK_VERSION = 'EXUpdatesSDKVersion',\n  UPDATE_URL = 'EXUpdatesURL',\n  RELEASE_CHANNEL = 'EXUpdatesReleaseChannel',\n  UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY = 'EXUpdatesRequestHeaders',\n  CODE_SIGNING_CERTIFICATE = 'EXUpdatesCodeSigningCertificate',\n  CODE_SIGNING_METADATA = 'EXUpdatesCodeSigningMetadata',\n}\n\n// when making changes to this config plugin, ensure the same changes are also made in eas-cli and build-tools\n// Also ensure the docs are up-to-date: https://docs.expo.dev/bare/installing-updates/\n\nexport const withUpdates: ConfigPlugin<{ expoUsername: string | null }> = (\n  config,\n  { expoUsername }\n) => {\n  return withExpoPlist(config, (config) => {\n    const projectRoot = config.modRequest.projectRoot;\n    const expoUpdatesPackageVersion = getExpoUpdatesPackageVersion(projectRoot);\n    config.modResults = setUpdatesConfig(\n      projectRoot,\n      config,\n      config.modResults,\n      expoUsername,\n      expoUpdatesPackageVersion\n    );\n    return config;\n  });\n};\n\nexport function setUpdatesConfig(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  expoPlist: ExpoPlist,\n  username: string | null,\n  expoUpdatesPackageVersion?: string | null\n): ExpoPlist {\n  const newExpoPlist = {\n    ...expoPlist,\n    [Config.ENABLED]: getUpdatesEnabled(config, username),\n    [Config.CHECK_ON_LAUNCH]: getUpdatesCheckOnLaunch(config, expoUpdatesPackageVersion),\n    [Config.LAUNCH_WAIT_MS]: getUpdatesTimeout(config),\n  };\n\n  const updateUrl = getUpdateUrl(config, username);\n  if (updateUrl) {\n    newExpoPlist[Config.UPDATE_URL] = updateUrl;\n  } else {\n    delete newExpoPlist[Config.UPDATE_URL];\n  }\n\n  const codeSigningCertificate = getUpdatesCodeSigningCertificate(projectRoot, config);\n  if (codeSigningCertificate) {\n    newExpoPlist[Config.CODE_SIGNING_CERTIFICATE] = codeSigningCertificate;\n  } else {\n    delete newExpoPlist[Config.CODE_SIGNING_CERTIFICATE];\n  }\n\n  const codeSigningMetadata = getUpdatesCodeSigningMetadata(config);\n  if (codeSigningMetadata) {\n    newExpoPlist[Config.CODE_SIGNING_METADATA] = codeSigningMetadata;\n  } else {\n    delete newExpoPlist[Config.CODE_SIGNING_METADATA];\n  }\n\n  const requestHeaders = getUpdatesRequestHeaders(config);\n  if (requestHeaders) {\n    newExpoPlist[Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY] = requestHeaders;\n  } else {\n    delete newExpoPlist[Config.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY];\n  }\n\n  return setVersionsConfig(config, newExpoPlist);\n}\n\nexport function setVersionsConfig(config: ExpoConfigUpdates, expoPlist: ExpoPlist): ExpoPlist {\n  const newExpoPlist = { ...expoPlist };\n\n  const runtimeVersion = getRuntimeVersionNullable(config, 'ios');\n  if (!runtimeVersion && expoPlist[Config.RUNTIME_VERSION]) {\n    throw new Error(\n      'A runtime version is set in your Expo.plist, but is missing from your app.json/app.config.js. Please either set runtimeVersion in your app.json/app.config.js or remove EXUpdatesRuntimeVersion from your Expo.plist.'\n    );\n  }\n  const sdkVersion = getSDKVersion(config);\n  if (runtimeVersion) {\n    delete newExpoPlist[Config.SDK_VERSION];\n    newExpoPlist[Config.RUNTIME_VERSION] = runtimeVersion;\n  } else if (sdkVersion) {\n    /**\n     * runtime version maybe null in projects using classic updates. In that\n     * case we use SDK version\n     */\n    delete newExpoPlist[Config.RUNTIME_VERSION];\n    newExpoPlist[Config.SDK_VERSION] = sdkVersion;\n  } else {\n    delete newExpoPlist[Config.SDK_VERSION];\n    delete newExpoPlist[Config.RUNTIME_VERSION];\n  }\n\n  return newExpoPlist;\n}\n\nfunction formatConfigurationScriptPath(projectRoot: string): string {\n  const buildScriptPath = resolveFrom.silent(projectRoot, CREATE_MANIFEST_IOS_PATH);\n\n  if (!buildScriptPath) {\n    throw new Error(\n      \"Could not find the build script for iOS. This could happen in case of outdated 'node_modules'. Run 'npm install' to make sure that it's up-to-date.\"\n    );\n  }\n\n  const relativePath = path.relative(path.join(projectRoot, 'ios'), buildScriptPath);\n  return process.platform === 'win32' ? relativePath.replace(/\\\\/g, '/') : relativePath;\n}\n\ninterface ShellScriptBuildPhase {\n  isa: 'PBXShellScriptBuildPhase';\n  name: string;\n  shellScript: string;\n  [key: string]: any;\n}\n\nexport function getBundleReactNativePhase(project: xcode.XcodeProject): ShellScriptBuildPhase {\n  const shellScriptBuildPhase = project.hash.project.objects.PBXShellScriptBuildPhase as Record<\n    string,\n    ShellScriptBuildPhase\n  >;\n  const bundleReactNative = Object.values(shellScriptBuildPhase).find(\n    (buildPhase) => buildPhase.name === '\"Bundle React Native code and images\"'\n  );\n\n  if (!bundleReactNative) {\n    throw new Error(`Couldn't find a build phase \"Bundle React Native code and images\"`);\n  }\n\n  return bundleReactNative;\n}\n\nexport function ensureBundleReactNativePhaseContainsConfigurationScript(\n  projectRoot: string,\n  project: xcode.XcodeProject\n): xcode.XcodeProject {\n  const bundleReactNative = getBundleReactNativePhase(project);\n  const buildPhaseShellScriptPath = formatConfigurationScriptPath(projectRoot);\n\n  if (!isShellScriptBuildPhaseConfigured(projectRoot, project)) {\n    // check if there's already another path to create-manifest-ios.sh\n    // this might be the case for monorepos\n    if (bundleReactNative.shellScript.includes(CREATE_MANIFEST_IOS_PATH)) {\n      bundleReactNative.shellScript = bundleReactNative.shellScript.replace(\n        new RegExp(`(\\\\\\\\n)(\\\\.\\\\.)+/node_modules/${CREATE_MANIFEST_IOS_PATH}`),\n        ''\n      );\n    }\n    bundleReactNative.shellScript = `${bundleReactNative.shellScript.replace(\n      /\"$/,\n      ''\n    )}${buildPhaseShellScriptPath}\\\\n\"`;\n  }\n  return project;\n}\n\nexport function isShellScriptBuildPhaseConfigured(\n  projectRoot: string,\n  project: xcode.XcodeProject\n): boolean {\n  const bundleReactNative = getBundleReactNativePhase(project);\n  const buildPhaseShellScriptPath = formatConfigurationScriptPath(projectRoot);\n  return bundleReactNative.shellScript.includes(buildPhaseShellScriptPath);\n}\n\nexport function isPlistConfigurationSet(expoPlist: ExpoPlist): boolean {\n  return Boolean(\n    expoPlist.EXUpdatesURL && (expoPlist.EXUpdatesSDKVersion || expoPlist.EXUpdatesRuntimeVersion)\n  );\n}\n\nexport function isPlistConfigurationSynced(\n  projectRoot: string,\n  config: ExpoConfigUpdates,\n  expoPlist: ExpoPlist,\n  username: string | null\n): boolean {\n  return (\n    getUpdateUrl(config, username) === expoPlist.EXUpdatesURL &&\n    getUpdatesEnabled(config, username) === expoPlist.EXUpdatesEnabled &&\n    getUpdatesTimeout(config) === expoPlist.EXUpdatesLaunchWaitMs &&\n    getUpdatesCheckOnLaunch(config) === expoPlist.EXUpdatesCheckOnLaunch &&\n    getUpdatesCodeSigningCertificate(projectRoot, config) ===\n      expoPlist.EXUpdatesCodeSigningCertificate &&\n    getUpdatesCodeSigningMetadata(config) === expoPlist.EXUpdatesCodeSigningMetadata &&\n    isPlistVersionConfigurationSynced(config, expoPlist)\n  );\n}\n\nexport function isPlistVersionConfigurationSynced(\n  config: Pick<ExpoConfigUpdates, 'sdkVersion' | 'runtimeVersion'>,\n  expoPlist: ExpoPlist\n): boolean {\n  const expectedRuntimeVersion = getRuntimeVersionNullable(config, 'ios');\n  const expectedSdkVersion = getSDKVersion(config);\n\n  const currentRuntimeVersion = expoPlist.EXUpdatesRuntimeVersion ?? null;\n  const currentSdkVersion = expoPlist.EXUpdatesSDKVersion ?? null;\n\n  if (expectedRuntimeVersion !== null) {\n    return currentRuntimeVersion === expectedRuntimeVersion && currentSdkVersion === null;\n  } else if (expectedSdkVersion !== null) {\n    return currentSdkVersion === expectedSdkVersion && currentRuntimeVersion === null;\n  } else {\n    return true;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAAA,KAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,IAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,aAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAF,OAAA;EAAAC,YAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,SAAAK,YAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,WAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,SAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAY0B,SAAAI,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAV,wBAAAM,GAAA,EAAAI,WAAA,SAAAA,WAAA,IAAAJ,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAQ,KAAA,GAAAL,wBAAA,CAAAC,WAAA,OAAAI,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAT,GAAA,YAAAQ,KAAA,CAAAE,GAAA,CAAAV,GAAA,SAAAW,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAhB,GAAA,QAAAgB,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAnB,GAAA,EAAAgB,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAf,GAAA,EAAAgB,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAhB,GAAA,CAAAgB,GAAA,SAAAL,MAAA,CAAAT,OAAA,GAAAF,GAAA,MAAAQ,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAArB,GAAA,EAAAW,MAAA,YAAAA,MAAA;AAG1B,MAAMW,wBAAwB,GAAG,6CAA6C;AAAC,IAEnEC,MAAM,EAalB;AACA;AAAAC,OAAA,CAAAD,MAAA,GAAAA,MAAA;AAAA,WAdYA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;EAANA,MAAM;AAAA,GAANA,MAAM,KAAAC,OAAA,CAAAD,MAAA,GAANA,MAAM;AAgBX,MAAME,WAA0D,GAAGA,CACxEC,MAAM,EACN;EAAEC;AAAa,CAAC,KACb;EACH,OAAO,IAAAC,2BAAa,EAACF,MAAM,EAAGA,MAAM,IAAK;IACvC,MAAMG,WAAW,GAAGH,MAAM,CAACI,UAAU,CAACD,WAAW;IACjD,MAAME,yBAAyB,GAAG,IAAAC,uCAA4B,EAACH,WAAW,CAAC;IAC3EH,MAAM,CAACO,UAAU,GAAGC,gBAAgB,CAClCL,WAAW,EACXH,MAAM,EACNA,MAAM,CAACO,UAAU,EACjBN,YAAY,EACZI,yBAAyB,CAC1B;IACD,OAAOL,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACF,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAEK,SAASS,gBAAgBA,CAC9BL,WAAmB,EACnBH,MAAyB,EACzBS,SAAoB,EACpBC,QAAuB,EACvBL,yBAAyC,EAC9B;EACX,MAAMM,YAAY,GAAG;IACnB,GAAGF,SAAS;IACZ,CAACZ,MAAM,CAACe,OAAO,GAAG,IAAAC,4BAAiB,EAACb,MAAM,EAAEU,QAAQ,CAAC;IACrD,CAACb,MAAM,CAACiB,eAAe,GAAG,IAAAC,kCAAuB,EAACf,MAAM,EAAEK,yBAAyB,CAAC;IACpF,CAACR,MAAM,CAACmB,cAAc,GAAG,IAAAC,4BAAiB,EAACjB,MAAM;EACnD,CAAC;EAED,MAAMkB,SAAS,GAAG,IAAAC,uBAAY,EAACnB,MAAM,EAAEU,QAAQ,CAAC;EAChD,IAAIQ,SAAS,EAAE;IACbP,YAAY,CAACd,MAAM,CAACuB,UAAU,CAAC,GAAGF,SAAS;EAC7C,CAAC,MAAM;IACL,OAAOP,YAAY,CAACd,MAAM,CAACuB,UAAU,CAAC;EACxC;EAEA,MAAMC,sBAAsB,GAAG,IAAAC,2CAAgC,EAACnB,WAAW,EAAEH,MAAM,CAAC;EACpF,IAAIqB,sBAAsB,EAAE;IAC1BV,YAAY,CAACd,MAAM,CAAC0B,wBAAwB,CAAC,GAAGF,sBAAsB;EACxE,CAAC,MAAM;IACL,OAAOV,YAAY,CAACd,MAAM,CAAC0B,wBAAwB,CAAC;EACtD;EAEA,MAAMC,mBAAmB,GAAG,IAAAC,wCAA6B,EAACzB,MAAM,CAAC;EACjE,IAAIwB,mBAAmB,EAAE;IACvBb,YAAY,CAACd,MAAM,CAAC6B,qBAAqB,CAAC,GAAGF,mBAAmB;EAClE,CAAC,MAAM;IACL,OAAOb,YAAY,CAACd,MAAM,CAAC6B,qBAAqB,CAAC;EACnD;EAEA,MAAMC,cAAc,GAAG,IAAAC,mCAAwB,EAAC5B,MAAM,CAAC;EACvD,IAAI2B,cAAc,EAAE;IAClBhB,YAAY,CAACd,MAAM,CAACgC,yCAAyC,CAAC,GAAGF,cAAc;EACjF,CAAC,MAAM;IACL,OAAOhB,YAAY,CAACd,MAAM,CAACgC,yCAAyC,CAAC;EACvE;EAEA,OAAOC,iBAAiB,CAAC9B,MAAM,EAAEW,YAAY,CAAC;AAChD;AAEO,SAASmB,iBAAiBA,CAAC9B,MAAyB,EAAES,SAAoB,EAAa;EAC5F,MAAME,YAAY,GAAG;IAAE,GAAGF;EAAU,CAAC;EAErC,MAAMsB,cAAc,GAAG,IAAAC,oCAAyB,EAAChC,MAAM,EAAE,KAAK,CAAC;EAC/D,IAAI,CAAC+B,cAAc,IAAItB,SAAS,CAACZ,MAAM,CAACoC,eAAe,CAAC,EAAE;IACxD,MAAM,IAAIC,KAAK,CACb,uNAAuN,CACxN;EACH;EACA,MAAMC,UAAU,GAAG,IAAAC,wBAAa,EAACpC,MAAM,CAAC;EACxC,IAAI+B,cAAc,EAAE;IAClB,OAAOpB,YAAY,CAACd,MAAM,CAACwC,WAAW,CAAC;IACvC1B,YAAY,CAACd,MAAM,CAACoC,eAAe,CAAC,GAAGF,cAAc;EACvD,CAAC,MAAM,IAAII,UAAU,EAAE;IACrB;AACJ;AACA;AACA;IACI,OAAOxB,YAAY,CAACd,MAAM,CAACoC,eAAe,CAAC;IAC3CtB,YAAY,CAACd,MAAM,CAACwC,WAAW,CAAC,GAAGF,UAAU;EAC/C,CAAC,MAAM;IACL,OAAOxB,YAAY,CAACd,MAAM,CAACwC,WAAW,CAAC;IACvC,OAAO1B,YAAY,CAACd,MAAM,CAACoC,eAAe,CAAC;EAC7C;EAEA,OAAOtB,YAAY;AACrB;AAEA,SAAS2B,6BAA6BA,CAACnC,WAAmB,EAAU;EAClE,MAAMoC,eAAe,GAAGC,sBAAW,CAACC,MAAM,CAACtC,WAAW,EAAEP,wBAAwB,CAAC;EAEjF,IAAI,CAAC2C,eAAe,EAAE;IACpB,MAAM,IAAIL,KAAK,CACb,qJAAqJ,CACtJ;EACH;EAEA,MAAMQ,YAAY,GAAG5E,IAAI,GAAC6E,QAAQ,CAAC7E,IAAI,GAAC8E,IAAI,CAACzC,WAAW,EAAE,KAAK,CAAC,EAAEoC,eAAe,CAAC;EAClF,OAAOM,OAAO,CAACC,QAAQ,KAAK,OAAO,GAAGJ,YAAY,CAACK,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAAGL,YAAY;AACvF;AASO,SAASM,yBAAyBA,CAACC,OAA2B,EAAyB;EAC5F,MAAMC,qBAAqB,GAAGD,OAAO,CAACE,IAAI,CAACF,OAAO,CAACG,OAAO,CAACC,wBAG1D;EACD,MAAMC,iBAAiB,GAAGnE,MAAM,CAACoE,MAAM,CAACL,qBAAqB,CAAC,CAACM,IAAI,CAChEC,UAAU,IAAKA,UAAU,CAACC,IAAI,KAAK,uCAAuC,CAC5E;EAED,IAAI,CAACJ,iBAAiB,EAAE;IACtB,MAAM,IAAIpB,KAAK,CAAE,mEAAkE,CAAC;EACtF;EAEA,OAAOoB,iBAAiB;AAC1B;AAEO,SAASK,uDAAuDA,CACrExD,WAAmB,EACnB8C,OAA2B,EACP;EACpB,MAAMK,iBAAiB,GAAGN,yBAAyB,CAACC,OAAO,CAAC;EAC5D,MAAMW,yBAAyB,GAAGtB,6BAA6B,CAACnC,WAAW,CAAC;EAE5E,IAAI,CAAC0D,iCAAiC,CAAC1D,WAAW,EAAE8C,OAAO,CAAC,EAAE;IAC5D;IACA;IACA,IAAIK,iBAAiB,CAACQ,WAAW,CAACC,QAAQ,CAACnE,wBAAwB,CAAC,EAAE;MACpE0D,iBAAiB,CAACQ,WAAW,GAAGR,iBAAiB,CAACQ,WAAW,CAACf,OAAO,CACnE,IAAIiB,MAAM,CAAE,iCAAgCpE,wBAAyB,EAAC,CAAC,EACvE,EAAE,CACH;IACH;IACA0D,iBAAiB,CAACQ,WAAW,GAAI,GAAER,iBAAiB,CAACQ,WAAW,CAACf,OAAO,CACtE,IAAI,EACJ,EAAE,CACF,GAAEa,yBAA0B,MAAK;EACrC;EACA,OAAOX,OAAO;AAChB;AAEO,SAASY,iCAAiCA,CAC/C1D,WAAmB,EACnB8C,OAA2B,EAClB;EACT,MAAMK,iBAAiB,GAAGN,yBAAyB,CAACC,OAAO,CAAC;EAC5D,MAAMW,yBAAyB,GAAGtB,6BAA6B,CAACnC,WAAW,CAAC;EAC5E,OAAOmD,iBAAiB,CAACQ,WAAW,CAACC,QAAQ,CAACH,yBAAyB,CAAC;AAC1E;AAEO,SAASK,uBAAuBA,CAACxD,SAAoB,EAAW;EACrE,OAAOyD,OAAO,CACZzD,SAAS,CAAC0D,YAAY,KAAK1D,SAAS,CAAC2D,mBAAmB,IAAI3D,SAAS,CAAC4D,uBAAuB,CAAC,CAC/F;AACH;AAEO,SAASC,0BAA0BA,CACxCnE,WAAmB,EACnBH,MAAyB,EACzBS,SAAoB,EACpBC,QAAuB,EACd;EACT,OACE,IAAAS,uBAAY,EAACnB,MAAM,EAAEU,QAAQ,CAAC,KAAKD,SAAS,CAAC0D,YAAY,IACzD,IAAAtD,4BAAiB,EAACb,MAAM,EAAEU,QAAQ,CAAC,KAAKD,SAAS,CAAC8D,gBAAgB,IAClE,IAAAtD,4BAAiB,EAACjB,MAAM,CAAC,KAAKS,SAAS,CAAC+D,qBAAqB,IAC7D,IAAAzD,kCAAuB,EAACf,MAAM,CAAC,KAAKS,SAAS,CAACgE,sBAAsB,IACpE,IAAAnD,2CAAgC,EAACnB,WAAW,EAAEH,MAAM,CAAC,KACnDS,SAAS,CAACiE,+BAA+B,IAC3C,IAAAjD,wCAA6B,EAACzB,MAAM,CAAC,KAAKS,SAAS,CAACkE,4BAA4B,IAChFC,iCAAiC,CAAC5E,MAAM,EAAES,SAAS,CAAC;AAExD;AAEO,SAASmE,iCAAiCA,CAC/C5E,MAAgE,EAChES,SAAoB,EACX;EAAA,IAAAoE,qBAAA,EAAAC,qBAAA;EACT,MAAMC,sBAAsB,GAAG,IAAA/C,oCAAyB,EAAChC,MAAM,EAAE,KAAK,CAAC;EACvE,MAAMgF,kBAAkB,GAAG,IAAA5C,wBAAa,EAACpC,MAAM,CAAC;EAEhD,MAAMiF,qBAAqB,IAAAJ,qBAAA,GAAGpE,SAAS,CAAC4D,uBAAuB,cAAAQ,qBAAA,cAAAA,qBAAA,GAAI,IAAI;EACvE,MAAMK,iBAAiB,IAAAJ,qBAAA,GAAGrE,SAAS,CAAC2D,mBAAmB,cAAAU,qBAAA,cAAAA,qBAAA,GAAI,IAAI;EAE/D,IAAIC,sBAAsB,KAAK,IAAI,EAAE;IACnC,OAAOE,qBAAqB,KAAKF,sBAAsB,IAAIG,iBAAiB,KAAK,IAAI;EACvF,CAAC,MAAM,IAAIF,kBAAkB,KAAK,IAAI,EAAE;IACtC,OAAOE,iBAAiB,KAAKF,kBAAkB,IAAIC,qBAAqB,KAAK,IAAI;EACnF,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF"}