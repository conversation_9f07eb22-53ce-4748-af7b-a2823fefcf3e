{"version": 3, "file": "Entitlements.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_slash", "_iosPlugins", "_Target", "_Xcodeproj", "_string", "obj", "__esModule", "default", "withAssociatedDomains", "createEntitlementsPlugin", "setAssociatedDomains", "exports", "config", "_", "entitlementsPlist", "_config$ios", "ios", "associatedDomains", "getEntitlementsPath", "projectRoot", "targetName", "buildConfiguration", "project", "getPbxproj", "xcBuildConfiguration", "getXCBuildConfigurationFromPbxproj", "entitlementsPath", "getEntitlementsPathFromBuildConfiguration", "fs", "existsSync", "_xcBuildConfiguration", "entitlementsPathRaw", "buildSettings", "CODE_SIGN_ENTITLEMENTS", "path", "normalize", "join", "trimQuotes", "ensureApplicationTargetEntitlementsFileConfigured", "projectName", "getProjectName", "productName", "getProductName", "applicationTarget", "findFirstNativeTarget", "buildConfigurations", "getBuildConfigurationsForListId", "buildConfigurationList", "hasChangesToWrite", "oldEntitlementPath", "entitlementsRelativePath", "slash", "mkdirSync", "dirname", "recursive", "writeFileSync", "ENTITLEMENTS_TEMPLATE", "filepath", "writeSync"], "sources": ["../../src/ios/Entitlements.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport { JSONObject } from '@expo/json-file';\nimport fs from 'fs';\nimport path from 'path';\nimport slash from 'slash';\nimport { XCBuildConfiguration } from 'xcode';\n\nimport { createEntitlementsPlugin } from '../plugins/ios-plugins';\nimport { findFirstNativeTarget, getXCBuildConfigurationFromPbxproj } from './Target';\nimport {\n  getBuildConfigurationsForListId,\n  getPbxproj,\n  getProductName,\n  getProjectName,\n} from './utils/Xcodeproj';\nimport { trimQuotes } from './utils/string';\n\nexport const withAssociatedDomains = createEntitlementsPlugin(\n  setAssociatedDomains,\n  'withAssociatedDomains'\n);\n\nexport function setAssociatedDomains(\n  config: ExpoConfig,\n  { 'com.apple.developer.associated-domains': _, ...entitlementsPlist }: JSONObject\n): JSONObject {\n  if (config.ios?.associatedDomains) {\n    return {\n      ...entitlementsPlist,\n      'com.apple.developer.associated-domains': config.ios.associatedDomains,\n    };\n  }\n\n  return entitlementsPlist;\n}\n\nexport function getEntitlementsPath(\n  projectRoot: string,\n  {\n    targetName,\n    buildConfiguration = 'Release',\n  }: { targetName?: string; buildConfiguration?: string } = {}\n): string | null {\n  const project = getPbxproj(projectRoot);\n  const xcBuildConfiguration = getXCBuildConfigurationFromPbxproj(project, {\n    targetName,\n    buildConfiguration,\n  });\n  if (!xcBuildConfiguration) {\n    return null;\n  }\n  const entitlementsPath = getEntitlementsPathFromBuildConfiguration(\n    projectRoot,\n    xcBuildConfiguration\n  );\n  return entitlementsPath && fs.existsSync(entitlementsPath) ? entitlementsPath : null;\n}\n\nfunction getEntitlementsPathFromBuildConfiguration(\n  projectRoot: string,\n  xcBuildConfiguration: XCBuildConfiguration\n): string | null {\n  const entitlementsPathRaw = xcBuildConfiguration?.buildSettings?.CODE_SIGN_ENTITLEMENTS as\n    | string\n    | undefined;\n  if (entitlementsPathRaw) {\n    return path.normalize(path.join(projectRoot, 'ios', trimQuotes(entitlementsPathRaw)));\n  } else {\n    return null;\n  }\n}\n\nexport function ensureApplicationTargetEntitlementsFileConfigured(projectRoot: string): void {\n  const project = getPbxproj(projectRoot);\n  const projectName = getProjectName(projectRoot);\n  const productName = getProductName(project);\n\n  const [, applicationTarget] = findFirstNativeTarget(project);\n  const buildConfigurations = getBuildConfigurationsForListId(\n    project,\n    applicationTarget.buildConfigurationList\n  );\n  let hasChangesToWrite = false;\n  for (const [, xcBuildConfiguration] of buildConfigurations) {\n    const oldEntitlementPath = getEntitlementsPathFromBuildConfiguration(\n      projectRoot,\n      xcBuildConfiguration\n    );\n    if (oldEntitlementPath && fs.existsSync(oldEntitlementPath)) {\n      return;\n    }\n    hasChangesToWrite = true;\n    // Use posix formatted path, even on Windows\n    const entitlementsRelativePath = slash(path.join(projectName, `${productName}.entitlements`));\n    const entitlementsPath = path.normalize(\n      path.join(projectRoot, 'ios', entitlementsRelativePath)\n    );\n    fs.mkdirSync(path.dirname(entitlementsPath), { recursive: true });\n    if (!fs.existsSync(entitlementsPath)) {\n      fs.writeFileSync(entitlementsPath, ENTITLEMENTS_TEMPLATE);\n    }\n    xcBuildConfiguration.buildSettings.CODE_SIGN_ENTITLEMENTS = entitlementsRelativePath;\n  }\n  if (hasChangesToWrite) {\n    fs.writeFileSync(project.filepath, project.writeSync());\n  }\n}\n\nconst ENTITLEMENTS_TEMPLATE = `\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n<plist version=\"1.0\">\n<dict>\n</dict>\n</plist>\n`;\n"], "mappings": ";;;;;;;;;AAEA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,OAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,MAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAK,YAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,WAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,QAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,WAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,UAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAMA,SAAAQ,QAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4C,SAAAC,uBAAAQ,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAErC,MAAMG,qBAAqB,GAAG,IAAAC,sCAAwB,EAC3DC,oBAAoB,EACpB,uBAAuB,CACxB;AAACC,OAAA,CAAAH,qBAAA,GAAAA,qBAAA;AAEK,SAASE,oBAAoBA,CAClCE,MAAkB,EAClB;EAAE,wCAAwC,EAAEC,CAAC;EAAE,GAAGC;AAA8B,CAAC,EACrE;EAAA,IAAAC,WAAA;EACZ,KAAAA,WAAA,GAAIH,MAAM,CAACI,GAAG,cAAAD,WAAA,eAAVA,WAAA,CAAYE,iBAAiB,EAAE;IACjC,OAAO;MACL,GAAGH,iBAAiB;MACpB,wCAAwC,EAAEF,MAAM,CAACI,GAAG,CAACC;IACvD,CAAC;EACH;EAEA,OAAOH,iBAAiB;AAC1B;AAEO,SAASI,mBAAmBA,CACjCC,WAAmB,EACnB;EACEC,UAAU;EACVC,kBAAkB,GAAG;AAC+B,CAAC,GAAG,CAAC,CAAC,EAC7C;EACf,MAAMC,OAAO,GAAG,IAAAC,uBAAU,EAACJ,WAAW,CAAC;EACvC,MAAMK,oBAAoB,GAAG,IAAAC,4CAAkC,EAACH,OAAO,EAAE;IACvEF,UAAU;IACVC;EACF,CAAC,CAAC;EACF,IAAI,CAACG,oBAAoB,EAAE;IACzB,OAAO,IAAI;EACb;EACA,MAAME,gBAAgB,GAAGC,yCAAyC,CAChER,WAAW,EACXK,oBAAoB,CACrB;EACD,OAAOE,gBAAgB,IAAIE,aAAE,CAACC,UAAU,CAACH,gBAAgB,CAAC,GAAGA,gBAAgB,GAAG,IAAI;AACtF;AAEA,SAASC,yCAAyCA,CAChDR,WAAmB,EACnBK,oBAA0C,EAC3B;EAAA,IAAAM,qBAAA;EACf,MAAMC,mBAAmB,GAAGP,oBAAoB,aAApBA,oBAAoB,wBAAAM,qBAAA,GAApBN,oBAAoB,CAAEQ,aAAa,cAAAF,qBAAA,uBAAnCA,qBAAA,CAAqCG,sBAEpD;EACb,IAAIF,mBAAmB,EAAE;IACvB,OAAOG,eAAI,CAACC,SAAS,CAACD,eAAI,CAACE,IAAI,CAACjB,WAAW,EAAE,KAAK,EAAE,IAAAkB,oBAAU,EAACN,mBAAmB,CAAC,CAAC,CAAC;EACvF,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;AAEO,SAASO,iDAAiDA,CAACnB,WAAmB,EAAQ;EAC3F,MAAMG,OAAO,GAAG,IAAAC,uBAAU,EAACJ,WAAW,CAAC;EACvC,MAAMoB,WAAW,GAAG,IAAAC,2BAAc,EAACrB,WAAW,CAAC;EAC/C,MAAMsB,WAAW,GAAG,IAAAC,2BAAc,EAACpB,OAAO,CAAC;EAE3C,MAAM,GAAGqB,iBAAiB,CAAC,GAAG,IAAAC,+BAAqB,EAACtB,OAAO,CAAC;EAC5D,MAAMuB,mBAAmB,GAAG,IAAAC,4CAA+B,EACzDxB,OAAO,EACPqB,iBAAiB,CAACI,sBAAsB,CACzC;EACD,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,KAAK,MAAM,GAAGxB,oBAAoB,CAAC,IAAIqB,mBAAmB,EAAE;IAC1D,MAAMI,kBAAkB,GAAGtB,yCAAyC,CAClER,WAAW,EACXK,oBAAoB,CACrB;IACD,IAAIyB,kBAAkB,IAAIrB,aAAE,CAACC,UAAU,CAACoB,kBAAkB,CAAC,EAAE;MAC3D;IACF;IACAD,iBAAiB,GAAG,IAAI;IACxB;IACA,MAAME,wBAAwB,GAAG,IAAAC,gBAAK,EAACjB,eAAI,CAACE,IAAI,CAACG,WAAW,EAAG,GAAEE,WAAY,eAAc,CAAC,CAAC;IAC7F,MAAMf,gBAAgB,GAAGQ,eAAI,CAACC,SAAS,CACrCD,eAAI,CAACE,IAAI,CAACjB,WAAW,EAAE,KAAK,EAAE+B,wBAAwB,CAAC,CACxD;IACDtB,aAAE,CAACwB,SAAS,CAAClB,eAAI,CAACmB,OAAO,CAAC3B,gBAAgB,CAAC,EAAE;MAAE4B,SAAS,EAAE;IAAK,CAAC,CAAC;IACjE,IAAI,CAAC1B,aAAE,CAACC,UAAU,CAACH,gBAAgB,CAAC,EAAE;MACpCE,aAAE,CAAC2B,aAAa,CAAC7B,gBAAgB,EAAE8B,qBAAqB,CAAC;IAC3D;IACAhC,oBAAoB,CAACQ,aAAa,CAACC,sBAAsB,GAAGiB,wBAAwB;EACtF;EACA,IAAIF,iBAAiB,EAAE;IACrBpB,aAAE,CAAC2B,aAAa,CAACjC,OAAO,CAACmC,QAAQ,EAAEnC,OAAO,CAACoC,SAAS,EAAE,CAAC;EACzD;AACF;AAEA,MAAMF,qBAAqB,GAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC"}