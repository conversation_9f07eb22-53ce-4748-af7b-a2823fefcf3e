{"version": 3, "file": "Paths.js", "names": ["_fs", "data", "require", "_glob", "path", "_interopRequireWildcard", "_errors", "_warnings", "Entitlements", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "ignoredPaths", "getAppDelegateHeaderFilePath", "projectRoot", "using", "extra", "globSync", "absolute", "cwd", "ignore", "UnexpectedError", "length", "warnMultipleFiles", "tag", "fileName", "getAppDelegateFilePath", "getAppDelegateObjcHeaderFilePath", "getLanguage", "filePath", "extension", "extname", "getFileInfo", "normalize", "contents", "readFileSync", "language", "getAppDelegate", "getSourceRoot", "appDelegate", "dirname", "findSchemePaths", "findSchemeNames", "schemePaths", "map", "schemePath", "parse", "name", "getAllXcodeProjectPaths", "iosFolder", "pbxproj<PERSON><PERSON><PERSON>", "filter", "project", "test", "sort", "a", "b", "isAInIos", "isBInIos", "value", "join", "getXcodeProjectPath", "getAllPBXProjectPaths", "projectPaths", "paths", "existsSync", "getPBXProjectPath", "getAllInfoPlistPaths", "getInfoPlistPath", "getAllEntitlementsPaths", "getEntitlementsPath", "getSupportingPath", "resolve", "basename", "getExpoPlistPath", "supporting<PERSON>ath", "usingPath", "relative", "extraPaths", "v", "addWarningIOS", "JSON", "stringify"], "sources": ["../../src/ios/Paths.ts"], "sourcesContent": ["import { existsSync, readFileSync } from 'fs';\nimport { sync as globSync } from 'glob';\nimport * as path from 'path';\n\nimport { UnexpectedError } from '../utils/errors';\nimport { addWarningIOS } from '../utils/warnings';\nimport * as Entitlements from './Entitlements';\n\nconst ignoredPaths = ['**/@(Carthage|Pods|vendor|node_modules)/**'];\n\ninterface ProjectFile<L extends string = string> {\n  path: string;\n  language: L;\n  contents: string;\n}\n\ntype AppleLanguage = 'objc' | 'objcpp' | 'swift';\n\nexport type AppDelegateProjectFile = ProjectFile<AppleLanguage>;\n\nexport function getAppDelegateHeaderFilePath(projectRoot: string): string {\n  const [using, ...extra] = globSync('ios/*/AppDelegate.h', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n\n  if (!using) {\n    throw new UnexpectedError(\n      `Could not locate a valid AppDelegate header at root: \"${projectRoot}\"`\n    );\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate-header',\n      fileName: 'AppDelegate',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAppDelegateFilePath(projectRoot: string): string {\n  const [using, ...extra] = globSync('ios/*/AppDelegate.@(m|mm|swift)', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n\n  if (!using) {\n    throw new UnexpectedError(`Could not locate a valid AppDelegate at root: \"${projectRoot}\"`);\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate',\n      fileName: 'AppDelegate',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAppDelegateObjcHeaderFilePath(projectRoot: string): string {\n  const [using, ...extra] = globSync('ios/*/AppDelegate.h', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n\n  if (!using) {\n    throw new UnexpectedError(`Could not locate a valid AppDelegate.h at root: \"${projectRoot}\"`);\n  }\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'app-delegate-objc-header',\n      fileName: 'AppDelegate.h',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nfunction getLanguage(filePath: string): AppleLanguage {\n  const extension = path.extname(filePath);\n  switch (extension) {\n    case '.mm':\n      return 'objcpp';\n    case '.m':\n    case '.h':\n      return 'objc';\n    case '.swift':\n      return 'swift';\n    default:\n      throw new UnexpectedError(`Unexpected iOS file extension: ${extension}`);\n  }\n}\n\nexport function getFileInfo(filePath: string) {\n  return {\n    path: path.normalize(filePath),\n    contents: readFileSync(filePath, 'utf8'),\n    language: getLanguage(filePath),\n  };\n}\n\nexport function getAppDelegate(projectRoot: string): AppDelegateProjectFile {\n  const filePath = getAppDelegateFilePath(projectRoot);\n  return getFileInfo(filePath);\n}\n\nexport function getSourceRoot(projectRoot: string): string {\n  const appDelegate = getAppDelegate(projectRoot);\n  return path.dirname(appDelegate.path);\n}\n\nexport function findSchemePaths(projectRoot: string): string[] {\n  return globSync('ios/*.xcodeproj/xcshareddata/xcschemes/*.xcscheme', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n}\n\nexport function findSchemeNames(projectRoot: string): string[] {\n  const schemePaths = findSchemePaths(projectRoot);\n  return schemePaths.map((schemePath) => path.parse(schemePath).name);\n}\n\nexport function getAllXcodeProjectPaths(projectRoot: string): string[] {\n  const iosFolder = 'ios';\n  const pbxprojPaths = globSync('ios/**/*.xcodeproj', { cwd: projectRoot, ignore: ignoredPaths })\n    .filter(\n      (project) => !/test|example|sample/i.test(project) || path.dirname(project) === iosFolder\n    )\n    // sort alphabetically to ensure this works the same across different devices (Fail in CI (linux) without this)\n    .sort()\n    .sort((a, b) => {\n      const isAInIos = path.dirname(a) === iosFolder;\n      const isBInIos = path.dirname(b) === iosFolder;\n      // preserve previous sort order\n      if ((isAInIos && isBInIos) || (!isAInIos && !isBInIos)) {\n        return 0;\n      }\n      return isAInIos ? -1 : 1;\n    });\n\n  if (!pbxprojPaths.length) {\n    throw new UnexpectedError(\n      `Failed to locate the ios/*.xcodeproj files relative to path \"${projectRoot}\".`\n    );\n  }\n  return pbxprojPaths.map((value) => path.join(projectRoot, value));\n}\n\n/**\n * Get the pbxproj for the given path\n */\nexport function getXcodeProjectPath(projectRoot: string): string {\n  const [using, ...extra] = getAllXcodeProjectPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'xcodeproj',\n      fileName: '*.xcodeproj',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllPBXProjectPaths(projectRoot: string): string[] {\n  const projectPaths = getAllXcodeProjectPaths(projectRoot);\n  const paths = projectPaths\n    .map((value) => path.join(value, 'project.pbxproj'))\n    .filter((value) => existsSync(value));\n\n  if (!paths.length) {\n    throw new UnexpectedError(\n      `Failed to locate the ios/*.xcodeproj/project.pbxproj files relative to path \"${projectRoot}\".`\n    );\n  }\n  return paths;\n}\n\nexport function getPBXProjectPath(projectRoot: string): string {\n  const [using, ...extra] = getAllPBXProjectPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'project-pbxproj',\n      fileName: 'project.pbxproj',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllInfoPlistPaths(projectRoot: string): string[] {\n  const paths = globSync('ios/*/Info.plist', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  }).sort(\n    // longer name means more suffixes, we want the shortest possible one to be first.\n    (a, b) => a.length - b.length\n  );\n\n  if (!paths.length) {\n    throw new UnexpectedError(\n      `Failed to locate Info.plist files relative to path \"${projectRoot}\".`\n    );\n  }\n  return paths;\n}\n\nexport function getInfoPlistPath(projectRoot: string): string {\n  const [using, ...extra] = getAllInfoPlistPaths(projectRoot);\n\n  if (extra.length) {\n    warnMultipleFiles({\n      tag: 'info-plist',\n      fileName: 'Info.plist',\n      projectRoot,\n      using,\n      extra,\n    });\n  }\n\n  return using;\n}\n\nexport function getAllEntitlementsPaths(projectRoot: string): string[] {\n  const paths = globSync('ios/*/*.entitlements', {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n  return paths;\n}\n\n/**\n * @deprecated: use Entitlements.getEntitlementsPath instead\n */\nexport function getEntitlementsPath(projectRoot: string): string | null {\n  return Entitlements.getEntitlementsPath(projectRoot);\n}\n\nexport function getSupportingPath(projectRoot: string): string {\n  return path.resolve(projectRoot, 'ios', path.basename(getSourceRoot(projectRoot)), 'Supporting');\n}\n\nexport function getExpoPlistPath(projectRoot: string): string {\n  const supportingPath = getSupportingPath(projectRoot);\n  return path.join(supportingPath, 'Expo.plist');\n}\n\nfunction warnMultipleFiles({\n  tag,\n  fileName,\n  projectRoot,\n  using,\n  extra,\n}: {\n  tag: string;\n  fileName: string;\n  projectRoot?: string;\n  using: string;\n  extra: string[];\n}) {\n  const usingPath = projectRoot ? path.relative(projectRoot, using) : using;\n  const extraPaths = projectRoot ? extra.map((v) => path.relative(projectRoot, v)) : extra;\n  addWarningIOS(\n    `paths-${tag}`,\n    `Found multiple ${fileName} file paths, using \"${usingPath}\". Ignored paths: ${JSON.stringify(\n      extraPaths\n    )}`\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,KAAA;EAAA,MAAAH,IAAA,GAAAI,uBAAA,CAAAH,OAAA;EAAAE,IAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,UAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,SAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,aAAA;EAAA,MAAAP,IAAA,GAAAI,uBAAA,CAAAH,OAAA;EAAAM,YAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+C,SAAAQ,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAE/C,MAAMW,YAAY,GAAG,CAAC,4CAA4C,CAAC;AAY5D,SAASC,4BAA4BA,CAACC,WAAmB,EAAU;EACxE,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,YAAQ,EAAC,qBAAqB,EAAE;IACxDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EAEF,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIM,yBAAe,EACtB,yDAAwDP,WAAY,GAAE,CACxE;EACH;EAEA,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,qBAAqB;MAC1BC,QAAQ,EAAE,aAAa;MACvBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASW,sBAAsBA,CAACZ,WAAmB,EAAU;EAClE,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,YAAQ,EAAC,iCAAiC,EAAE;IACpEC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EAEF,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIM,yBAAe,EAAE,kDAAiDP,WAAY,GAAE,CAAC;EAC7F;EAEA,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,cAAc;MACnBC,QAAQ,EAAE,aAAa;MACvBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASY,gCAAgCA,CAACb,WAAmB,EAAU;EAC5E,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG,IAAAC,YAAQ,EAAC,qBAAqB,EAAE;IACxDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EAEF,IAAI,CAACG,KAAK,EAAE;IACV,MAAM,KAAIM,yBAAe,EAAE,oDAAmDP,WAAY,GAAE,CAAC;EAC/F;EAEA,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,0BAA0B;MAC/BC,QAAQ,EAAE,eAAe;MACzBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEA,SAASa,WAAWA,CAACC,QAAgB,EAAiB;EACpD,MAAMC,SAAS,GAAG7C,IAAI,GAAC8C,OAAO,CAACF,QAAQ,CAAC;EACxC,QAAQC,SAAS;IACf,KAAK,KAAK;MACR,OAAO,QAAQ;IACjB,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,MAAM;IACf,KAAK,QAAQ;MACX,OAAO,OAAO;IAChB;MACE,MAAM,KAAIT,yBAAe,EAAE,kCAAiCS,SAAU,EAAC,CAAC;EAAC;AAE/E;AAEO,SAASE,WAAWA,CAACH,QAAgB,EAAE;EAC5C,OAAO;IACL5C,IAAI,EAAEA,IAAI,GAACgD,SAAS,CAACJ,QAAQ,CAAC;IAC9BK,QAAQ,EAAE,IAAAC,kBAAY,EAACN,QAAQ,EAAE,MAAM,CAAC;IACxCO,QAAQ,EAAER,WAAW,CAACC,QAAQ;EAChC,CAAC;AACH;AAEO,SAASQ,cAAcA,CAACvB,WAAmB,EAA0B;EAC1E,MAAMe,QAAQ,GAAGH,sBAAsB,CAACZ,WAAW,CAAC;EACpD,OAAOkB,WAAW,CAACH,QAAQ,CAAC;AAC9B;AAEO,SAASS,aAAaA,CAACxB,WAAmB,EAAU;EACzD,MAAMyB,WAAW,GAAGF,cAAc,CAACvB,WAAW,CAAC;EAC/C,OAAO7B,IAAI,GAACuD,OAAO,CAACD,WAAW,CAACtD,IAAI,CAAC;AACvC;AAEO,SAASwD,eAAeA,CAAC3B,WAAmB,EAAY;EAC7D,OAAO,IAAAG,YAAQ,EAAC,mDAAmD,EAAE;IACnEC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;AACJ;AAEO,SAAS8B,eAAeA,CAAC5B,WAAmB,EAAY;EAC7D,MAAM6B,WAAW,GAAGF,eAAe,CAAC3B,WAAW,CAAC;EAChD,OAAO6B,WAAW,CAACC,GAAG,CAAEC,UAAU,IAAK5D,IAAI,GAAC6D,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC;AACrE;AAEO,SAASC,uBAAuBA,CAAClC,WAAmB,EAAY;EACrE,MAAMmC,SAAS,GAAG,KAAK;EACvB,MAAMC,YAAY,GAAG,IAAAjC,YAAQ,EAAC,oBAAoB,EAAE;IAAEE,GAAG,EAAEL,WAAW;IAAEM,MAAM,EAAER;EAAa,CAAC,CAAC,CAC5FuC,MAAM,CACJC,OAAO,IAAK,CAAC,sBAAsB,CAACC,IAAI,CAACD,OAAO,CAAC,IAAInE,IAAI,GAACuD,OAAO,CAACY,OAAO,CAAC,KAAKH,SAAS;EAE3F;EAAA,CACCK,IAAI,EAAE,CACNA,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,MAAMC,QAAQ,GAAGxE,IAAI,GAACuD,OAAO,CAACe,CAAC,CAAC,KAAKN,SAAS;IAC9C,MAAMS,QAAQ,GAAGzE,IAAI,GAACuD,OAAO,CAACgB,CAAC,CAAC,KAAKP,SAAS;IAC9C;IACA,IAAKQ,QAAQ,IAAIC,QAAQ,IAAM,CAACD,QAAQ,IAAI,CAACC,QAAS,EAAE;MACtD,OAAO,CAAC;IACV;IACA,OAAOD,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1B,CAAC,CAAC;EAEJ,IAAI,CAACP,YAAY,CAAC5B,MAAM,EAAE;IACxB,MAAM,KAAID,yBAAe,EACtB,gEAA+DP,WAAY,IAAG,CAChF;EACH;EACA,OAAOoC,YAAY,CAACN,GAAG,CAAEe,KAAK,IAAK1E,IAAI,GAAC2E,IAAI,CAAC9C,WAAW,EAAE6C,KAAK,CAAC,CAAC;AACnE;;AAEA;AACA;AACA;AACO,SAASE,mBAAmBA,CAAC/C,WAAmB,EAAU;EAC/D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAGgC,uBAAuB,CAAClC,WAAW,CAAC;EAE9D,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,WAAW;MAChBC,QAAQ,EAAE,aAAa;MACvBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAAS+C,qBAAqBA,CAAChD,WAAmB,EAAY;EACnE,MAAMiD,YAAY,GAAGf,uBAAuB,CAAClC,WAAW,CAAC;EACzD,MAAMkD,KAAK,GAAGD,YAAY,CACvBnB,GAAG,CAAEe,KAAK,IAAK1E,IAAI,GAAC2E,IAAI,CAACD,KAAK,EAAE,iBAAiB,CAAC,CAAC,CACnDR,MAAM,CAAEQ,KAAK,IAAK,IAAAM,gBAAU,EAACN,KAAK,CAAC,CAAC;EAEvC,IAAI,CAACK,KAAK,CAAC1C,MAAM,EAAE;IACjB,MAAM,KAAID,yBAAe,EACtB,gFAA+EP,WAAY,IAAG,CAChG;EACH;EACA,OAAOkD,KAAK;AACd;AAEO,SAASE,iBAAiBA,CAACpD,WAAmB,EAAU;EAC7D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAG8C,qBAAqB,CAAChD,WAAW,CAAC;EAE5D,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,iBAAiB;MACtBC,QAAQ,EAAE,iBAAiB;MAC3BX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASoD,oBAAoBA,CAACrD,WAAmB,EAAY;EAClE,MAAMkD,KAAK,GAAG,IAAA/C,YAAQ,EAAC,kBAAkB,EAAE;IACzCC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC,CAAC0C,IAAI;EACL;EACA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjC,MAAM,GAAGkC,CAAC,CAAClC,MAAM,CAC9B;EAED,IAAI,CAAC0C,KAAK,CAAC1C,MAAM,EAAE;IACjB,MAAM,KAAID,yBAAe,EACtB,uDAAsDP,WAAY,IAAG,CACvE;EACH;EACA,OAAOkD,KAAK;AACd;AAEO,SAASI,gBAAgBA,CAACtD,WAAmB,EAAU;EAC5D,MAAM,CAACC,KAAK,EAAE,GAAGC,KAAK,CAAC,GAAGmD,oBAAoB,CAACrD,WAAW,CAAC;EAE3D,IAAIE,KAAK,CAACM,MAAM,EAAE;IAChBC,iBAAiB,CAAC;MAChBC,GAAG,EAAE,YAAY;MACjBC,QAAQ,EAAE,YAAY;MACtBX,WAAW;MACXC,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOD,KAAK;AACd;AAEO,SAASsD,uBAAuBA,CAACvD,WAAmB,EAAY;EACrE,MAAMkD,KAAK,GAAG,IAAA/C,YAAQ,EAAC,sBAAsB,EAAE;IAC7CC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEL,WAAW;IAChBM,MAAM,EAAER;EACV,CAAC,CAAC;EACF,OAAOoD,KAAK;AACd;;AAEA;AACA;AACA;AACO,SAASM,mBAAmBA,CAACxD,WAAmB,EAAiB;EACtE,OAAOzB,YAAY,GAACiF,mBAAmB,CAACxD,WAAW,CAAC;AACtD;AAEO,SAASyD,iBAAiBA,CAACzD,WAAmB,EAAU;EAC7D,OAAO7B,IAAI,GAACuF,OAAO,CAAC1D,WAAW,EAAE,KAAK,EAAE7B,IAAI,GAACwF,QAAQ,CAACnC,aAAa,CAACxB,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC;AAClG;AAEO,SAAS4D,gBAAgBA,CAAC5D,WAAmB,EAAU;EAC5D,MAAM6D,cAAc,GAAGJ,iBAAiB,CAACzD,WAAW,CAAC;EACrD,OAAO7B,IAAI,GAAC2E,IAAI,CAACe,cAAc,EAAE,YAAY,CAAC;AAChD;AAEA,SAASpD,iBAAiBA,CAAC;EACzBC,GAAG;EACHC,QAAQ;EACRX,WAAW;EACXC,KAAK;EACLC;AAOF,CAAC,EAAE;EACD,MAAM4D,SAAS,GAAG9D,WAAW,GAAG7B,IAAI,GAAC4F,QAAQ,CAAC/D,WAAW,EAAEC,KAAK,CAAC,GAAGA,KAAK;EACzE,MAAM+D,UAAU,GAAGhE,WAAW,GAAGE,KAAK,CAAC4B,GAAG,CAAEmC,CAAC,IAAK9F,IAAI,GAAC4F,QAAQ,CAAC/D,WAAW,EAAEiE,CAAC,CAAC,CAAC,GAAG/D,KAAK;EACxF,IAAAgE,yBAAa,EACV,SAAQxD,GAAI,EAAC,EACb,kBAAiBC,QAAS,uBAAsBmD,SAAU,qBAAoBK,IAAI,CAACC,SAAS,CAC3FJ,UAAU,CACV,EAAC,CACJ;AACH"}