{"version": 3, "file": "Maps.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_resolveFrom", "_iosPlugins", "_withDangerousMod", "_generateCode", "obj", "__esModule", "default", "debug", "MATCH_INIT", "exports", "withGoogleMapsKey", "createInfoPlistPlugin", "setGoogleMapsApiKey", "withMaps", "config", "<PERSON><PERSON><PERSON><PERSON>", "getGoogleMapsApiKey", "withMapsCocoaPods", "useGoogleMaps", "withGoogleMapsAppDelegate", "_config$ios$config$go", "_config$ios", "_config$ios$config", "ios", "googleMapsApiKey", "GMSApiKey", "infoPlist", "addGoogleMapsAppDelegateImport", "src", "newSrc", "push", "mergeContents", "tag", "join", "anchor", "offset", "comment", "removeGoogleMapsAppDelegateImport", "removeContents", "addGoogleMapsAppDelegateInit", "removeGoogleMapsAppDelegateInit", "addMapsCocoaPods", "removeMapsCocoaPods", "isReactNativeMapsInstalled", "projectRoot", "resolved", "resolveFrom", "silent", "path", "dirname", "isReactNativeMapsAutolinked", "withDangerousMod", "filePath", "modRequest", "platformProjectRoot", "contents", "fs", "promises", "readFile", "results", "googleMapsPath", "isLinked", "error", "code", "Error", "didMerge", "<PERSON><PERSON><PERSON><PERSON>", "writeFile", "withAppDelegate", "includes", "modResults", "language"], "sources": ["../../src/ios/Maps.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { ConfigPlugin, InfoPlist } from '../Plugin.types';\nimport { createInfoPlistPlugin, withAppDelegate } from '../plugins/ios-plugins';\nimport { withDangerousMod } from '../plugins/withDangerousMod';\nimport { mergeContents, MergeResults, removeContents } from '../utils/generateCode';\n\nconst debug = require('debug')('expo:config-plugins:ios:maps') as typeof console.log;\n\nexport const MATCH_INIT =\n  /-\\s*\\(BOOL\\)\\s*application:\\s*\\(UIApplication\\s*\\*\\s*\\)\\s*\\w+\\s+didFinishLaunchingWithOptions:/g;\n\nconst withGoogleMapsKey = createInfoPlistPlugin(setGoogleMapsApiKey, 'withGoogleMapsKey');\n\nexport const withMaps: ConfigPlugin = (config) => {\n  config = withGoogleMapsKey(config);\n\n  const apiKey = getGoogleMapsApiKey(config);\n  // Technically adds react-native-maps (Apple maps) and google maps.\n\n  debug('Google Maps API Key:', apiKey);\n  config = withMapsCocoaPods(config, { useGoogleMaps: !!apiKey });\n\n  // Adds/Removes AppDelegate setup for Google Maps API on iOS\n  config = withGoogleMapsAppDelegate(config, { apiKey });\n\n  return config;\n};\n\nexport function getGoogleMapsApiKey(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.config?.googleMapsApiKey ?? null;\n}\n\nexport function setGoogleMapsApiKey(\n  config: Pick<ExpoConfig, 'ios'>,\n  { GMSApiKey, ...infoPlist }: InfoPlist\n): InfoPlist {\n  const apiKey = getGoogleMapsApiKey(config);\n\n  if (apiKey === null) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    GMSApiKey: apiKey,\n  };\n}\n\nexport function addGoogleMapsAppDelegateImport(src: string): MergeResults {\n  const newSrc = [];\n  newSrc.push(\n    '#if __has_include(<GoogleMaps/GoogleMaps.h>)',\n    '#import <GoogleMaps/GoogleMaps.h>',\n    '#endif'\n  );\n\n  return mergeContents({\n    tag: 'react-native-maps-import',\n    src,\n    newSrc: newSrc.join('\\n'),\n    anchor: /#import \"AppDelegate\\.h\"/,\n    offset: 1,\n    comment: '//',\n  });\n}\n\nexport function removeGoogleMapsAppDelegateImport(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps-import',\n    src,\n  });\n}\n\nexport function addGoogleMapsAppDelegateInit(src: string, apiKey: string): MergeResults {\n  const newSrc = [];\n  newSrc.push(\n    '#if __has_include(<GoogleMaps/GoogleMaps.h>)',\n    `  [GMSServices provideAPIKey:@\"${apiKey}\"];`,\n    '#endif'\n  );\n\n  return mergeContents({\n    tag: 'react-native-maps-init',\n    src,\n    newSrc: newSrc.join('\\n'),\n    anchor: MATCH_INIT,\n    offset: 2,\n    comment: '//',\n  });\n}\n\nexport function removeGoogleMapsAppDelegateInit(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps-init',\n    src,\n  });\n}\n\n/**\n * @param src The contents of the Podfile.\n * @returns Podfile with Google Maps added.\n */\nexport function addMapsCocoaPods(src: string): MergeResults {\n  return mergeContents({\n    tag: 'react-native-maps',\n    src,\n    newSrc: `  pod 'react-native-google-maps', path: File.dirname(\\`node --print \"require.resolve('react-native-maps/package.json')\"\\`)`,\n    anchor: /use_native_modules/,\n    offset: 0,\n    comment: '#',\n  });\n}\n\nexport function removeMapsCocoaPods(src: string): MergeResults {\n  return removeContents({\n    tag: 'react-native-maps',\n    src,\n  });\n}\n\nfunction isReactNativeMapsInstalled(projectRoot: string): string | null {\n  const resolved = resolveFrom.silent(projectRoot, 'react-native-maps/package.json');\n  return resolved ? path.dirname(resolved) : null;\n}\n\nfunction isReactNativeMapsAutolinked(config: Pick<ExpoConfig, '_internal'>): boolean {\n  // Only add the native code changes if we know that the package is going to be linked natively.\n  // This is specifically for monorepo support where one app might have react-native-maps (adding it to the node_modules)\n  // but another app will not have it installed in the package.json, causing it to not be linked natively.\n  // This workaround only exists because react-native-maps doesn't have a config plugin vendored in the package.\n\n  // TODO: `react-native-maps` doesn't use Expo autolinking so we cannot safely disable the module.\n  return true;\n\n  // return (\n  //   !config._internal?.autolinkedModules ||\n  //   config._internal.autolinkedModules.includes('react-native-maps')\n  // );\n}\n\nconst withMapsCocoaPods: ConfigPlugin<{ useGoogleMaps: boolean }> = (config, { useGoogleMaps }) => {\n  return withDangerousMod(config, [\n    'ios',\n    async (config) => {\n      const filePath = path.join(config.modRequest.platformProjectRoot, 'Podfile');\n      const contents = await fs.promises.readFile(filePath, 'utf-8');\n      let results: MergeResults;\n      // Only add the block if react-native-maps is installed in the project (best effort).\n      // Generally prebuild runs after a yarn install so this should always work as expected.\n      const googleMapsPath = isReactNativeMapsInstalled(config.modRequest.projectRoot);\n      const isLinked = isReactNativeMapsAutolinked(config);\n      debug('Is Expo Autolinked:', isLinked);\n      debug('react-native-maps path:', googleMapsPath);\n      if (isLinked && googleMapsPath && useGoogleMaps) {\n        try {\n          results = addMapsCocoaPods(contents);\n        } catch (error: any) {\n          if (error.code === 'ERR_NO_MATCH') {\n            throw new Error(\n              `Cannot add react-native-maps to the project's ios/Podfile because it's malformed. Please report this with a copy of your project Podfile.`\n            );\n          }\n          throw error;\n        }\n      } else {\n        // If the package is no longer installed, then remove the block.\n        results = removeMapsCocoaPods(contents);\n      }\n      if (results.didMerge || results.didClear) {\n        await fs.promises.writeFile(filePath, results.contents);\n      }\n      return config;\n    },\n  ]);\n};\n\nconst withGoogleMapsAppDelegate: ConfigPlugin<{ apiKey: string | null }> = (config, { apiKey }) => {\n  return withAppDelegate(config, (config) => {\n    if (['objc', 'objcpp'].includes(config.modResults.language)) {\n      if (\n        apiKey &&\n        isReactNativeMapsAutolinked(config) &&\n        isReactNativeMapsInstalled(config.modRequest.projectRoot)\n      ) {\n        try {\n          config.modResults.contents = addGoogleMapsAppDelegateImport(\n            config.modResults.contents\n          ).contents;\n          config.modResults.contents = addGoogleMapsAppDelegateInit(\n            config.modResults.contents,\n            apiKey\n          ).contents;\n        } catch (error: any) {\n          if (error.code === 'ERR_NO_MATCH') {\n            throw new Error(\n              `Cannot add Google Maps to the project's AppDelegate because it's malformed. Please report this with a copy of your project AppDelegate.`\n            );\n          }\n          throw error;\n        }\n      } else {\n        config.modResults.contents = removeGoogleMapsAppDelegateImport(\n          config.modResults.contents\n        ).contents;\n        config.modResults.contents = removeGoogleMapsAppDelegateInit(\n          config.modResults.contents\n        ).contents;\n      }\n    } else {\n      throw new Error(\n        `Cannot setup Google Maps because the project AppDelegate is not a supported language: ${config.modResults.language}`\n      );\n    }\n    return config;\n  });\n};\n"], "mappings": ";;;;;;;;;;;;;;;AACA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,aAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,YAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAK,YAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,WAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,kBAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,iBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,cAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,aAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoF,SAAAC,uBAAAO,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEpF,MAAMG,KAAK,GAAGT,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAuB;AAE7E,MAAMU,UAAU,GACrB,iGAAiG;AAACC,OAAA,CAAAD,UAAA,GAAAA,UAAA;AAEpG,MAAME,iBAAiB,GAAG,IAAAC,mCAAqB,EAACC,mBAAmB,EAAE,mBAAmB,CAAC;AAElF,MAAMC,QAAsB,GAAIC,MAAM,IAAK;EAChDA,MAAM,GAAGJ,iBAAiB,CAACI,MAAM,CAAC;EAElC,MAAMC,MAAM,GAAGC,mBAAmB,CAACF,MAAM,CAAC;EAC1C;;EAEAP,KAAK,CAAC,sBAAsB,EAAEQ,MAAM,CAAC;EACrCD,MAAM,GAAGG,iBAAiB,CAACH,MAAM,EAAE;IAAEI,aAAa,EAAE,CAAC,CAACH;EAAO,CAAC,CAAC;;EAE/D;EACAD,MAAM,GAAGK,yBAAyB,CAACL,MAAM,EAAE;IAAEC;EAAO,CAAC,CAAC;EAEtD,OAAOD,MAAM;AACf,CAAC;AAACL,OAAA,CAAAI,QAAA,GAAAA,QAAA;AAEK,SAASG,mBAAmBA,CAACF,MAA+B,EAAE;EAAA,IAAAM,qBAAA,EAAAC,WAAA,EAAAC,kBAAA;EACnE,QAAAF,qBAAA,IAAAC,WAAA,GAAOP,MAAM,CAACS,GAAG,cAAAF,WAAA,wBAAAC,kBAAA,GAAVD,WAAA,CAAYP,MAAM,cAAAQ,kBAAA,uBAAlBA,kBAAA,CAAoBE,gBAAgB,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AACrD;AAEO,SAASR,mBAAmBA,CACjCE,MAA+B,EAC/B;EAAEW,SAAS;EAAE,GAAGC;AAAqB,CAAC,EAC3B;EACX,MAAMX,MAAM,GAAGC,mBAAmB,CAACF,MAAM,CAAC;EAE1C,IAAIC,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOW,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZD,SAAS,EAAEV;EACb,CAAC;AACH;AAEO,SAASY,8BAA8BA,CAACC,GAAW,EAAgB;EACxE,MAAMC,MAAM,GAAG,EAAE;EACjBA,MAAM,CAACC,IAAI,CACT,8CAA8C,EAC9C,mCAAmC,EACnC,QAAQ,CACT;EAED,OAAO,IAAAC,6BAAa,EAAC;IACnBC,GAAG,EAAE,0BAA0B;IAC/BJ,GAAG;IACHC,MAAM,EAAEA,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC;IACzBC,MAAM,EAAE,0BAA0B;IAClCC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASC,iCAAiCA,CAACT,GAAW,EAAgB;EAC3E,OAAO,IAAAU,8BAAc,EAAC;IACpBN,GAAG,EAAE,0BAA0B;IAC/BJ;EACF,CAAC,CAAC;AACJ;AAEO,SAASW,4BAA4BA,CAACX,GAAW,EAAEb,MAAc,EAAgB;EACtF,MAAMc,MAAM,GAAG,EAAE;EACjBA,MAAM,CAACC,IAAI,CACT,8CAA8C,EAC7C,kCAAiCf,MAAO,KAAI,EAC7C,QAAQ,CACT;EAED,OAAO,IAAAgB,6BAAa,EAAC;IACnBC,GAAG,EAAE,wBAAwB;IAC7BJ,GAAG;IACHC,MAAM,EAAEA,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC;IACzBC,MAAM,EAAE1B,UAAU;IAClB2B,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASI,+BAA+BA,CAACZ,GAAW,EAAgB;EACzE,OAAO,IAAAU,8BAAc,EAAC;IACpBN,GAAG,EAAE,wBAAwB;IAC7BJ;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACO,SAASa,gBAAgBA,CAACb,GAAW,EAAgB;EAC1D,OAAO,IAAAG,6BAAa,EAAC;IACnBC,GAAG,EAAE,mBAAmB;IACxBJ,GAAG;IACHC,MAAM,EAAG,4HAA2H;IACpIK,MAAM,EAAE,oBAAoB;IAC5BC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AAEO,SAASM,mBAAmBA,CAACd,GAAW,EAAgB;EAC7D,OAAO,IAAAU,8BAAc,EAAC;IACpBN,GAAG,EAAE,mBAAmB;IACxBJ;EACF,CAAC,CAAC;AACJ;AAEA,SAASe,0BAA0BA,CAACC,WAAmB,EAAiB;EACtE,MAAMC,QAAQ,GAAGC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,gCAAgC,CAAC;EAClF,OAAOC,QAAQ,GAAGG,eAAI,CAACC,OAAO,CAACJ,QAAQ,CAAC,GAAG,IAAI;AACjD;AAEA,SAASK,2BAA2BA,CAACpC,MAAqC,EAAW;EACnF;EACA;EACA;EACA;;EAEA;EACA,OAAO,IAAI;;EAEX;EACA;EACA;EACA;AACF;;AAEA,MAAMG,iBAA2D,GAAGA,CAACH,MAAM,EAAE;EAAEI;AAAc,CAAC,KAAK;EACjG,OAAO,IAAAiC,oCAAgB,EAACrC,MAAM,EAAE,CAC9B,KAAK,EACL,MAAOA,MAAM,IAAK;IAChB,MAAMsC,QAAQ,GAAGJ,eAAI,CAACf,IAAI,CAACnB,MAAM,CAACuC,UAAU,CAACC,mBAAmB,EAAE,SAAS,CAAC;IAC5E,MAAMC,QAAQ,GAAG,MAAMC,aAAE,CAACC,QAAQ,CAACC,QAAQ,CAACN,QAAQ,EAAE,OAAO,CAAC;IAC9D,IAAIO,OAAqB;IACzB;IACA;IACA,MAAMC,cAAc,GAAGjB,0BAA0B,CAAC7B,MAAM,CAACuC,UAAU,CAACT,WAAW,CAAC;IAChF,MAAMiB,QAAQ,GAAGX,2BAA2B,CAACpC,MAAM,CAAC;IACpDP,KAAK,CAAC,qBAAqB,EAAEsD,QAAQ,CAAC;IACtCtD,KAAK,CAAC,yBAAyB,EAAEqD,cAAc,CAAC;IAChD,IAAIC,QAAQ,IAAID,cAAc,IAAI1C,aAAa,EAAE;MAC/C,IAAI;QACFyC,OAAO,GAAGlB,gBAAgB,CAACc,QAAQ,CAAC;MACtC,CAAC,CAAC,OAAOO,KAAU,EAAE;QACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;UACjC,MAAM,IAAIC,KAAK,CACZ,2IAA0I,CAC5I;QACH;QACA,MAAMF,KAAK;MACb;IACF,CAAC,MAAM;MACL;MACAH,OAAO,GAAGjB,mBAAmB,CAACa,QAAQ,CAAC;IACzC;IACA,IAAII,OAAO,CAACM,QAAQ,IAAIN,OAAO,CAACO,QAAQ,EAAE;MACxC,MAAMV,aAAE,CAACC,QAAQ,CAACU,SAAS,CAACf,QAAQ,EAAEO,OAAO,CAACJ,QAAQ,CAAC;IACzD;IACA,OAAOzC,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAED,MAAMK,yBAAkE,GAAGA,CAACL,MAAM,EAAE;EAAEC;AAAO,CAAC,KAAK;EACjG,OAAO,IAAAqD,6BAAe,EAACtD,MAAM,EAAGA,MAAM,IAAK;IACzC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAACuD,QAAQ,CAACvD,MAAM,CAACwD,UAAU,CAACC,QAAQ,CAAC,EAAE;MAC3D,IACExD,MAAM,IACNmC,2BAA2B,CAACpC,MAAM,CAAC,IACnC6B,0BAA0B,CAAC7B,MAAM,CAACuC,UAAU,CAACT,WAAW,CAAC,EACzD;QACA,IAAI;UACF9B,MAAM,CAACwD,UAAU,CAACf,QAAQ,GAAG5B,8BAA8B,CACzDb,MAAM,CAACwD,UAAU,CAACf,QAAQ,CAC3B,CAACA,QAAQ;UACVzC,MAAM,CAACwD,UAAU,CAACf,QAAQ,GAAGhB,4BAA4B,CACvDzB,MAAM,CAACwD,UAAU,CAACf,QAAQ,EAC1BxC,MAAM,CACP,CAACwC,QAAQ;QACZ,CAAC,CAAC,OAAOO,KAAU,EAAE;UACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;YACjC,MAAM,IAAIC,KAAK,CACZ,yIAAwI,CAC1I;UACH;UACA,MAAMF,KAAK;QACb;MACF,CAAC,MAAM;QACLhD,MAAM,CAACwD,UAAU,CAACf,QAAQ,GAAGlB,iCAAiC,CAC5DvB,MAAM,CAACwD,UAAU,CAACf,QAAQ,CAC3B,CAACA,QAAQ;QACVzC,MAAM,CAACwD,UAAU,CAACf,QAAQ,GAAGf,+BAA+B,CAC1D1B,MAAM,CAACwD,UAAU,CAACf,QAAQ,CAC3B,CAACA,QAAQ;MACZ;IACF,CAAC,MAAM;MACL,MAAM,IAAIS,KAAK,CACZ,yFAAwFlD,MAAM,CAACwD,UAAU,CAACC,QAAS,EAAC,CACtH;IACH;IACA,OAAOzD,MAAM;EACf,CAAC,CAAC;AACJ,CAAC"}