{"version": 3, "file": "Google.js", "names": ["_plist", "data", "_interopRequireDefault", "require", "_assert", "_fs", "_path", "_iosPlugins", "_Paths", "_Scheme", "_Xcodeproj", "obj", "__esModule", "default", "<PERSON><PERSON><PERSON><PERSON>", "config", "withInfoPlist", "modResults", "setGoogleConfig", "modRequest", "exports", "withGoogleServicesFile", "withXcodeProject", "setGoogleServicesFile", "projectRoot", "project", "readGoogleServicesInfoPlist", "relativePath", "googleServiceFilePath", "path", "resolve", "contents", "fs", "readFileSync", "assert", "plist", "parse", "getGoogleSignInReversedClientId", "_infoPlist$REVERSED_C", "googleServicesFileRelativePath", "getGoogleServicesFile", "infoPlist", "REVERSED_CLIENT_ID", "_config$ios$googleSer", "_config$ios", "ios", "googleServicesFile", "setGoogleSignInReversedClientId", "reversedClientId", "appendScheme", "copyFileSync", "join", "getSourceRoot", "projectName", "getProjectName", "plist<PERSON><PERSON><PERSON><PERSON>", "hasFile", "addResourceFileToGroup", "filepath", "groupName", "isBuildFile", "verbose"], "sources": ["../../src/ios/Google.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport plist from '@expo/plist';\nimport assert from 'assert';\nimport fs from 'fs';\nimport path from 'path';\nimport { XcodeProject } from 'xcode';\n\nimport { ConfigPlugin, ModProps } from '../Plugin.types';\nimport { withInfoPlist, withXcodeProject } from '../plugins/ios-plugins';\nimport { InfoPlist } from './IosConfig.types';\nimport { getSourceRoot } from './Paths';\nimport { appendScheme } from './Scheme';\nimport { addResourceFileToGroup, getProjectName } from './utils/Xcodeproj';\n\nexport const withGoogle: ConfigPlugin = (config) => {\n  return withInfoPlist(config, (config) => {\n    config.modResults = setGoogleConfig(config, config.modResults, config.modRequest);\n    return config;\n  });\n};\n\nexport const withGoogleServicesFile: ConfigPlugin = (config) => {\n  return withXcodeProject(config, (config) => {\n    config.modResults = setGoogleServicesFile(config, {\n      projectRoot: config.modRequest.projectRoot,\n      project: config.modResults,\n    });\n    return config;\n  });\n};\n\nfunction readGoogleServicesInfoPlist(\n  relativePath: string,\n  { projectRoot }: { projectRoot: string }\n) {\n  const googleServiceFilePath = path.resolve(projectRoot, relativePath);\n  const contents = fs.readFileSync(googleServiceFilePath, 'utf8');\n  assert(contents, 'GoogleService-Info.plist is empty');\n  return plist.parse(contents);\n}\n\nexport function getGoogleSignInReversedClientId(\n  config: Pick<ExpoConfig, 'ios'>,\n  modRequest: Pick<ModProps<InfoPlist>, 'projectRoot'>\n): string | null {\n  const googleServicesFileRelativePath = getGoogleServicesFile(config);\n  if (googleServicesFileRelativePath === null) {\n    return null;\n  }\n\n  const infoPlist = readGoogleServicesInfoPlist(googleServicesFileRelativePath, modRequest);\n\n  return infoPlist.REVERSED_CLIENT_ID ?? null;\n}\n\nexport function getGoogleServicesFile(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.googleServicesFile ?? null;\n}\n\nexport function setGoogleSignInReversedClientId(\n  config: Pick<ExpoConfig, 'ios'>,\n  infoPlist: InfoPlist,\n  modRequest: Pick<ModProps<InfoPlist>, 'projectRoot'>\n): InfoPlist {\n  const reversedClientId = getGoogleSignInReversedClientId(config, modRequest);\n\n  if (reversedClientId === null) {\n    return infoPlist;\n  }\n\n  return appendScheme(reversedClientId, infoPlist);\n}\n\nexport function setGoogleConfig(\n  config: Pick<ExpoConfig, 'ios'>,\n  infoPlist: InfoPlist,\n  modRequest: ModProps<InfoPlist>\n): InfoPlist {\n  infoPlist = setGoogleSignInReversedClientId(config, infoPlist, modRequest);\n  return infoPlist;\n}\n\nexport function setGoogleServicesFile(\n  config: Pick<ExpoConfig, 'ios'>,\n  { projectRoot, project }: { project: XcodeProject; projectRoot: string }\n): XcodeProject {\n  const googleServicesFileRelativePath = getGoogleServicesFile(config);\n  if (googleServicesFileRelativePath === null) {\n    return project;\n  }\n\n  const googleServiceFilePath = path.resolve(projectRoot, googleServicesFileRelativePath);\n  fs.copyFileSync(\n    googleServiceFilePath,\n    path.join(getSourceRoot(projectRoot), 'GoogleService-Info.plist')\n  );\n\n  const projectName = getProjectName(projectRoot);\n  const plistFilePath = `${projectName}/GoogleService-Info.plist`;\n  if (!project.hasFile(plistFilePath)) {\n    project = addResourceFileToGroup({\n      filepath: plistFilePath,\n      groupName: projectName,\n      project,\n      isBuildFile: true,\n      verbose: true,\n    });\n  }\n  return project;\n}\n"], "mappings": ";;;;;;;;;;;AACA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,SAAAM,YAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,WAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,OAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,MAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,QAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,WAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,UAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2E,SAAAC,uBAAAS,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEpE,MAAMG,UAAwB,GAAIC,MAAM,IAAK;EAClD,OAAO,IAAAC,2BAAa,EAACD,MAAM,EAAGA,MAAM,IAAK;IACvCA,MAAM,CAACE,UAAU,GAAGC,eAAe,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,EAAEF,MAAM,CAACI,UAAU,CAAC;IACjF,OAAOJ,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,UAAA,GAAAA,UAAA;AAEK,MAAMO,sBAAoC,GAAIN,MAAM,IAAK;EAC9D,OAAO,IAAAO,8BAAgB,EAACP,MAAM,EAAGA,MAAM,IAAK;IAC1CA,MAAM,CAACE,UAAU,GAAGM,qBAAqB,CAACR,MAAM,EAAE;MAChDS,WAAW,EAAET,MAAM,CAACI,UAAU,CAACK,WAAW;MAC1CC,OAAO,EAAEV,MAAM,CAACE;IAClB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAC,sBAAA,GAAAA,sBAAA;AAEF,SAASK,2BAA2BA,CAClCC,YAAoB,EACpB;EAAEH;AAAqC,CAAC,EACxC;EACA,MAAMI,qBAAqB,GAAGC,eAAI,CAACC,OAAO,CAACN,WAAW,EAAEG,YAAY,CAAC;EACrE,MAAMI,QAAQ,GAAGC,aAAE,CAACC,YAAY,CAACL,qBAAqB,EAAE,MAAM,CAAC;EAC/D,IAAAM,iBAAM,EAACH,QAAQ,EAAE,mCAAmC,CAAC;EACrD,OAAOI,gBAAK,CAACC,KAAK,CAACL,QAAQ,CAAC;AAC9B;AAEO,SAASM,+BAA+BA,CAC7CtB,MAA+B,EAC/BI,UAAoD,EACrC;EAAA,IAAAmB,qBAAA;EACf,MAAMC,8BAA8B,GAAGC,qBAAqB,CAACzB,MAAM,CAAC;EACpE,IAAIwB,8BAA8B,KAAK,IAAI,EAAE;IAC3C,OAAO,IAAI;EACb;EAEA,MAAME,SAAS,GAAGf,2BAA2B,CAACa,8BAA8B,EAAEpB,UAAU,CAAC;EAEzF,QAAAmB,qBAAA,GAAOG,SAAS,CAACC,kBAAkB,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AAC7C;AAEO,SAASE,qBAAqBA,CAACzB,MAA+B,EAAE;EAAA,IAAA4B,qBAAA,EAAAC,WAAA;EACrE,QAAAD,qBAAA,IAAAC,WAAA,GAAO7B,MAAM,CAAC8B,GAAG,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,kBAAkB,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,IAAI;AAC/C;AAEO,SAASI,+BAA+BA,CAC7ChC,MAA+B,EAC/B0B,SAAoB,EACpBtB,UAAoD,EACzC;EACX,MAAM6B,gBAAgB,GAAGX,+BAA+B,CAACtB,MAAM,EAAEI,UAAU,CAAC;EAE5E,IAAI6B,gBAAgB,KAAK,IAAI,EAAE;IAC7B,OAAOP,SAAS;EAClB;EAEA,OAAO,IAAAQ,sBAAY,EAACD,gBAAgB,EAAEP,SAAS,CAAC;AAClD;AAEO,SAASvB,eAAeA,CAC7BH,MAA+B,EAC/B0B,SAAoB,EACpBtB,UAA+B,EACpB;EACXsB,SAAS,GAAGM,+BAA+B,CAAChC,MAAM,EAAE0B,SAAS,EAAEtB,UAAU,CAAC;EAC1E,OAAOsB,SAAS;AAClB;AAEO,SAASlB,qBAAqBA,CACnCR,MAA+B,EAC/B;EAAES,WAAW;EAAEC;AAAwD,CAAC,EAC1D;EACd,MAAMc,8BAA8B,GAAGC,qBAAqB,CAACzB,MAAM,CAAC;EACpE,IAAIwB,8BAA8B,KAAK,IAAI,EAAE;IAC3C,OAAOd,OAAO;EAChB;EAEA,MAAMG,qBAAqB,GAAGC,eAAI,CAACC,OAAO,CAACN,WAAW,EAAEe,8BAA8B,CAAC;EACvFP,aAAE,CAACkB,YAAY,CACbtB,qBAAqB,EACrBC,eAAI,CAACsB,IAAI,CAAC,IAAAC,sBAAa,EAAC5B,WAAW,CAAC,EAAE,0BAA0B,CAAC,CAClE;EAED,MAAM6B,WAAW,GAAG,IAAAC,2BAAc,EAAC9B,WAAW,CAAC;EAC/C,MAAM+B,aAAa,GAAI,GAAEF,WAAY,2BAA0B;EAC/D,IAAI,CAAC5B,OAAO,CAAC+B,OAAO,CAACD,aAAa,CAAC,EAAE;IACnC9B,OAAO,GAAG,IAAAgC,mCAAsB,EAAC;MAC/BC,QAAQ,EAAEH,aAAa;MACvBI,SAAS,EAAEN,WAAW;MACtB5B,OAAO;MACPmC,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EACA,OAAOpC,OAAO;AAChB"}