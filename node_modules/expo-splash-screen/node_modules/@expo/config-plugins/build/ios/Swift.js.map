{"version": 3, "file": "Swift.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_iosPlugins", "_Paths", "_XcodeProjectFile", "_Xcodeproj", "obj", "__esModule", "default", "templateBridgingHeader", "withSwiftBridgingHeader", "config", "withXcodeProject", "modResults", "ensureSwiftBridgingHeaderSetup", "project", "projectRoot", "modRequest", "exports", "shouldCreateSwiftBridgingHeader", "projectName", "getProjectName", "bridgingHeader", "createBridgingHeaderFileName", "createBridgingHeaderFile", "linkBridgingHeaderFile", "path", "join", "isObjc", "getAppDelegate", "language", "getDesignatedSwiftBridgingHeaderFileReference", "configurations", "pbxXCBuildConfigurationSection", "buildSettings", "Object", "values", "PRODUCT_NAME", "SWIFT_OBJC_BRIDGING_HEADER", "bridgingHeaderProjectPath", "getSourceRoot", "fs", "existsSync", "writeFileSync", "filePath", "hasFile", "addResourceFileToGroup", "filepath", "groupName", "isBuildFile", "verbose", "withNoopSwiftFile", "withBuildSourceFile", "contents"], "sources": ["../../src/ios/Swift.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { ConfigPlugin, XcodeProject } from '../Plugin.types';\nimport { withXcodeProject } from '../plugins/ios-plugins';\nimport { getAppDelegate, getSourceRoot } from './Paths';\nimport { withBuildSourceFile } from './XcodeProjectFile';\nimport { addResourceFileToGroup, getProjectName } from './utils/Xcodeproj';\n\nconst templateBridgingHeader = `//\n//  Use this file to import your target's public headers that you would like to expose to Swift.\n//\n`;\n\n/**\n * Ensure a Swift bridging header is created for the project.\n * This helps fix problems related to using modules that are written in Swift (lottie, FBSDK).\n *\n * 1. Ensures the file exists given the project path.\n * 2. Writes the file and links to Xcode as a resource file.\n * 3. Sets the build configuration `SWIFT_OBJC_BRIDGING_HEADER = [PROJECT_NAME]-Bridging-Header.h`\n */\nexport const withSwiftBridgingHeader: ConfigPlugin = (config) => {\n  return withXcodeProject(config, (config) => {\n    config.modResults = ensureSwiftBridgingHeaderSetup({\n      project: config.modResults,\n      projectRoot: config.modRequest.projectRoot,\n    });\n    return config;\n  });\n};\n\nexport function ensureSwiftBridgingHeaderSetup({\n  projectRoot,\n  project,\n}: {\n  projectRoot: string;\n  project: XcodeProject;\n}) {\n  // Only create a bridging header if using objective-c\n  if (shouldCreateSwiftBridgingHeader({ projectRoot, project })) {\n    const projectName = getProjectName(projectRoot);\n    const bridgingHeader = createBridgingHeaderFileName(projectName);\n    // Ensure a bridging header is created in the Xcode project.\n    project = createBridgingHeaderFile({\n      project,\n      projectName,\n      projectRoot,\n      bridgingHeader,\n    });\n    // Designate the newly created file as the Swift bridging header in the Xcode project.\n    project = linkBridgingHeaderFile({\n      project,\n      bridgingHeader: path.join(projectName, bridgingHeader),\n    });\n  }\n  return project;\n}\n\nfunction shouldCreateSwiftBridgingHeader({\n  projectRoot,\n  project,\n}: {\n  projectRoot: string;\n  project: XcodeProject;\n}): boolean {\n  // Only create a bridging header if the project is using in Objective C (AppDelegate is written in Objc).\n  const isObjc = getAppDelegate(projectRoot).language !== 'swift';\n  return isObjc && !getDesignatedSwiftBridgingHeaderFileReference({ project });\n}\n\n/**\n * @returns String matching the default name used when Xcode automatically creates a bridging header file.\n */\nfunction createBridgingHeaderFileName(projectName: string): string {\n  return `${projectName}-Bridging-Header.h`;\n}\n\nexport function getDesignatedSwiftBridgingHeaderFileReference({\n  project,\n}: {\n  project: XcodeProject;\n}): string | null {\n  const configurations = project.pbxXCBuildConfigurationSection();\n  // @ts-ignore\n  for (const { buildSettings } of Object.values(configurations || {})) {\n    // Guessing that this is the best way to emulate Xcode.\n    // Using `project.addToBuildSettings` modifies too many targets.\n    if (typeof buildSettings?.PRODUCT_NAME !== 'undefined') {\n      if (\n        typeof buildSettings.SWIFT_OBJC_BRIDGING_HEADER === 'string' &&\n        buildSettings.SWIFT_OBJC_BRIDGING_HEADER\n      ) {\n        return buildSettings.SWIFT_OBJC_BRIDGING_HEADER;\n      }\n    }\n  }\n  return null;\n}\n\n/**\n *\n * @param bridgingHeader The bridging header filename ex: `ExpoAPIs-Bridging-Header.h`\n * @returns\n */\nexport function linkBridgingHeaderFile({\n  project,\n  bridgingHeader,\n}: {\n  project: XcodeProject;\n  bridgingHeader: string;\n}): XcodeProject {\n  const configurations = project.pbxXCBuildConfigurationSection();\n  // @ts-ignore\n  for (const { buildSettings } of Object.values(configurations || {})) {\n    // Guessing that this is the best way to emulate Xcode.\n    // Using `project.addToBuildSettings` modifies too many targets.\n    if (typeof buildSettings?.PRODUCT_NAME !== 'undefined') {\n      buildSettings.SWIFT_OBJC_BRIDGING_HEADER = bridgingHeader;\n    }\n  }\n\n  return project;\n}\n\nexport function createBridgingHeaderFile({\n  projectRoot,\n  projectName,\n  project,\n  bridgingHeader,\n}: {\n  project: XcodeProject;\n  projectName: string;\n  projectRoot: string;\n  bridgingHeader: string;\n}): XcodeProject {\n  const bridgingHeaderProjectPath = path.join(getSourceRoot(projectRoot), bridgingHeader);\n  if (!fs.existsSync(bridgingHeaderProjectPath)) {\n    // Create the file\n    fs.writeFileSync(bridgingHeaderProjectPath, templateBridgingHeader, 'utf8');\n  }\n\n  // This is non-standard, Xcode generates the bridging header in `/ios` which is kinda annoying.\n  // Instead, this'll generate the default header in the application code folder `/ios/myproject/`.\n  const filePath = `${projectName}/${bridgingHeader}`;\n  // Ensure the file is linked with Xcode resource files\n  if (!project.hasFile(filePath)) {\n    project = addResourceFileToGroup({\n      filepath: filePath,\n      groupName: projectName,\n      project,\n      // Not sure why, but this is how Xcode generates it.\n      isBuildFile: false,\n      verbose: false,\n    });\n  }\n  return project;\n}\n\nexport const withNoopSwiftFile: ConfigPlugin = (config) => {\n  return withBuildSourceFile(config, {\n    filePath: 'noop-file.swift',\n    contents: [\n      '//',\n      '// @generated',\n      '// A blank Swift file must be created for native modules with Swift files to work correctly.',\n      '//',\n      '',\n    ].join('\\n'),\n  });\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,YAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,WAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,OAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,MAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,kBAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,iBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,WAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,UAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2E,SAAAC,uBAAAO,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE3E,MAAMG,sBAAsB,GAAI;AAChC;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,uBAAqC,GAAIC,MAAM,IAAK;EAC/D,OAAO,IAAAC,8BAAgB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC1CA,MAAM,CAACE,UAAU,GAAGC,8BAA8B,CAAC;MACjDC,OAAO,EAAEJ,MAAM,CAACE,UAAU;MAC1BG,WAAW,EAAEL,MAAM,CAACM,UAAU,CAACD;IACjC,CAAC,CAAC;IACF,OAAOL,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACO,OAAA,CAAAR,uBAAA,GAAAA,uBAAA;AAEK,SAASI,8BAA8BA,CAAC;EAC7CE,WAAW;EACXD;AAIF,CAAC,EAAE;EACD;EACA,IAAII,+BAA+B,CAAC;IAAEH,WAAW;IAAED;EAAQ,CAAC,CAAC,EAAE;IAC7D,MAAMK,WAAW,GAAG,IAAAC,2BAAc,EAACL,WAAW,CAAC;IAC/C,MAAMM,cAAc,GAAGC,4BAA4B,CAACH,WAAW,CAAC;IAChE;IACAL,OAAO,GAAGS,wBAAwB,CAAC;MACjCT,OAAO;MACPK,WAAW;MACXJ,WAAW;MACXM;IACF,CAAC,CAAC;IACF;IACAP,OAAO,GAAGU,sBAAsB,CAAC;MAC/BV,OAAO;MACPO,cAAc,EAAEI,eAAI,CAACC,IAAI,CAACP,WAAW,EAAEE,cAAc;IACvD,CAAC,CAAC;EACJ;EACA,OAAOP,OAAO;AAChB;AAEA,SAASI,+BAA+BA,CAAC;EACvCH,WAAW;EACXD;AAIF,CAAC,EAAW;EACV;EACA,MAAMa,MAAM,GAAG,IAAAC,uBAAc,EAACb,WAAW,CAAC,CAACc,QAAQ,KAAK,OAAO;EAC/D,OAAOF,MAAM,IAAI,CAACG,6CAA6C,CAAC;IAAEhB;EAAQ,CAAC,CAAC;AAC9E;;AAEA;AACA;AACA;AACA,SAASQ,4BAA4BA,CAACH,WAAmB,EAAU;EACjE,OAAQ,GAAEA,WAAY,oBAAmB;AAC3C;AAEO,SAASW,6CAA6CA,CAAC;EAC5DhB;AAGF,CAAC,EAAiB;EAChB,MAAMiB,cAAc,GAAGjB,OAAO,CAACkB,8BAA8B,EAAE;EAC/D;EACA,KAAK,MAAM;IAAEC;EAAc,CAAC,IAAIC,MAAM,CAACC,MAAM,CAACJ,cAAc,IAAI,CAAC,CAAC,CAAC,EAAE;IACnE;IACA;IACA,IAAI,QAAOE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEG,YAAY,MAAK,WAAW,EAAE;MACtD,IACE,OAAOH,aAAa,CAACI,0BAA0B,KAAK,QAAQ,IAC5DJ,aAAa,CAACI,0BAA0B,EACxC;QACA,OAAOJ,aAAa,CAACI,0BAA0B;MACjD;IACF;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASb,sBAAsBA,CAAC;EACrCV,OAAO;EACPO;AAIF,CAAC,EAAgB;EACf,MAAMU,cAAc,GAAGjB,OAAO,CAACkB,8BAA8B,EAAE;EAC/D;EACA,KAAK,MAAM;IAAEC;EAAc,CAAC,IAAIC,MAAM,CAACC,MAAM,CAACJ,cAAc,IAAI,CAAC,CAAC,CAAC,EAAE;IACnE;IACA;IACA,IAAI,QAAOE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEG,YAAY,MAAK,WAAW,EAAE;MACtDH,aAAa,CAACI,0BAA0B,GAAGhB,cAAc;IAC3D;EACF;EAEA,OAAOP,OAAO;AAChB;AAEO,SAASS,wBAAwBA,CAAC;EACvCR,WAAW;EACXI,WAAW;EACXL,OAAO;EACPO;AAMF,CAAC,EAAgB;EACf,MAAMiB,yBAAyB,GAAGb,eAAI,CAACC,IAAI,CAAC,IAAAa,sBAAa,EAACxB,WAAW,CAAC,EAAEM,cAAc,CAAC;EACvF,IAAI,CAACmB,aAAE,CAACC,UAAU,CAACH,yBAAyB,CAAC,EAAE;IAC7C;IACAE,aAAE,CAACE,aAAa,CAACJ,yBAAyB,EAAE9B,sBAAsB,EAAE,MAAM,CAAC;EAC7E;;EAEA;EACA;EACA,MAAMmC,QAAQ,GAAI,GAAExB,WAAY,IAAGE,cAAe,EAAC;EACnD;EACA,IAAI,CAACP,OAAO,CAAC8B,OAAO,CAACD,QAAQ,CAAC,EAAE;IAC9B7B,OAAO,GAAG,IAAA+B,mCAAsB,EAAC;MAC/BC,QAAQ,EAAEH,QAAQ;MAClBI,SAAS,EAAE5B,WAAW;MACtBL,OAAO;MACP;MACAkC,WAAW,EAAE,KAAK;MAClBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EACA,OAAOnC,OAAO;AAChB;AAEO,MAAMoC,iBAA+B,GAAIxC,MAAM,IAAK;EACzD,OAAO,IAAAyC,uCAAmB,EAACzC,MAAM,EAAE;IACjCiC,QAAQ,EAAE,iBAAiB;IAC3BS,QAAQ,EAAE,CACR,IAAI,EACJ,eAAe,EACf,8FAA8F,EAC9F,IAAI,EACJ,EAAE,CACH,CAAC1B,IAAI,CAAC,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;AAACT,OAAA,CAAAiC,iBAAA,GAAAA,iBAAA"}