{"version": 3, "file": "mod-compiler.js", "names": ["_debug", "data", "_interopRequireDefault", "require", "_path", "_Xcodeproj", "_errors", "Warnings", "_interopRequireWildcard", "_createBaseMod", "_withAndroidBaseMods", "_withIosBaseMods", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "debug", "Debug", "withDefaultBaseMods", "config", "props", "withIosBaseMods", "withAndroidBaseMods", "withIntrospectionBaseMods", "saveToInternal", "skipEmptyMod", "mods", "platform", "keys", "_config$mods$platform", "_config$mods$platform2", "isIntrospective", "_config$mods$platform3", "compileModsAsync", "introspect", "evalModsAsync", "sortMods", "commands", "order", "allKeys", "map", "completeOrder", "Set", "sorted", "length", "group", "shift", "commandSet", "find", "push", "getRawClone", "freeze", "JSON", "parse", "stringify", "orders", "ios", "projectRoot", "platforms", "assertMissingModProviders", "ignoreExistingNativeFiles", "modRawConfig", "platformName", "entries", "_config$mods", "includes", "_orders$platformName", "name", "join", "platformProjectRoot", "path", "projectName", "getHackyProjectName", "undefined", "modName", "mod", "modRequest", "is<PERSON><PERSON><PERSON>", "errorMessage", "PluginError", "addWarningForPlatform", "results", "modResults", "assertModResults"], "sources": ["../../src/plugins/mod-compiler.ts"], "sourcesContent": ["import Debug from 'debug';\nimport path from 'path';\n\nimport { ExportedConfig, Mod, ModConfig, ModPlatform } from '../Plugin.types';\nimport { getHackyProjectName } from '../ios/utils/Xcodeproj';\nimport { PluginError } from '../utils/errors';\nimport * as Warnings from '../utils/warnings';\nimport { assertModResults, ForwardedBaseModOptions } from './createBaseMod';\nimport { withAndroidBaseMods } from './withAndroidBaseMods';\nimport { withIosBaseMods } from './withIosBaseMods';\n\nconst debug = Debug('expo:config-plugins:mod-compiler');\n\nexport function withDefaultBaseMods(\n  config: ExportedConfig,\n  props: ForwardedBaseModOptions = {}\n): ExportedConfig {\n  config = withIosBaseMods(config, props);\n  config = withAndroidBaseMods(config, props);\n  return config;\n}\n\n/**\n * Get a prebuild config that safely evaluates mods without persisting any changes to the file system.\n * Currently this only supports infoPlist, entitlements, androidManifest, strings, gradleProperties, and expoPlist mods.\n * This plugin should be evaluated directly:\n */\nexport function withIntrospectionBaseMods(\n  config: ExportedConfig,\n  props: ForwardedBaseModOptions = {}\n): ExportedConfig {\n  config = withIosBaseMods(config, {\n    saveToInternal: true,\n    // This writing optimization can be skipped since we never write in introspection mode.\n    // Including empty mods will ensure that all mods get introspected.\n    skipEmptyMod: false,\n    ...props,\n  });\n  config = withAndroidBaseMods(config, {\n    saveToInternal: true,\n    skipEmptyMod: false,\n    ...props,\n  });\n\n  if (config.mods) {\n    // Remove all mods that don't have an introspection base mod, for instance `dangerous` mods.\n    for (const platform of Object.keys(config.mods) as ModPlatform[]) {\n      // const platformPreserve = preserve[platform];\n      for (const key of Object.keys(config.mods[platform] || {})) {\n        // @ts-ignore\n        if (!config.mods[platform]?.[key]?.isIntrospective) {\n          debug(`removing non-idempotent mod: ${platform}.${key}`);\n          // @ts-ignore\n          delete config.mods[platform]?.[key];\n        }\n      }\n    }\n  }\n\n  return config;\n}\n\n/**\n *\n * @param projectRoot\n * @param config\n */\nexport async function compileModsAsync(\n  config: ExportedConfig,\n  props: {\n    projectRoot: string;\n    platforms?: ModPlatform[];\n    introspect?: boolean;\n    assertMissingModProviders?: boolean;\n    ignoreExistingNativeFiles?: boolean;\n  }\n): Promise<ExportedConfig> {\n  if (props.introspect === true) {\n    config = withIntrospectionBaseMods(config);\n  } else {\n    config = withDefaultBaseMods(config);\n  }\n  return await evalModsAsync(config, props);\n}\n\nfunction sortMods(commands: [string, any][], order: string[]): [string, any][] {\n  const allKeys = commands.map(([key]) => key);\n  const completeOrder = [...new Set([...order, ...allKeys])];\n  const sorted: [string, any][] = [];\n  while (completeOrder.length) {\n    const group = completeOrder.shift()!;\n    const commandSet = commands.find(([key]) => key === group);\n    if (commandSet) {\n      sorted.push(commandSet);\n    }\n  }\n  return sorted;\n}\n\nfunction getRawClone({ mods, ...config }: ExportedConfig) {\n  // Configs should be fully serializable, so we can clone them without worrying about\n  // the mods.\n  return Object.freeze(JSON.parse(JSON.stringify(config)));\n}\n\nconst orders: Record<string, string[]> = {\n  ios: [\n    // dangerous runs first\n    'dangerous',\n    // run the XcodeProject mod second because many plugins attempt to read from it.\n    'xcodeproj',\n  ],\n};\n/**\n * A generic plugin compiler.\n *\n * @param config\n */\nexport async function evalModsAsync(\n  config: ExportedConfig,\n  {\n    projectRoot,\n    introspect,\n    platforms,\n    assertMissingModProviders,\n    ignoreExistingNativeFiles = false,\n  }: {\n    projectRoot: string;\n    introspect?: boolean;\n    platforms?: ModPlatform[];\n    /**\n     * Throw errors when mods are missing providers.\n     * @default true\n     */\n    assertMissingModProviders?: boolean;\n    /** Ignore any existing native files, only use the generated prebuild results. */\n    ignoreExistingNativeFiles?: boolean;\n  }\n): Promise<ExportedConfig> {\n  const modRawConfig = getRawClone(config);\n  for (const [platformName, platform] of Object.entries(config.mods ?? ({} as ModConfig))) {\n    if (platforms && !platforms.includes(platformName as any)) {\n      debug(`skip platform: ${platformName}`);\n      continue;\n    }\n\n    let entries = Object.entries(platform);\n    if (entries.length) {\n      // Move dangerous item to the first position if it exists, this ensures that all dangerous code runs first.\n      entries = sortMods(entries, orders[platformName] ?? ['dangerous']);\n      debug(`run in order: ${entries.map(([name]) => name).join(', ')}`);\n      const platformProjectRoot = path.join(projectRoot, platformName);\n      const projectName =\n        platformName === 'ios' ? getHackyProjectName(projectRoot, config) : undefined;\n\n      for (const [modName, mod] of entries) {\n        const modRequest = {\n          projectRoot,\n          projectName,\n          platformProjectRoot,\n          platform: platformName as ModPlatform,\n          modName,\n          introspect: !!introspect,\n          ignoreExistingNativeFiles,\n        };\n\n        if (!(mod as Mod).isProvider) {\n          // In strict mode, throw an error.\n          const errorMessage = `Initial base modifier for \"${platformName}.${modName}\" is not a provider and therefore will not provide modResults to child mods`;\n          if (assertMissingModProviders !== false) {\n            throw new PluginError(errorMessage, 'MISSING_PROVIDER');\n          } else {\n            Warnings.addWarningForPlatform(\n              platformName as ModPlatform,\n              `${platformName}.${modName}`,\n              `Skipping: Initial base modifier for \"${platformName}.${modName}\" is not a provider and therefore will not provide modResults to child mods. This may be due to an outdated version of Expo CLI.`\n            );\n            // In loose mode, just skip the mod entirely.\n            continue;\n          }\n        }\n\n        const results = await (mod as Mod)({\n          ...config,\n          modResults: null,\n          modRequest,\n          modRawConfig,\n        });\n\n        // Sanity check to help locate non compliant mods.\n        config = assertModResults(results, platformName, modName);\n        // @ts-ignore: `modResults` is added for modifications\n        delete config.modResults;\n        // @ts-ignore: `modRequest` is added for modifications\n        delete config.modRequest;\n        // @ts-ignore: `modRawConfig` is added for modifications\n        delete config.modRawConfig;\n      }\n    }\n  }\n\n  return config;\n}\n"], "mappings": ";;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,WAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,SAAA;EAAA,MAAAN,IAAA,GAAAO,uBAAA,CAAAL,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,eAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,cAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,qBAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,oBAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,iBAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,gBAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoD,SAAAW,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAL,wBAAAS,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAArB,uBAAAe,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEpD,MAAMiB,KAAK,GAAG,IAAAC,gBAAK,EAAC,kCAAkC,CAAC;AAEhD,SAASC,mBAAmBA,CACjCC,MAAsB,EACtBC,KAA8B,GAAG,CAAC,CAAC,EACnB;EAChBD,MAAM,GAAG,IAAAE,kCAAe,EAACF,MAAM,EAAEC,KAAK,CAAC;EACvCD,MAAM,GAAG,IAAAG,0CAAmB,EAACH,MAAM,EAAEC,KAAK,CAAC;EAC3C,OAAOD,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASI,yBAAyBA,CACvCJ,MAAsB,EACtBC,KAA8B,GAAG,CAAC,CAAC,EACnB;EAChBD,MAAM,GAAG,IAAAE,kCAAe,EAACF,MAAM,EAAE;IAC/BK,cAAc,EAAE,IAAI;IACpB;IACA;IACAC,YAAY,EAAE,KAAK;IACnB,GAAGL;EACL,CAAC,CAAC;EACFD,MAAM,GAAG,IAAAG,0CAAmB,EAACH,MAAM,EAAE;IACnCK,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAE,KAAK;IACnB,GAAGL;EACL,CAAC,CAAC;EAEF,IAAID,MAAM,CAACO,IAAI,EAAE;IACf;IACA,KAAK,MAAMC,QAAQ,IAAIpB,MAAM,CAACqB,IAAI,CAACT,MAAM,CAACO,IAAI,CAAC,EAAmB;MAChE;MACA,KAAK,MAAMhB,GAAG,IAAIH,MAAM,CAACqB,IAAI,CAACT,MAAM,CAACO,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QAAA,IAAAE,qBAAA,EAAAC,sBAAA;QAC1D;QACA,IAAI,GAAAD,qBAAA,GAACV,MAAM,CAACO,IAAI,CAACC,QAAQ,CAAC,cAAAE,qBAAA,gBAAAC,sBAAA,GAArBD,qBAAA,CAAwBnB,GAAG,CAAC,cAAAoB,sBAAA,eAA5BA,sBAAA,CAA8BC,eAAe,GAAE;UAAA,IAAAC,sBAAA;UAClDhB,KAAK,CAAE,gCAA+BW,QAAS,IAAGjB,GAAI,EAAC,CAAC;UACxD;UACA,CAAAsB,sBAAA,GAAOb,MAAM,CAACO,IAAI,CAACC,QAAQ,CAAC,cAAAK,sBAAA,qBAA5B,OAAOA,sBAAA,CAAwBtB,GAAG,CAAC;QACrC;MACF;IACF;EACF;EAEA,OAAOS,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACO,eAAec,gBAAgBA,CACpCd,MAAsB,EACtBC,KAMC,EACwB;EACzB,IAAIA,KAAK,CAACc,UAAU,KAAK,IAAI,EAAE;IAC7Bf,MAAM,GAAGI,yBAAyB,CAACJ,MAAM,CAAC;EAC5C,CAAC,MAAM;IACLA,MAAM,GAAGD,mBAAmB,CAACC,MAAM,CAAC;EACtC;EACA,OAAO,MAAMgB,aAAa,CAAChB,MAAM,EAAEC,KAAK,CAAC;AAC3C;AAEA,SAASgB,QAAQA,CAACC,QAAyB,EAAEC,KAAe,EAAmB;EAC7E,MAAMC,OAAO,GAAGF,QAAQ,CAACG,GAAG,CAAC,CAAC,CAAC9B,GAAG,CAAC,KAAKA,GAAG,CAAC;EAC5C,MAAM+B,aAAa,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGJ,KAAK,EAAE,GAAGC,OAAO,CAAC,CAAC,CAAC;EAC1D,MAAMI,MAAuB,GAAG,EAAE;EAClC,OAAOF,aAAa,CAACG,MAAM,EAAE;IAC3B,MAAMC,KAAK,GAAGJ,aAAa,CAACK,KAAK,EAAG;IACpC,MAAMC,UAAU,GAAGV,QAAQ,CAACW,IAAI,CAAC,CAAC,CAACtC,GAAG,CAAC,KAAKA,GAAG,KAAKmC,KAAK,CAAC;IAC1D,IAAIE,UAAU,EAAE;MACdJ,MAAM,CAACM,IAAI,CAACF,UAAU,CAAC;IACzB;EACF;EACA,OAAOJ,MAAM;AACf;AAEA,SAASO,WAAWA,CAAC;EAAExB,IAAI;EAAE,GAAGP;AAAuB,CAAC,EAAE;EACxD;EACA;EACA,OAAOZ,MAAM,CAAC4C,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACnC,MAAM,CAAC,CAAC,CAAC;AAC1D;AAEA,MAAMoC,MAAgC,GAAG;EACvCC,GAAG,EAAE;EACH;EACA,WAAW;EACX;EACA,WAAW;AAEf,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,eAAerB,aAAaA,CACjChB,MAAsB,EACtB;EACEsC,WAAW;EACXvB,UAAU;EACVwB,SAAS;EACTC,yBAAyB;EACzBC,yBAAyB,GAAG;AAY9B,CAAC,EACwB;EACzB,MAAMC,YAAY,GAAGX,WAAW,CAAC/B,MAAM,CAAC;EACxC,KAAK,MAAM,CAAC2C,YAAY,EAAEnC,QAAQ,CAAC,IAAIpB,MAAM,CAACwD,OAAO,EAAAC,YAAA,GAAC7C,MAAM,CAACO,IAAI,cAAAsC,YAAA,cAAAA,YAAA,GAAK,CAAC,CAAC,CAAe,EAAE;IAAA,IAAAA,YAAA;IACvF,IAAIN,SAAS,IAAI,CAACA,SAAS,CAACO,QAAQ,CAACH,YAAY,CAAQ,EAAE;MACzD9C,KAAK,CAAE,kBAAiB8C,YAAa,EAAC,CAAC;MACvC;IACF;IAEA,IAAIC,OAAO,GAAGxD,MAAM,CAACwD,OAAO,CAACpC,QAAQ,CAAC;IACtC,IAAIoC,OAAO,CAACnB,MAAM,EAAE;MAAA,IAAAsB,oBAAA;MAClB;MACAH,OAAO,GAAG3B,QAAQ,CAAC2B,OAAO,GAAAG,oBAAA,GAAEX,MAAM,CAACO,YAAY,CAAC,cAAAI,oBAAA,cAAAA,oBAAA,GAAI,CAAC,WAAW,CAAC,CAAC;MAClElD,KAAK,CAAE,iBAAgB+C,OAAO,CAACvB,GAAG,CAAC,CAAC,CAAC2B,IAAI,CAAC,KAAKA,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;MAClE,MAAMC,mBAAmB,GAAGC,eAAI,CAACF,IAAI,CAACX,WAAW,EAAEK,YAAY,CAAC;MAChE,MAAMS,WAAW,GACfT,YAAY,KAAK,KAAK,GAAG,IAAAU,gCAAmB,EAACf,WAAW,EAAEtC,MAAM,CAAC,GAAGsD,SAAS;MAE/E,KAAK,MAAM,CAACC,OAAO,EAAEC,GAAG,CAAC,IAAIZ,OAAO,EAAE;QACpC,MAAMa,UAAU,GAAG;UACjBnB,WAAW;UACXc,WAAW;UACXF,mBAAmB;UACnB1C,QAAQ,EAAEmC,YAA2B;UACrCY,OAAO;UACPxC,UAAU,EAAE,CAAC,CAACA,UAAU;UACxB0B;QACF,CAAC;QAED,IAAI,CAAEe,GAAG,CAASE,UAAU,EAAE;UAC5B;UACA,MAAMC,YAAY,GAAI,8BAA6BhB,YAAa,IAAGY,OAAQ,6EAA4E;UACvJ,IAAIf,yBAAyB,KAAK,KAAK,EAAE;YACvC,MAAM,KAAIoB,qBAAW,EAACD,YAAY,EAAE,kBAAkB,CAAC;UACzD,CAAC,MAAM;YACLzF,QAAQ,GAAC2F,qBAAqB,CAC5BlB,YAAY,EACX,GAAEA,YAAa,IAAGY,OAAQ,EAAC,EAC3B,wCAAuCZ,YAAa,IAAGY,OAAQ,kIAAiI,CAClM;YACD;YACA;UACF;QACF;QAEA,MAAMO,OAAO,GAAG,MAAON,GAAG,CAAS;UACjC,GAAGxD,MAAM;UACT+D,UAAU,EAAE,IAAI;UAChBN,UAAU;UACVf;QACF,CAAC,CAAC;;QAEF;QACA1C,MAAM,GAAG,IAAAgE,iCAAgB,EAACF,OAAO,EAAEnB,YAAY,EAAEY,OAAO,CAAC;QACzD;QACA,OAAOvD,MAAM,CAAC+D,UAAU;QACxB;QACA,OAAO/D,MAAM,CAACyD,UAAU;QACxB;QACA,OAAOzD,MAAM,CAAC0C,YAAY;MAC5B;IACF;EACF;EAEA,OAAO1C,MAAM;AACf"}