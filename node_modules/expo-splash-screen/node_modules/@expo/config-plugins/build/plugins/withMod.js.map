{"version": 3, "file": "withMod.js", "names": ["_chalk", "data", "_interopRequireDefault", "require", "_getenv", "_errors", "obj", "__esModule", "default", "EXPO_DEBUG", "boolish", "withBaseMod", "config", "platform", "mod", "action", "skipEmptyMod", "is<PERSON><PERSON><PERSON>", "isIntrospective", "saveToInternal", "_config$_internal$isD", "_config$_internal", "mods", "intercepted<PERSON>od", "noopMod", "debugTrace", "isDebug", "_internal", "stack", "Error", "getDebugPluginStackFromStackTrace", "modStack", "chalk", "bold", "PluginError", "interceptingMod", "modRequest", "console", "log", "results", "nextMod", "saveToInternalObject", "modResults", "platformName", "modName", "stacktrace", "treeStackLines", "line", "split", "first", "second", "trim", "push", "plugins", "map", "_ref", "_first$match$1$trim", "_first$match", "_first$match$", "_first$match2", "_first$match2$", "match", "filter", "Boolean", "plugin", "includes", "commonPlugins", "reverse", "pluginName", "index", "toLowerCase", "red", "blue", "dim", "join", "with<PERSON><PERSON>"], "sources": ["../../src/plugins/withMod.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport { JSONObject } from '@expo/json-file';\nimport chalk from 'chalk';\nimport { boolish } from 'getenv';\n\nimport { ExportedConfig, ExportedConfigWithProps, Mod, ModPlatform } from '../Plugin.types';\nimport { PluginError } from '../utils/errors';\n\nconst EXPO_DEBUG = boolish('EXPO_DEBUG', false);\n\nexport type BaseModOptions = {\n  /** Officially supports `'ios' | 'android'` (`ModPlatform`). Arbitrary strings are supported for adding out-of-tree platforms. */\n  platform: ModPlatform & string;\n  mod: string;\n  isProvider?: boolean;\n  skipEmptyMod?: boolean;\n  saveToInternal?: boolean;\n  /**\n   * If the mod supports introspection, and avoids making any filesystem modifications during compilation.\n   * By enabling, this mod, and all of its descendants will be run in introspection mode.\n   * This should only be used for static files like JSON or XML, and not for application files that require regexes,\n   * or complex static files that require other files to be generated like Xcode `.pbxproj`.\n   */\n  isIntrospective?: boolean;\n};\n\n/**\n * Plugin to intercept execution of a given `mod` with the given `action`.\n * If an action was already set on the given `config` config for `mod`, then it\n * will be provided to the `action` as `nextMod` when it's evaluated, otherwise\n * `nextMod` will be an identity function.\n *\n * @param config exported config\n * @param platform platform to target (ios or android)\n * @param mod name of the platform function to intercept\n * @param skipEmptyMod should skip running the action if there is no existing mod to intercept\n * @param saveToInternal should save the results to `_internal.modResults`, only enable this when the results are pure JSON.\n * @param isProvider should provide data up to the other mods.\n * @param action method to run on the mod when the config is compiled\n */\nexport function withBaseMod<T>(\n  config: ExportedConfig,\n  {\n    platform,\n    mod,\n    action,\n    skipEmptyMod,\n    isProvider,\n    isIntrospective,\n    saveToInternal,\n  }: BaseModOptions & { action: Mod<T> }\n): ExportedConfig {\n  if (!config.mods) {\n    config.mods = {};\n  }\n  if (!config.mods[platform]) {\n    config.mods[platform] = {};\n  }\n\n  let interceptedMod: Mod<T> = (config.mods[platform] as Record<string, any>)[mod];\n\n  // No existing mod to intercept\n  if (!interceptedMod) {\n    if (skipEmptyMod) {\n      // Skip running the action\n      return config;\n    }\n    // Use a noop mod and continue\n    const noopMod: Mod<T> = (config) => config;\n    interceptedMod = noopMod;\n  }\n\n  // Create a stack trace for debugging ahead of time\n  let debugTrace: string = '';\n  // Use the possibly user defined value. Otherwise fallback to the env variable.\n  // We support the env variable because user mods won't have _internal defined in time.\n  const isDebug = config._internal?.isDebug ?? EXPO_DEBUG;\n  if (isDebug) {\n    // Get a stack trace via the Error API\n    const stack = new Error().stack;\n    // Format the stack trace to create the debug log\n    debugTrace = getDebugPluginStackFromStackTrace(stack);\n    const modStack = chalk.bold(`${platform}.${mod}`);\n\n    debugTrace = `${modStack}: ${debugTrace}`;\n  }\n\n  // Prevent adding multiple providers to a mod.\n  // Base mods that provide files ignore any incoming modResults and therefore shouldn't have provider mods as parents.\n  if (interceptedMod.isProvider) {\n    if (isProvider) {\n      throw new PluginError(\n        `Cannot set provider mod for \"${platform}.${mod}\" because another is already being used.`,\n        'CONFLICTING_PROVIDER'\n      );\n    } else {\n      throw new PluginError(\n        `Cannot add mod to \"${platform}.${mod}\" because the provider has already been added. Provider must be the last mod added.`,\n        'INVALID_MOD_ORDER'\n      );\n    }\n  }\n\n  async function interceptingMod({ modRequest, ...config }: ExportedConfigWithProps<T>) {\n    if (isDebug) {\n      // In debug mod, log the plugin stack in the order which they were invoked\n      console.log(debugTrace);\n    }\n    const results = await action({\n      ...config,\n      modRequest: { ...modRequest, nextMod: interceptedMod },\n    });\n\n    if (saveToInternal) {\n      saveToInternalObject(results, platform, mod, results.modResults as unknown as JSONObject);\n    }\n    return results;\n  }\n\n  // Ensure this base mod is registered as the provider.\n  interceptingMod.isProvider = isProvider;\n\n  if (isIntrospective) {\n    // Register the mode as idempotent so introspection doesn't remove it.\n    interceptingMod.isIntrospective = isIntrospective;\n  }\n\n  (config.mods[platform] as any)[mod] = interceptingMod;\n\n  return config;\n}\n\nfunction saveToInternalObject(\n  config: Pick<ExpoConfig, '_internal'>,\n  platformName: ModPlatform,\n  modName: string,\n  results: JSONObject\n) {\n  if (!config._internal) config._internal = {};\n  if (!config._internal.modResults) config._internal.modResults = {};\n  if (!config._internal.modResults[platformName]) config._internal.modResults[platformName] = {};\n  config._internal.modResults[platformName][modName] = results;\n}\n\nfunction getDebugPluginStackFromStackTrace(stacktrace?: string): string {\n  if (!stacktrace) {\n    return '';\n  }\n\n  const treeStackLines: string[] = [];\n  for (const line of stacktrace.split('\\n')) {\n    const [first, second] = line.trim().split(' ');\n    if (first === 'at') {\n      treeStackLines.push(second);\n    }\n  }\n\n  const plugins = treeStackLines\n    .map((first) => {\n      // Match the first part of the stack trace against the plugin naming convention\n      // \"with\" followed by a capital letter.\n      return (\n        first?.match(/^(\\bwith[A-Z].*?\\b)/)?.[1]?.trim() ??\n        first?.match(/\\.(\\bwith[A-Z].*?\\b)/)?.[1]?.trim() ??\n        null\n      );\n    })\n    .filter(Boolean)\n    .filter((plugin) => {\n      // redundant as all debug logs are captured in withBaseMod\n      return !['withMod', 'withBaseMod', 'withExtendedMod'].includes(plugin!);\n    });\n\n  const commonPlugins = ['withPlugins', 'withRunOnce', 'withStaticPlugin'];\n\n  return (\n    (plugins as string[])\n      .reverse()\n      .map((pluginName, index) => {\n        // Base mods indicate a logical section.\n        if (pluginName.includes('BaseMod')) {\n          pluginName = chalk.bold(pluginName);\n        }\n        // highlight dangerous mods\n        if (pluginName.toLowerCase().includes('dangerous')) {\n          pluginName = chalk.red(pluginName);\n        }\n\n        if (index === 0) {\n          return chalk.blue(pluginName);\n        } else if (commonPlugins.includes(pluginName)) {\n          // Common mod names often clutter up the logs, dim them out\n          return chalk.dim(pluginName);\n        }\n        return pluginName;\n      })\n      // Join the results:\n      // withAndroidExpoPlugins ➜ withPlugins ➜ withIcons ➜ withDangerousMod ➜ withMod\n      .join(' ➜ ')\n  );\n}\n\n/**\n * Plugin to extend a mod function in the plugins config.\n *\n * @param config exported config\n * @param platform platform to target (ios or android)\n * @param mod name of the platform function to extend\n * @param action method to run on the mod when the config is compiled\n */\nexport function withMod<T>(\n  config: ExportedConfig,\n  {\n    platform,\n    mod,\n    action,\n  }: {\n    platform: ModPlatform;\n    mod: string;\n    action: Mod<T>;\n  }\n): ExportedConfig {\n  return withBaseMod(config, {\n    platform,\n    mod,\n    isProvider: false,\n    async action({ modRequest: { nextMod, ...modRequest }, modResults, ...config }) {\n      const results = await action({ modRequest, modResults: modResults as T, ...config });\n      return nextMod!(results as any);\n    },\n  });\n}\n"], "mappings": ";;;;;;;AAEA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,QAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,OAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA8C,SAAAC,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE9C,MAAMG,UAAU,GAAG,IAAAC,iBAAO,EAAC,YAAY,EAAE,KAAK,CAAC;AAkB/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAWA,CACzBC,MAAsB,EACtB;EACEC,QAAQ;EACRC,GAAG;EACHC,MAAM;EACNC,YAAY;EACZC,UAAU;EACVC,eAAe;EACfC;AACmC,CAAC,EACtB;EAAA,IAAAC,qBAAA,EAAAC,iBAAA;EAChB,IAAI,CAACT,MAAM,CAACU,IAAI,EAAE;IAChBV,MAAM,CAACU,IAAI,GAAG,CAAC,CAAC;EAClB;EACA,IAAI,CAACV,MAAM,CAACU,IAAI,CAACT,QAAQ,CAAC,EAAE;IAC1BD,MAAM,CAACU,IAAI,CAACT,QAAQ,CAAC,GAAG,CAAC,CAAC;EAC5B;EAEA,IAAIU,cAAsB,GAAIX,MAAM,CAACU,IAAI,CAACT,QAAQ,CAAC,CAAyBC,GAAG,CAAC;;EAEhF;EACA,IAAI,CAACS,cAAc,EAAE;IACnB,IAAIP,YAAY,EAAE;MAChB;MACA,OAAOJ,MAAM;IACf;IACA;IACA,MAAMY,OAAe,GAAIZ,MAAM,IAAKA,MAAM;IAC1CW,cAAc,GAAGC,OAAO;EAC1B;;EAEA;EACA,IAAIC,UAAkB,GAAG,EAAE;EAC3B;EACA;EACA,MAAMC,OAAO,IAAAN,qBAAA,IAAAC,iBAAA,GAAGT,MAAM,CAACe,SAAS,cAAAN,iBAAA,uBAAhBA,iBAAA,CAAkBK,OAAO,cAAAN,qBAAA,cAAAA,qBAAA,GAAIX,UAAU;EACvD,IAAIiB,OAAO,EAAE;IACX;IACA,MAAME,KAAK,GAAG,IAAIC,KAAK,EAAE,CAACD,KAAK;IAC/B;IACAH,UAAU,GAAGK,iCAAiC,CAACF,KAAK,CAAC;IACrD,MAAMG,QAAQ,GAAGC,gBAAK,CAACC,IAAI,CAAE,GAAEpB,QAAS,IAAGC,GAAI,EAAC,CAAC;IAEjDW,UAAU,GAAI,GAAEM,QAAS,KAAIN,UAAW,EAAC;EAC3C;;EAEA;EACA;EACA,IAAIF,cAAc,CAACN,UAAU,EAAE;IAC7B,IAAIA,UAAU,EAAE;MACd,MAAM,KAAIiB,qBAAW,EAClB,gCAA+BrB,QAAS,IAAGC,GAAI,0CAAyC,EACzF,sBAAsB,CACvB;IACH,CAAC,MAAM;MACL,MAAM,KAAIoB,qBAAW,EAClB,sBAAqBrB,QAAS,IAAGC,GAAI,qFAAoF,EAC1H,mBAAmB,CACpB;IACH;EACF;EAEA,eAAeqB,eAAeA,CAAC;IAAEC,UAAU;IAAE,GAAGxB;EAAmC,CAAC,EAAE;IACpF,IAAIc,OAAO,EAAE;MACX;MACAW,OAAO,CAACC,GAAG,CAACb,UAAU,CAAC;IACzB;IACA,MAAMc,OAAO,GAAG,MAAMxB,MAAM,CAAC;MAC3B,GAAGH,MAAM;MACTwB,UAAU,EAAE;QAAE,GAAGA,UAAU;QAAEI,OAAO,EAAEjB;MAAe;IACvD,CAAC,CAAC;IAEF,IAAIJ,cAAc,EAAE;MAClBsB,oBAAoB,CAACF,OAAO,EAAE1B,QAAQ,EAAEC,GAAG,EAAEyB,OAAO,CAACG,UAAU,CAA0B;IAC3F;IACA,OAAOH,OAAO;EAChB;;EAEA;EACAJ,eAAe,CAAClB,UAAU,GAAGA,UAAU;EAEvC,IAAIC,eAAe,EAAE;IACnB;IACAiB,eAAe,CAACjB,eAAe,GAAGA,eAAe;EACnD;EAECN,MAAM,CAACU,IAAI,CAACT,QAAQ,CAAC,CAASC,GAAG,CAAC,GAAGqB,eAAe;EAErD,OAAOvB,MAAM;AACf;AAEA,SAAS6B,oBAAoBA,CAC3B7B,MAAqC,EACrC+B,YAAyB,EACzBC,OAAe,EACfL,OAAmB,EACnB;EACA,IAAI,CAAC3B,MAAM,CAACe,SAAS,EAAEf,MAAM,CAACe,SAAS,GAAG,CAAC,CAAC;EAC5C,IAAI,CAACf,MAAM,CAACe,SAAS,CAACe,UAAU,EAAE9B,MAAM,CAACe,SAAS,CAACe,UAAU,GAAG,CAAC,CAAC;EAClE,IAAI,CAAC9B,MAAM,CAACe,SAAS,CAACe,UAAU,CAACC,YAAY,CAAC,EAAE/B,MAAM,CAACe,SAAS,CAACe,UAAU,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;EAC9F/B,MAAM,CAACe,SAAS,CAACe,UAAU,CAACC,YAAY,CAAC,CAACC,OAAO,CAAC,GAAGL,OAAO;AAC9D;AAEA,SAAST,iCAAiCA,CAACe,UAAmB,EAAU;EACtE,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,EAAE;EACX;EAEA,MAAMC,cAAwB,GAAG,EAAE;EACnC,KAAK,MAAMC,IAAI,IAAIF,UAAU,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IACzC,MAAM,CAACC,KAAK,EAAEC,MAAM,CAAC,GAAGH,IAAI,CAACI,IAAI,EAAE,CAACH,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIC,KAAK,KAAK,IAAI,EAAE;MAClBH,cAAc,CAACM,IAAI,CAACF,MAAM,CAAC;IAC7B;EACF;EAEA,MAAMG,OAAO,GAAGP,cAAc,CAC3BQ,GAAG,CAAEL,KAAK,IAAK;IAAA,IAAAM,IAAA,EAAAC,mBAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,cAAA;IACd;IACA;IACA,QAAAL,IAAA,IAAAC,mBAAA,GACEP,KAAK,aAALA,KAAK,wBAAAQ,YAAA,GAALR,KAAK,CAAEY,KAAK,CAAC,qBAAqB,CAAC,cAAAJ,YAAA,wBAAAC,aAAA,GAAnCD,YAAA,CAAsC,CAAC,CAAC,cAAAC,aAAA,uBAAxCA,aAAA,CAA0CP,IAAI,EAAE,cAAAK,mBAAA,cAAAA,mBAAA,GAChDP,KAAK,aAALA,KAAK,wBAAAU,aAAA,GAALV,KAAK,CAAEY,KAAK,CAAC,sBAAsB,CAAC,cAAAF,aAAA,wBAAAC,cAAA,GAApCD,aAAA,CAAuC,CAAC,CAAC,cAAAC,cAAA,uBAAzCA,cAAA,CAA2CT,IAAI,EAAE,cAAAI,IAAA,cAAAA,IAAA,GACjD,IAAI;EAER,CAAC,CAAC,CACDO,MAAM,CAACC,OAAO,CAAC,CACfD,MAAM,CAAEE,MAAM,IAAK;IAClB;IACA,OAAO,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAACC,QAAQ,CAACD,MAAM,CAAE;EACzE,CAAC,CAAC;EAEJ,MAAME,aAAa,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,kBAAkB,CAAC;EAExE,OACGb,OAAO,CACLc,OAAO,EAAE,CACTb,GAAG,CAAC,CAACc,UAAU,EAAEC,KAAK,KAAK;IAC1B;IACA,IAAID,UAAU,CAACH,QAAQ,CAAC,SAAS,CAAC,EAAE;MAClCG,UAAU,GAAGpC,gBAAK,CAACC,IAAI,CAACmC,UAAU,CAAC;IACrC;IACA;IACA,IAAIA,UAAU,CAACE,WAAW,EAAE,CAACL,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClDG,UAAU,GAAGpC,gBAAK,CAACuC,GAAG,CAACH,UAAU,CAAC;IACpC;IAEA,IAAIC,KAAK,KAAK,CAAC,EAAE;MACf,OAAOrC,gBAAK,CAACwC,IAAI,CAACJ,UAAU,CAAC;IAC/B,CAAC,MAAM,IAAIF,aAAa,CAACD,QAAQ,CAACG,UAAU,CAAC,EAAE;MAC7C;MACA,OAAOpC,gBAAK,CAACyC,GAAG,CAACL,UAAU,CAAC;IAC9B;IACA,OAAOA,UAAU;EACnB,CAAC;EACD;EACA;EAAA,CACCM,IAAI,CAAC,KAAK,CAAC;AAElB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,OAAOA,CACrB/D,MAAsB,EACtB;EACEC,QAAQ;EACRC,GAAG;EACHC;AAKF,CAAC,EACe;EAChB,OAAOJ,WAAW,CAACC,MAAM,EAAE;IACzBC,QAAQ;IACRC,GAAG;IACHG,UAAU,EAAE,KAAK;IACjB,MAAMF,MAAMA,CAAC;MAAEqB,UAAU,EAAE;QAAEI,OAAO;QAAE,GAAGJ;MAAW,CAAC;MAAEM,UAAU;MAAE,GAAG9B;IAAO,CAAC,EAAE;MAC9E,MAAM2B,OAAO,GAAG,MAAMxB,MAAM,CAAC;QAAEqB,UAAU;QAAEM,UAAU,EAAEA,UAAe;QAAE,GAAG9B;MAAO,CAAC,CAAC;MACpF,OAAO4B,OAAO,CAAED,OAAO,CAAQ;IACjC;EACF,CAAC,CAAC;AACJ"}