{"version": 3, "file": "withAndroidBaseMods.js", "names": ["_fs", "data", "require", "_path", "_interopRequireDefault", "_android", "_XML", "_sortObject", "_createBaseMod", "obj", "__esModule", "default", "readFile", "writeFile", "promises", "getAndroidManifestTemplate", "config", "_config$android$packa", "_config$android", "parseXMLAsync", "android", "package", "sortAndroidManifest", "manifest", "sortObject", "reverseSortString", "Array", "isArray", "sort", "a", "b", "$", "application", "map", "sortObjWithOrder", "defaultProviders", "dangerous", "provider", "getFilePath", "read", "filePath", "modResults", "write", "isIntrospective", "modRequest", "platformProjectRoot", "path", "join", "Manifest", "readAndroidManifestAsync", "error", "introspect", "writeAndroidManifestAsync", "gradleProperties", "Properties", "parsePropertiesFile", "propertiesListToString", "strings", "projectRoot", "Strings", "getProjectStringsXMLPathAsync", "Resources", "readResourcesXMLAsync", "resources", "writeXMLAsync", "xml", "colors", "Colors", "getProjectColorsXMLPathAsync", "colorsNight", "kind", "styles", "Styles", "getProjectStylesXMLPathAsync", "_styles$resources$$", "fallback", "projectBuildGradle", "Paths", "getProjectBuildGradleFilePath", "getFileInfo", "contents", "<PERSON><PERSON><PERSON><PERSON>", "getSettingsGradleFilePath", "appBuildGradle", "getAppBuildGradleFilePath", "mainActivity", "getProjectFilePath", "mainApplication", "withAndroidBaseMods", "providers", "props", "withGeneratedBaseMods", "platform", "getAndroidModFileProviders"], "sources": ["../../src/plugins/withAndroidBaseMods.ts"], "sourcesContent": ["import { promises } from 'fs';\nimport path from 'path';\n\nimport { ExportedConfig, ModConfig } from '../Plugin.types';\nimport { Colors, Manifest, Paths, Properties, Resources, Strings, Styles } from '../android';\nimport { AndroidManifest } from '../android/Manifest';\nimport { parseXMLAsync, writeXMLAsync } from '../utils/XML';\nimport { reverseSortString, sortObject, sortObjWithOrder } from '../utils/sortObject';\nimport { ForwardedBaseModOptions, provider, withGeneratedBaseMods } from './createBaseMod';\n\nconst { readFile, writeFile } = promises;\n\ntype AndroidModName = keyof Required<ModConfig>['android'];\n\nfunction getAndroidManifestTemplate(config: ExportedConfig) {\n  // Keep in sync with https://github.com/expo/expo/blob/master/templates/expo-template-bare-minimum/android/app/src/main/AndroidManifest.xml\n  // TODO: Read from remote template when possible\n  return parseXMLAsync(`\n  <manifest xmlns:android=\"http://schemas.android.com/apk/res/android\" package=\"${\n    config.android?.package ?? 'com.placeholder.appid'\n  }\">\n\n    <uses-permission android:name=\"android.permission.INTERNET\"/>\n    <!-- OPTIONAL PERMISSIONS, REMOVE WHATEVER YOU DO NOT NEED -->\n    <uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>\n    <!-- These require runtime permissions on M -->\n    <uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>\n    <uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>\n    <!-- END OPTIONAL PERMISSIONS -->\n\n    <queries>\n      <!-- Support checking for http(s) links via the Linking API -->\n      <intent>\n        <action android:name=\"android.intent.action.VIEW\" />\n        <category android:name=\"android.intent.category.BROWSABLE\" />\n        <data android:scheme=\"https\" />\n      </intent>\n    </queries>\n\n    <application\n      android:name=\".MainApplication\"\n      android:label=\"@string/app_name\"\n      android:icon=\"@mipmap/ic_launcher\"\n      android:roundIcon=\"@mipmap/ic_launcher_round\"\n      android:allowBackup=\"false\"\n      android:theme=\"@style/AppTheme\"\n      android:usesCleartextTraffic=\"true\"\n    >\n      <meta-data android:name=\"expo.modules.updates.EXPO_UPDATE_URL\" android:value=\"YOUR-APP-URL-HERE\"/>\n      <meta-data android:name=\"expo.modules.updates.EXPO_SDK_VERSION\" android:value=\"YOUR-APP-SDK-VERSION-HERE\"/>\n      <activity\n        android:name=\".MainActivity\"\n        android:label=\"@string/app_name\"\n        android:configChanges=\"keyboard|keyboardHidden|orientation|screenSize|uiMode\"\n        android:launchMode=\"singleTask\"\n        android:windowSoftInputMode=\"adjustResize\"\n        android:theme=\"@style/Theme.App.SplashScreen\"\n      >\n        <intent-filter>\n          <action android:name=\"android.intent.action.MAIN\"/>\n          <category android:name=\"android.intent.category.LAUNCHER\"/>\n        </intent-filter>\n      </activity>\n      <activity android:name=\"com.facebook.react.devsupport.DevSettingsActivity\"/>\n    </application>\n  </manifest>\n  `) as Promise<AndroidManifest>;\n}\n\nexport function sortAndroidManifest(obj: AndroidManifest) {\n  if (obj.manifest) {\n    // Reverse sort so application is last and permissions are first\n    obj.manifest = sortObject(obj.manifest, reverseSortString);\n\n    if (Array.isArray(obj.manifest['uses-permission'])) {\n      // Sort permissions alphabetically\n      obj.manifest['uses-permission'].sort((a, b) => {\n        if (a.$['android:name'] < b.$['android:name']) return -1;\n        if (a.$['android:name'] > b.$['android:name']) return 1;\n        return 0;\n      });\n    }\n\n    if (Array.isArray(obj.manifest.application)) {\n      // reverse sort applications so activity is towards the end and meta-data is towards the front.\n      obj.manifest.application = obj.manifest.application.map((application) => {\n        application = sortObjWithOrder(application, ['meta-data', 'service', 'activity']);\n\n        if (Array.isArray(application['meta-data'])) {\n          // Sort metadata alphabetically\n          application['meta-data'].sort((a, b) => {\n            if (a.$['android:name'] < b.$['android:name']) return -1;\n            if (a.$['android:name'] > b.$['android:name']) return 1;\n            return 0;\n          });\n        }\n        return application;\n      });\n    }\n  }\n  return obj;\n}\n\nconst defaultProviders = {\n  dangerous: provider<unknown>({\n    getFilePath() {\n      return '';\n    },\n    async read() {\n      return { filePath: '', modResults: {} };\n    },\n    async write() {},\n  }),\n\n  // Append a rule to supply gradle.properties data to mods on `mods.android.gradleProperties`\n  manifest: provider<Manifest.AndroidManifest>({\n    isIntrospective: true,\n    getFilePath({ modRequest: { platformProjectRoot } }) {\n      return path.join(platformProjectRoot, 'app/src/main/AndroidManifest.xml');\n    },\n    async read(filePath, config) {\n      try {\n        return await Manifest.readAndroidManifestAsync(filePath);\n      } catch (error: any) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n      return await getAndroidManifestTemplate(config);\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await Manifest.writeAndroidManifestAsync(filePath, sortAndroidManifest(modResults));\n    },\n  }),\n\n  // Append a rule to supply gradle.properties data to mods on `mods.android.gradleProperties`\n  gradleProperties: provider<Properties.PropertiesItem[]>({\n    isIntrospective: true,\n\n    getFilePath({ modRequest: { platformProjectRoot } }) {\n      return path.join(platformProjectRoot, 'gradle.properties');\n    },\n    async read(filePath, config) {\n      try {\n        return await Properties.parsePropertiesFile(await readFile(filePath, 'utf8'));\n      } catch (error) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n      return [];\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeFile(filePath, Properties.propertiesListToString(modResults));\n    },\n  }),\n\n  // Append a rule to supply strings.xml data to mods on `mods.android.strings`\n  strings: provider<Resources.ResourceXML>({\n    isIntrospective: true,\n\n    async getFilePath({ modRequest: { projectRoot, introspect } }) {\n      try {\n        return await Strings.getProjectStringsXMLPathAsync(projectRoot);\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return '';\n    },\n\n    async read(filePath, config) {\n      try {\n        return await Resources.readResourcesXMLAsync({ path: filePath });\n      } catch (error) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n      return { resources: {} };\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeXMLAsync({ path: filePath, xml: modResults });\n    },\n  }),\n\n  colors: provider<Resources.ResourceXML>({\n    isIntrospective: true,\n\n    async getFilePath({ modRequest: { projectRoot, introspect } }) {\n      try {\n        return await Colors.getProjectColorsXMLPathAsync(projectRoot);\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return '';\n    },\n\n    async read(filePath, { modRequest: { introspect } }) {\n      try {\n        return await Resources.readResourcesXMLAsync({ path: filePath });\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return { resources: {} };\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeXMLAsync({ path: filePath, xml: modResults });\n    },\n  }),\n\n  colorsNight: provider<Resources.ResourceXML>({\n    isIntrospective: true,\n\n    async getFilePath({ modRequest: { projectRoot, introspect } }) {\n      try {\n        return await Colors.getProjectColorsXMLPathAsync(projectRoot, { kind: 'values-night' });\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return '';\n    },\n    async read(filePath, config) {\n      try {\n        return await Resources.readResourcesXMLAsync({ path: filePath });\n      } catch (error: any) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n      return { resources: {} };\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeXMLAsync({ path: filePath, xml: modResults });\n    },\n  }),\n\n  styles: provider<Resources.ResourceXML>({\n    isIntrospective: true,\n\n    async getFilePath({ modRequest: { projectRoot, introspect } }) {\n      try {\n        return await Styles.getProjectStylesXMLPathAsync(projectRoot);\n      } catch (error: any) {\n        if (!introspect) {\n          throw error;\n        }\n      }\n      return '';\n    },\n    async read(filePath, config) {\n      let styles: Resources.ResourceXML = { resources: {} };\n\n      try {\n        // Adds support for `tools:x`\n        styles = await Resources.readResourcesXMLAsync({\n          path: filePath,\n          fallback: `<?xml version=\"1.0\" encoding=\"utf-8\"?><resources xmlns:tools=\"http://schemas.android.com/tools\"></resources>`,\n        });\n      } catch (error: any) {\n        if (!config.modRequest.introspect) {\n          throw error;\n        }\n      }\n\n      // Ensure support for tools is added...\n      if (!styles.resources.$) {\n        styles.resources.$ = {};\n      }\n      if (!styles.resources.$?.['xmlns:tools']) {\n        styles.resources.$['xmlns:tools'] = 'http://schemas.android.com/tools';\n      }\n      return styles;\n    },\n    async write(filePath, { modResults, modRequest: { introspect } }) {\n      if (introspect) return;\n      await writeXMLAsync({ path: filePath, xml: modResults });\n    },\n  }),\n\n  projectBuildGradle: provider<Paths.GradleProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getProjectBuildGradleFilePath(projectRoot);\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  settingsGradle: provider<Paths.GradleProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getSettingsGradleFilePath(projectRoot);\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  appBuildGradle: provider<Paths.GradleProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getAppBuildGradleFilePath(projectRoot);\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  mainActivity: provider<Paths.ApplicationProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getProjectFilePath(projectRoot, 'MainActivity');\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n\n  mainApplication: provider<Paths.ApplicationProjectFile>({\n    getFilePath({ modRequest: { projectRoot } }) {\n      return Paths.getProjectFilePath(projectRoot, 'MainApplication');\n    },\n    async read(filePath) {\n      return Paths.getFileInfo(filePath);\n    },\n    async write(filePath, { modResults: { contents } }) {\n      await writeFile(filePath, contents);\n    },\n  }),\n};\n\ntype AndroidDefaultProviders = typeof defaultProviders;\n\nexport function withAndroidBaseMods(\n  config: ExportedConfig,\n  {\n    providers,\n    ...props\n  }: ForwardedBaseModOptions & { providers?: Partial<AndroidDefaultProviders> } = {}\n): ExportedConfig {\n  return withGeneratedBaseMods<AndroidModName>(config, {\n    ...props,\n    platform: 'android',\n    providers: providers ?? getAndroidModFileProviders(),\n  });\n}\n\nexport function getAndroidModFileProviders() {\n  return defaultProviders;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,YAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,WAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,eAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,cAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2F,SAAAG,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE3F,MAAM;EAAEG,QAAQ;EAAEC;AAAU,CAAC,GAAGC,cAAQ;AAIxC,SAASC,0BAA0BA,CAACC,MAAsB,EAAE;EAAA,IAAAC,qBAAA,EAAAC,eAAA;EAC1D;EACA;EACA,OAAO,IAAAC,oBAAa,EAAE;AACxB,kFAAgF,CAAAF,qBAAA,IAAAC,eAAA,GAC5EF,MAAM,CAACI,OAAO,cAAAF,eAAA,uBAAdA,eAAA,CAAgBG,OAAO,cAAAJ,qBAAA,cAAAA,qBAAA,GAAI,uBAC5B;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC;AACJ;AAEO,SAASK,mBAAmBA,CAACb,GAAoB,EAAE;EACxD,IAAIA,GAAG,CAACc,QAAQ,EAAE;IAChB;IACAd,GAAG,CAACc,QAAQ,GAAG,IAAAC,wBAAU,EAACf,GAAG,CAACc,QAAQ,EAAEE,+BAAiB,CAAC;IAE1D,IAAIC,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACc,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;MAClD;MACAd,GAAG,CAACc,QAAQ,CAAC,iBAAiB,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC7C,IAAID,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC;QACxD,IAAIF,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC;QACvD,OAAO,CAAC;MACV,CAAC,CAAC;IACJ;IAEA,IAAIL,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACc,QAAQ,CAACS,WAAW,CAAC,EAAE;MAC3C;MACAvB,GAAG,CAACc,QAAQ,CAACS,WAAW,GAAGvB,GAAG,CAACc,QAAQ,CAACS,WAAW,CAACC,GAAG,CAAED,WAAW,IAAK;QACvEA,WAAW,GAAG,IAAAE,8BAAgB,EAACF,WAAW,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAEjF,IAAIN,KAAK,CAACC,OAAO,CAACK,WAAW,CAAC,WAAW,CAAC,CAAC,EAAE;UAC3C;UACAA,WAAW,CAAC,WAAW,CAAC,CAACJ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACtC,IAAID,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC;YACxD,IAAIF,CAAC,CAACE,CAAC,CAAC,cAAc,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC;YACvD,OAAO,CAAC;UACV,CAAC,CAAC;QACJ;QACA,OAAOC,WAAW;MACpB,CAAC,CAAC;IACJ;EACF;EACA,OAAOvB,GAAG;AACZ;AAEA,MAAM0B,gBAAgB,GAAG;EACvBC,SAAS,EAAE,IAAAC,yBAAQ,EAAU;IAC3BC,WAAWA,CAAA,EAAG;MACZ,OAAO,EAAE;IACX,CAAC;IACD,MAAMC,IAAIA,CAAA,EAAG;MACX,OAAO;QAAEC,QAAQ,EAAE,EAAE;QAAEC,UAAU,EAAE,CAAC;MAAE,CAAC;IACzC,CAAC;IACD,MAAMC,KAAKA,CAAA,EAAG,CAAC;EACjB,CAAC,CAAC;EAEF;EACAnB,QAAQ,EAAE,IAAAc,yBAAQ,EAA2B;IAC3CM,eAAe,EAAE,IAAI;IACrBL,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEC;MAAoB;IAAE,CAAC,EAAE;MACnD,OAAOC,eAAI,CAACC,IAAI,CAACF,mBAAmB,EAAE,kCAAkC,CAAC;IAC3E,CAAC;IACD,MAAMN,IAAIA,CAACC,QAAQ,EAAExB,MAAM,EAAE;MAC3B,IAAI;QACF,OAAO,MAAMgC,mBAAQ,CAACC,wBAAwB,CAACT,QAAQ,CAAC;MAC1D,CAAC,CAAC,OAAOU,KAAU,EAAE;QACnB,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;MACA,OAAO,MAAMnC,0BAA0B,CAACC,MAAM,CAAC;IACjD,CAAC;IACD,MAAM0B,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEG,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAMH,mBAAQ,CAACI,yBAAyB,CAACZ,QAAQ,EAAElB,mBAAmB,CAACmB,UAAU,CAAC,CAAC;IACrF;EACF,CAAC,CAAC;EAEF;EACAY,gBAAgB,EAAE,IAAAhB,yBAAQ,EAA8B;IACtDM,eAAe,EAAE,IAAI;IAErBL,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEC;MAAoB;IAAE,CAAC,EAAE;MACnD,OAAOC,eAAI,CAACC,IAAI,CAACF,mBAAmB,EAAE,mBAAmB,CAAC;IAC5D,CAAC;IACD,MAAMN,IAAIA,CAACC,QAAQ,EAAExB,MAAM,EAAE;MAC3B,IAAI;QACF,OAAO,MAAMsC,qBAAU,CAACC,mBAAmB,CAAC,MAAM3C,QAAQ,CAAC4B,QAAQ,EAAE,MAAM,CAAC,CAAC;MAC/E,CAAC,CAAC,OAAOU,KAAK,EAAE;QACd,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IACD,MAAMR,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEG,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAMtC,SAAS,CAAC2B,QAAQ,EAAEc,qBAAU,CAACE,sBAAsB,CAACf,UAAU,CAAC,CAAC;IAC1E;EACF,CAAC,CAAC;EAEF;EACAgB,OAAO,EAAE,IAAApB,yBAAQ,EAAwB;IACvCM,eAAe,EAAE,IAAI;IAErB,MAAML,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc,WAAW;QAAEP;MAAW;IAAE,CAAC,EAAE;MAC7D,IAAI;QACF,OAAO,MAAMQ,kBAAO,CAACC,6BAA6B,CAACF,WAAW,CAAC;MACjE,CAAC,CAAC,OAAOR,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IAED,MAAMX,IAAIA,CAACC,QAAQ,EAAExB,MAAM,EAAE;MAC3B,IAAI;QACF,OAAO,MAAM6C,oBAAS,CAACC,qBAAqB,CAAC;UAAEhB,IAAI,EAAEN;QAAS,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOU,KAAK,EAAE;QACd,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;MACA,OAAO;QAAEa,SAAS,EAAE,CAAC;MAAE,CAAC;IAC1B,CAAC;IACD,MAAMrB,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEG,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAM,IAAAa,oBAAa,EAAC;QAAElB,IAAI,EAAEN,QAAQ;QAAEyB,GAAG,EAAExB;MAAW,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEFyB,MAAM,EAAE,IAAA7B,yBAAQ,EAAwB;IACtCM,eAAe,EAAE,IAAI;IAErB,MAAML,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc,WAAW;QAAEP;MAAW;IAAE,CAAC,EAAE;MAC7D,IAAI;QACF,OAAO,MAAMgB,iBAAM,CAACC,4BAA4B,CAACV,WAAW,CAAC;MAC/D,CAAC,CAAC,OAAOR,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IAED,MAAMX,IAAIA,CAACC,QAAQ,EAAE;MAAEI,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MACnD,IAAI;QACF,OAAO,MAAMU,oBAAS,CAACC,qBAAqB,CAAC;UAAEhB,IAAI,EAAEN;QAAS,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOU,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO;QAAEa,SAAS,EAAE,CAAC;MAAE,CAAC;IAC1B,CAAC;IACD,MAAMrB,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEG,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAM,IAAAa,oBAAa,EAAC;QAAElB,IAAI,EAAEN,QAAQ;QAAEyB,GAAG,EAAExB;MAAW,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF4B,WAAW,EAAE,IAAAhC,yBAAQ,EAAwB;IAC3CM,eAAe,EAAE,IAAI;IAErB,MAAML,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc,WAAW;QAAEP;MAAW;IAAE,CAAC,EAAE;MAC7D,IAAI;QACF,OAAO,MAAMgB,iBAAM,CAACC,4BAA4B,CAACV,WAAW,EAAE;UAAEY,IAAI,EAAE;QAAe,CAAC,CAAC;MACzF,CAAC,CAAC,OAAOpB,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IACD,MAAMX,IAAIA,CAACC,QAAQ,EAAExB,MAAM,EAAE;MAC3B,IAAI;QACF,OAAO,MAAM6C,oBAAS,CAACC,qBAAqB,CAAC;UAAEhB,IAAI,EAAEN;QAAS,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOU,KAAU,EAAE;QACnB,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;MACA,OAAO;QAAEa,SAAS,EAAE,CAAC;MAAE,CAAC;IAC1B,CAAC;IACD,MAAMrB,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEG,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAM,IAAAa,oBAAa,EAAC;QAAElB,IAAI,EAAEN,QAAQ;QAAEyB,GAAG,EAAExB;MAAW,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEF8B,MAAM,EAAE,IAAAlC,yBAAQ,EAAwB;IACtCM,eAAe,EAAE,IAAI;IAErB,MAAML,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc,WAAW;QAAEP;MAAW;IAAE,CAAC,EAAE;MAC7D,IAAI;QACF,OAAO,MAAMqB,iBAAM,CAACC,4BAA4B,CAACf,WAAW,CAAC;MAC/D,CAAC,CAAC,OAAOR,KAAU,EAAE;QACnB,IAAI,CAACC,UAAU,EAAE;UACf,MAAMD,KAAK;QACb;MACF;MACA,OAAO,EAAE;IACX,CAAC;IACD,MAAMX,IAAIA,CAACC,QAAQ,EAAExB,MAAM,EAAE;MAAA,IAAA0D,mBAAA;MAC3B,IAAIH,MAA6B,GAAG;QAAER,SAAS,EAAE,CAAC;MAAE,CAAC;MAErD,IAAI;QACF;QACAQ,MAAM,GAAG,MAAMV,oBAAS,CAACC,qBAAqB,CAAC;UAC7ChB,IAAI,EAAEN,QAAQ;UACdmC,QAAQ,EAAG;QACb,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOzB,KAAU,EAAE;QACnB,IAAI,CAAClC,MAAM,CAAC4B,UAAU,CAACO,UAAU,EAAE;UACjC,MAAMD,KAAK;QACb;MACF;;MAEA;MACA,IAAI,CAACqB,MAAM,CAACR,SAAS,CAAChC,CAAC,EAAE;QACvBwC,MAAM,CAACR,SAAS,CAAChC,CAAC,GAAG,CAAC,CAAC;MACzB;MACA,IAAI,GAAA2C,mBAAA,GAACH,MAAM,CAACR,SAAS,CAAChC,CAAC,cAAA2C,mBAAA,eAAlBA,mBAAA,CAAqB,aAAa,CAAC,GAAE;QACxCH,MAAM,CAACR,SAAS,CAAChC,CAAC,CAAC,aAAa,CAAC,GAAG,kCAAkC;MACxE;MACA,OAAOwC,MAAM;IACf,CAAC;IACD,MAAM7B,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU;MAAEG,UAAU,EAAE;QAAEO;MAAW;IAAE,CAAC,EAAE;MAChE,IAAIA,UAAU,EAAE;MAChB,MAAM,IAAAa,oBAAa,EAAC;QAAElB,IAAI,EAAEN,QAAQ;QAAEyB,GAAG,EAAExB;MAAW,CAAC,CAAC;IAC1D;EACF,CAAC,CAAC;EAEFmC,kBAAkB,EAAE,IAAAvC,yBAAQ,EAA0B;IACpDC,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOmB,gBAAK,CAACC,6BAA6B,CAACpB,WAAW,CAAC;IACzD,CAAC;IACD,MAAMnB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMnE,SAAS,CAAC2B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEFC,cAAc,EAAE,IAAA5C,yBAAQ,EAA0B;IAChDC,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOmB,gBAAK,CAACK,yBAAyB,CAACxB,WAAW,CAAC;IACrD,CAAC;IACD,MAAMnB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMnE,SAAS,CAAC2B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEFG,cAAc,EAAE,IAAA9C,yBAAQ,EAA0B;IAChDC,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOmB,gBAAK,CAACO,yBAAyB,CAAC1B,WAAW,CAAC;IACrD,CAAC;IACD,MAAMnB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMnE,SAAS,CAAC2B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEFK,YAAY,EAAE,IAAAhD,yBAAQ,EAA+B;IACnDC,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOmB,gBAAK,CAACS,kBAAkB,CAAC5B,WAAW,EAAE,cAAc,CAAC;IAC9D,CAAC;IACD,MAAMnB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMnE,SAAS,CAAC2B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC,CAAC;EAEFO,eAAe,EAAE,IAAAlD,yBAAQ,EAA+B;IACtDC,WAAWA,CAAC;MAAEM,UAAU,EAAE;QAAEc;MAAY;IAAE,CAAC,EAAE;MAC3C,OAAOmB,gBAAK,CAACS,kBAAkB,CAAC5B,WAAW,EAAE,iBAAiB,CAAC;IACjE,CAAC;IACD,MAAMnB,IAAIA,CAACC,QAAQ,EAAE;MACnB,OAAOqC,gBAAK,CAACE,WAAW,CAACvC,QAAQ,CAAC;IACpC,CAAC;IACD,MAAME,KAAKA,CAACF,QAAQ,EAAE;MAAEC,UAAU,EAAE;QAAEuC;MAAS;IAAE,CAAC,EAAE;MAClD,MAAMnE,SAAS,CAAC2B,QAAQ,EAAEwC,QAAQ,CAAC;IACrC;EACF,CAAC;AACH,CAAC;AAIM,SAASQ,mBAAmBA,CACjCxE,MAAsB,EACtB;EACEyE,SAAS;EACT,GAAGC;AACuE,CAAC,GAAG,CAAC,CAAC,EAClE;EAChB,OAAO,IAAAC,sCAAqB,EAAiB3E,MAAM,EAAE;IACnD,GAAG0E,KAAK;IACRE,QAAQ,EAAE,SAAS;IACnBH,SAAS,EAAEA,SAAS,aAATA,SAAS,cAATA,SAAS,GAAII,0BAA0B;EACpD,CAAC,CAAC;AACJ;AAEO,SAASA,0BAA0BA,CAAA,EAAG;EAC3C,OAAO1D,gBAAgB;AACzB"}