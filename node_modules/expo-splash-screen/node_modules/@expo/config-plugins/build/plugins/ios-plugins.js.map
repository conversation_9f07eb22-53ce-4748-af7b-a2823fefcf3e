{"version": 3, "file": "ios-plugins.js", "names": ["_obj", "data", "require", "_warnings", "_withMod", "createInfoPlistPlugin", "action", "name", "withUnknown", "config", "withInfoPlist", "modResults", "Object", "defineProperty", "value", "createInfoPlistPluginWithPropertyGuard", "settings", "_config$modRawConfig$", "_config$modRawConfig$2", "existingProperty", "expoPropertyGetter", "get", "expoConfigProperty", "modRawConfig", "ios", "infoPlist", "infoPlistProperty", "undefined", "addWarningIOS", "createEntitlementsPlugin", "withEntitlementsPlist", "withAppDelegate", "with<PERSON><PERSON>", "platform", "mod", "exports", "entitlements", "withExpoPlist", "withXcodeProject", "withPodfileProperties"], "sources": ["../../src/plugins/ios-plugins.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\nimport type { JSONObject } from '@expo/json-file';\nimport type { XcodeProject } from 'xcode';\n\nimport type { ConfigPlugin, Mod } from '../Plugin.types';\nimport type { ExpoPlist, InfoPlist } from '../ios/IosConfig.types';\nimport type { AppDelegateProjectFile } from '../ios/Paths';\nimport { get } from '../utils/obj';\nimport { addWarningIOS } from '../utils/warnings';\nimport { withMod } from './withMod';\n\ntype MutateInfoPlistAction = (\n  expo: ExpoConfig,\n  infoPlist: InfoPlist\n) => Promise<InfoPlist> | InfoPlist;\n\n/**\n * Helper method for creating mods from existing config functions.\n *\n * @param action\n */\nexport function createInfoPlistPlugin(action: MutateInfoPlistAction, name?: string): ConfigPlugin {\n  const withUnknown: ConfigPlugin = (config) =>\n    withInfoPlist(config, async (config) => {\n      config.modResults = await action(config, config.modResults);\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\nexport function createInfoPlistPluginWithPropertyGuard(\n  action: MutateInfoPlistAction,\n  settings: {\n    infoPlistProperty: string;\n    expoConfigProperty: string;\n    expoPropertyGetter?: (config: ExpoConfig) => string;\n  },\n  name?: string\n): ConfigPlugin {\n  const withUnknown: ConfigPlugin = (config) =>\n    withInfoPlist(config, async (config) => {\n      const existingProperty = settings.expoPropertyGetter\n        ? settings.expoPropertyGetter(config)\n        : get(config, settings.expoConfigProperty);\n      // If the user explicitly sets a value in the infoPlist, we should respect that.\n      if (config.modRawConfig.ios?.infoPlist?.[settings.infoPlistProperty] === undefined) {\n        config.modResults = await action(config, config.modResults);\n      } else if (existingProperty !== undefined) {\n        // Only warn if there is a conflict.\n        addWarningIOS(\n          settings.expoConfigProperty,\n          `\"ios.infoPlist.${settings.infoPlistProperty}\" is set in the config. Ignoring abstract property \"${settings.expoConfigProperty}\": ${existingProperty}`\n        );\n      }\n\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\ntype MutateEntitlementsPlistAction = (expo: ExpoConfig, entitlements: JSONObject) => JSONObject;\n\n/**\n * Helper method for creating mods from existing config functions.\n *\n * @param action\n */\nexport function createEntitlementsPlugin(\n  action: MutateEntitlementsPlistAction,\n  name: string\n): ConfigPlugin {\n  const withUnknown: ConfigPlugin = (config) =>\n    withEntitlementsPlist(config, async (config) => {\n      config.modResults = await action(config, config.modResults);\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\n/**\n * Provides the AppDelegate file for modification.\n *\n * @param config\n * @param action\n */\nexport const withAppDelegate: ConfigPlugin<Mod<AppDelegateProjectFile>> = (config, action) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'appDelegate',\n    action,\n  });\n};\n\n/**\n * Provides the Info.plist file for modification.\n * Keeps the config's expo.ios.infoPlist object in sync with the data.\n *\n * @param config\n * @param action\n */\nexport const withInfoPlist: ConfigPlugin<Mod<InfoPlist>> = (config, action) => {\n  return withMod<InfoPlist>(config, {\n    platform: 'ios',\n    mod: 'infoPlist',\n    async action(config) {\n      config = await action(config);\n      if (!config.ios) {\n        config.ios = {};\n      }\n      config.ios.infoPlist = config.modResults;\n      return config;\n    },\n  });\n};\n\n/**\n * Provides the main .entitlements file for modification.\n * Keeps the config's expo.ios.entitlements object in sync with the data.\n *\n * @param config\n * @param action\n */\nexport const withEntitlementsPlist: ConfigPlugin<Mod<JSONObject>> = (config, action) => {\n  return withMod<JSONObject>(config, {\n    platform: 'ios',\n    mod: 'entitlements',\n    async action(config) {\n      config = await action(config);\n      if (!config.ios) {\n        config.ios = {};\n      }\n      config.ios.entitlements = config.modResults;\n      return config;\n    },\n  });\n};\n\n/**\n * Provides the Expo.plist for modification.\n *\n * @param config\n * @param action\n */\nexport const withExpoPlist: ConfigPlugin<Mod<ExpoPlist>> = (config, action) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'expoPlist',\n    action,\n  });\n};\n\n/**\n * Provides the main .xcodeproj for modification.\n *\n * @param config\n * @param action\n */\nexport const withXcodeProject: ConfigPlugin<Mod<XcodeProject>> = (config, action) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'xcodeproj',\n    action,\n  });\n};\n\n/**\n * Provides the Podfile.properties.json for modification.\n *\n * @param config\n * @param action\n */\nexport const withPodfileProperties: ConfigPlugin<Mod<Record<string, string>>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'podfileProperties',\n    action,\n  });\n};\n"], "mappings": ";;;;;;;;;AAOA,SAAAA,KAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,IAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,SAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,QAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AACO,SAASI,qBAAqBA,CAACC,MAA6B,EAAEC,IAAa,EAAgB;EAChG,MAAMC,WAAyB,GAAIC,MAAM,IACvCC,aAAa,CAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IACtCA,MAAM,CAACE,UAAU,GAAG,MAAML,MAAM,CAACG,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC3D,OAAOF,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRK,MAAM,CAACC,cAAc,CAACL,WAAW,EAAE,MAAM,EAAE;MACzCM,KAAK,EAAEP;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;AAEO,SAASO,sCAAsCA,CACpDT,MAA6B,EAC7BU,QAIC,EACDT,IAAa,EACC;EACd,MAAMC,WAAyB,GAAIC,MAAM,IACvCC,aAAa,CAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAAA,IAAAQ,qBAAA,EAAAC,sBAAA;IACtC,MAAMC,gBAAgB,GAAGH,QAAQ,CAACI,kBAAkB,GAChDJ,QAAQ,CAACI,kBAAkB,CAACX,MAAM,CAAC,GACnC,IAAAY,UAAG,EAACZ,MAAM,EAAEO,QAAQ,CAACM,kBAAkB,CAAC;IAC5C;IACA,IAAI,EAAAL,qBAAA,GAAAR,MAAM,CAACc,YAAY,CAACC,GAAG,cAAAP,qBAAA,wBAAAC,sBAAA,GAAvBD,qBAAA,CAAyBQ,SAAS,cAAAP,sBAAA,uBAAlCA,sBAAA,CAAqCF,QAAQ,CAACU,iBAAiB,CAAC,MAAKC,SAAS,EAAE;MAClFlB,MAAM,CAACE,UAAU,GAAG,MAAML,MAAM,CAACG,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC7D,CAAC,MAAM,IAAIQ,gBAAgB,KAAKQ,SAAS,EAAE;MACzC;MACA,IAAAC,yBAAa,EACXZ,QAAQ,CAACM,kBAAkB,EAC1B,kBAAiBN,QAAQ,CAACU,iBAAkB,uDAAsDV,QAAQ,CAACM,kBAAmB,MAAKH,gBAAiB,EAAC,CACvJ;IACH;IAEA,OAAOV,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRK,MAAM,CAACC,cAAc,CAACL,WAAW,EAAE,MAAM,EAAE;MACzCM,KAAK,EAAEP;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;AAIA;AACA;AACA;AACA;AACA;AACO,SAASqB,wBAAwBA,CACtCvB,MAAqC,EACrCC,IAAY,EACE;EACd,MAAMC,WAAyB,GAAIC,MAAM,IACvCqB,qBAAqB,CAACrB,MAAM,EAAE,MAAOA,MAAM,IAAK;IAC9CA,MAAM,CAACE,UAAU,GAAG,MAAML,MAAM,CAACG,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC3D,OAAOF,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRK,MAAM,CAACC,cAAc,CAACL,WAAW,EAAE,MAAM,EAAE;MACzCM,KAAK,EAAEP;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMuB,eAA0D,GAAGA,CAACtB,MAAM,EAAEH,MAAM,KAAK;EAC5F,OAAO,IAAA0B,kBAAO,EAACvB,MAAM,EAAE;IACrBwB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,aAAa;IAClB5B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANA6B,OAAA,CAAAJ,eAAA,GAAAA,eAAA;AAOO,MAAMrB,aAA2C,GAAGA,CAACD,MAAM,EAAEH,MAAM,KAAK;EAC7E,OAAO,IAAA0B,kBAAO,EAAYvB,MAAM,EAAE;IAChCwB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,WAAW;IAChB,MAAM5B,MAAMA,CAACG,MAAM,EAAE;MACnBA,MAAM,GAAG,MAAMH,MAAM,CAACG,MAAM,CAAC;MAC7B,IAAI,CAACA,MAAM,CAACe,GAAG,EAAE;QACff,MAAM,CAACe,GAAG,GAAG,CAAC,CAAC;MACjB;MACAf,MAAM,CAACe,GAAG,CAACC,SAAS,GAAGhB,MAAM,CAACE,UAAU;MACxC,OAAOF,MAAM;IACf;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANA0B,OAAA,CAAAzB,aAAA,GAAAA,aAAA;AAOO,MAAMoB,qBAAoD,GAAGA,CAACrB,MAAM,EAAEH,MAAM,KAAK;EACtF,OAAO,IAAA0B,kBAAO,EAAavB,MAAM,EAAE;IACjCwB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,cAAc;IACnB,MAAM5B,MAAMA,CAACG,MAAM,EAAE;MACnBA,MAAM,GAAG,MAAMH,MAAM,CAACG,MAAM,CAAC;MAC7B,IAAI,CAACA,MAAM,CAACe,GAAG,EAAE;QACff,MAAM,CAACe,GAAG,GAAG,CAAC,CAAC;MACjB;MACAf,MAAM,CAACe,GAAG,CAACY,YAAY,GAAG3B,MAAM,CAACE,UAAU;MAC3C,OAAOF,MAAM;IACf;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA0B,OAAA,CAAAL,qBAAA,GAAAA,qBAAA;AAMO,MAAMO,aAA2C,GAAGA,CAAC5B,MAAM,EAAEH,MAAM,KAAK;EAC7E,OAAO,IAAA0B,kBAAO,EAACvB,MAAM,EAAE;IACrBwB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,WAAW;IAChB5B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA6B,OAAA,CAAAE,aAAA,GAAAA,aAAA;AAMO,MAAMC,gBAAiD,GAAGA,CAAC7B,MAAM,EAAEH,MAAM,KAAK;EACnF,OAAO,IAAA0B,kBAAO,EAACvB,MAAM,EAAE;IACrBwB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,WAAW;IAChB5B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA6B,OAAA,CAAAG,gBAAA,GAAAA,gBAAA;AAMO,MAAMC,qBAAgE,GAAGA,CAC9E9B,MAAM,EACNH,MAAM,KACH;EACH,OAAO,IAAA0B,kBAAO,EAACvB,MAAM,EAAE;IACrBwB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,mBAAmB;IACxB5B;EACF,CAAC,CAAC;AACJ,CAAC;AAAC6B,OAAA,CAAAI,qBAAA,GAAAA,qBAAA"}