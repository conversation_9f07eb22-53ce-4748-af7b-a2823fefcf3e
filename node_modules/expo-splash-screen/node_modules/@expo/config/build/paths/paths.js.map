{"version": 3, "file": "paths.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_resolveFrom", "_Config", "_extensions", "obj", "__esModule", "default", "ensureSlash", "inputPath", "needsSlash", "hasSlash", "endsWith", "substr", "length", "getPossibleProjectRoot", "fs", "realpathSync", "process", "cwd", "nativePlatforms", "resolveEntryPoint", "projectRoot", "platform", "projectConfig", "platforms", "includes", "getEntryPoint", "entryFiles", "extensions", "getBareExtensions", "getEntryPointWithExtensions", "original", "stdout", "write", "getConfig", "skipSDKVersionRequirement", "pkg", "main", "entry", "getFileWithExtensions", "resolveFromSilentWithExtensions", "Error", "fileName", "resolveFrom", "fromDirectory", "moduleId", "extension", "modulePath", "silent", "path", "join", "existsSync"], "sources": ["../../src/paths/paths.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { getConfig } from '../Config';\nimport { ProjectConfig } from '../Config.types';\nimport { getBareExtensions } from './extensions';\n\n// https://github.com/facebook/create-react-app/blob/9750738cce89a967cc71f28390daf5d4311b193c/packages/react-scripts/config/paths.js#L22\nexport function ensureSlash(inputPath: string, needsSlash: boolean): string {\n  const hasSlash = inputPath.endsWith('/');\n  if (hasSlash && !needsSlash) {\n    return inputPath.substr(0, inputPath.length - 1);\n  } else if (!hasSlash && needsSlash) {\n    return `${inputPath}/`;\n  } else {\n    return inputPath;\n  }\n}\n\nexport function getPossibleProjectRoot(): string {\n  return fs.realpathSync(process.cwd());\n}\n\nconst nativePlatforms = ['ios', 'android'];\n\nexport function resolveEntryPoint(\n  projectRoot: string,\n  { platform, projectConfig }: { platform: string; projectConfig?: Partial<ProjectConfig> }\n) {\n  const platforms = nativePlatforms.includes(platform) ? [platform, 'native'] : [platform];\n  return getEntryPoint(projectRoot, ['./index'], platforms, projectConfig);\n}\n\nexport function getEntryPoint(\n  projectRoot: string,\n  entryFiles: string[],\n  platforms: string[],\n  projectConfig?: Partial<ProjectConfig>\n): string | null {\n  const extensions = getBareExtensions(platforms);\n  return getEntryPointWithExtensions(projectRoot, entryFiles, extensions, projectConfig);\n}\n\n// Used to resolve the main entry file for a project.\nexport function getEntryPointWithExtensions(\n  projectRoot: string,\n  entryFiles: string[],\n  extensions: string[],\n  projectConfig?: Partial<ProjectConfig>\n): string {\n  if (!projectConfig) {\n    // drop all logging abilities\n    const original = process.stdout.write;\n    process.stdout.write = () => true;\n    try {\n      projectConfig = getConfig(projectRoot, { skipSDKVersionRequirement: true });\n    } finally {\n      process.stdout.write = original;\n    }\n  }\n\n  const { pkg } = projectConfig;\n\n  if (pkg) {\n    // If the config doesn't define a custom entry then we want to look at the `package.json`s `main` field, and try again.\n    const { main } = pkg;\n    if (main && typeof main === 'string') {\n      // Testing the main field against all of the provided extensions - for legacy reasons we can't use node module resolution as the package.json allows you to pass in a file without a relative path and expect it as a relative path.\n      let entry = getFileWithExtensions(projectRoot, main, extensions);\n      if (!entry) {\n        // Allow for paths like: `{ \"main\": \"expo/AppEntry\" }`\n        entry = resolveFromSilentWithExtensions(projectRoot, main, extensions);\n        if (!entry)\n          throw new Error(\n            `Cannot resolve entry file: The \\`main\\` field defined in your \\`package.json\\` points to a non-existent path.`\n          );\n      }\n      return entry;\n    }\n  }\n\n  // Now we will start looking for a default entry point using the provided `entryFiles` argument.\n  // This will add support for create-react-app (src/index.js) and react-native-cli (index.js) which don't define a main.\n  for (const fileName of entryFiles) {\n    const entry = resolveFromSilentWithExtensions(projectRoot, fileName, extensions);\n    if (entry) return entry;\n  }\n\n  try {\n    // If none of the default files exist then we will attempt to use the main Expo entry point.\n    // This requires `expo` to be installed in the project to work as it will use `node_module/expo/AppEntry.js`\n    // Doing this enables us to create a bare minimum Expo project.\n\n    // TODO(Bacon): We may want to do a check against `./App` and `expo` in the `package.json` `dependencies` as we can more accurately ensure that the project is expo-min without needing the modules installed.\n    return resolveFrom(projectRoot, 'expo/AppEntry');\n  } catch {\n    throw new Error(\n      `The project entry file could not be resolved. Please define it in the \\`main\\` field of the \\`package.json\\`, create an \\`index.js\\`, or install the \\`expo\\` package.`\n    );\n  }\n}\n\n// Resolve from but with the ability to resolve like a bundler\nexport function resolveFromSilentWithExtensions(\n  fromDirectory: string,\n  moduleId: string,\n  extensions: string[]\n): string | null {\n  for (const extension of extensions) {\n    const modulePath = resolveFrom.silent(fromDirectory, `${moduleId}.${extension}`);\n    if (modulePath && modulePath.endsWith(extension)) {\n      return modulePath;\n    }\n  }\n  return resolveFrom.silent(fromDirectory, moduleId) || null;\n}\n\n// Statically attempt to resolve a module but with the ability to resolve like a bundler.\n// This won't use node module resolution.\nexport function getFileWithExtensions(\n  fromDirectory: string,\n  moduleId: string,\n  extensions: string[]\n): string | null {\n  const modulePath = path.join(fromDirectory, moduleId);\n  if (fs.existsSync(modulePath)) {\n    return modulePath;\n  }\n  for (const extension of extensions) {\n    const modulePath = path.join(fromDirectory, `${moduleId}.${extension}`);\n    if (fs.existsSync(modulePath)) {\n      return modulePath;\n    }\n  }\n  return null;\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,aAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,YAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,YAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,WAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiD,SAAAC,uBAAAM,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAEjD;AACO,SAASG,WAAWA,CAACC,SAAiB,EAAEC,UAAmB,EAAU;EAC1E,MAAMC,QAAQ,GAAGF,SAAS,CAACG,QAAQ,CAAC,GAAG,CAAC;EACxC,IAAID,QAAQ,IAAI,CAACD,UAAU,EAAE;IAC3B,OAAOD,SAAS,CAACI,MAAM,CAAC,CAAC,EAAEJ,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC;EAClD,CAAC,MAAM,IAAI,CAACH,QAAQ,IAAID,UAAU,EAAE;IAClC,OAAQ,GAAED,SAAU,GAAE;EACxB,CAAC,MAAM;IACL,OAAOA,SAAS;EAClB;AACF;AAEO,SAASM,sBAAsBA,CAAA,EAAW;EAC/C,OAAOC,aAAE,CAACC,YAAY,CAACC,OAAO,CAACC,GAAG,EAAE,CAAC;AACvC;AAEA,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC;AAEnC,SAASC,iBAAiBA,CAC/BC,WAAmB,EACnB;EAAEC,QAAQ;EAAEC;AAA4E,CAAC,EACzF;EACA,MAAMC,SAAS,GAAGL,eAAe,CAACM,QAAQ,CAACH,QAAQ,CAAC,GAAG,CAACA,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAACA,QAAQ,CAAC;EACxF,OAAOI,aAAa,CAACL,WAAW,EAAE,CAAC,SAAS,CAAC,EAAEG,SAAS,EAAED,aAAa,CAAC;AAC1E;AAEO,SAASG,aAAaA,CAC3BL,WAAmB,EACnBM,UAAoB,EACpBH,SAAmB,EACnBD,aAAsC,EACvB;EACf,MAAMK,UAAU,GAAG,IAAAC,+BAAiB,EAACL,SAAS,CAAC;EAC/C,OAAOM,2BAA2B,CAACT,WAAW,EAAEM,UAAU,EAAEC,UAAU,EAAEL,aAAa,CAAC;AACxF;;AAEA;AACO,SAASO,2BAA2BA,CACzCT,WAAmB,EACnBM,UAAoB,EACpBC,UAAoB,EACpBL,aAAsC,EAC9B;EACR,IAAI,CAACA,aAAa,EAAE;IAClB;IACA,MAAMQ,QAAQ,GAAGd,OAAO,CAACe,MAAM,CAACC,KAAK;IACrChB,OAAO,CAACe,MAAM,CAACC,KAAK,GAAG,MAAM,IAAI;IACjC,IAAI;MACFV,aAAa,GAAG,IAAAW,mBAAS,EAACb,WAAW,EAAE;QAAEc,yBAAyB,EAAE;MAAK,CAAC,CAAC;IAC7E,CAAC,SAAS;MACRlB,OAAO,CAACe,MAAM,CAACC,KAAK,GAAGF,QAAQ;IACjC;EACF;EAEA,MAAM;IAAEK;EAAI,CAAC,GAAGb,aAAa;EAE7B,IAAIa,GAAG,EAAE;IACP;IACA,MAAM;MAAEC;IAAK,CAAC,GAAGD,GAAG;IACpB,IAAIC,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACpC;MACA,IAAIC,KAAK,GAAGC,qBAAqB,CAAClB,WAAW,EAAEgB,IAAI,EAAET,UAAU,CAAC;MAChE,IAAI,CAACU,KAAK,EAAE;QACV;QACAA,KAAK,GAAGE,+BAA+B,CAACnB,WAAW,EAAEgB,IAAI,EAAET,UAAU,CAAC;QACtE,IAAI,CAACU,KAAK,EACR,MAAM,IAAIG,KAAK,CACZ,+GAA8G,CAChH;MACL;MACA,OAAOH,KAAK;IACd;EACF;;EAEA;EACA;EACA,KAAK,MAAMI,QAAQ,IAAIf,UAAU,EAAE;IACjC,MAAMW,KAAK,GAAGE,+BAA+B,CAACnB,WAAW,EAAEqB,QAAQ,EAAEd,UAAU,CAAC;IAChF,IAAIU,KAAK,EAAE,OAAOA,KAAK;EACzB;EAEA,IAAI;IACF;IACA;IACA;;IAEA;IACA,OAAO,IAAAK,sBAAW,EAACtB,WAAW,EAAE,eAAe,CAAC;EAClD,CAAC,CAAC,MAAM;IACN,MAAM,IAAIoB,KAAK,CACZ,wKAAuK,CACzK;EACH;AACF;;AAEA;AACO,SAASD,+BAA+BA,CAC7CI,aAAqB,EACrBC,QAAgB,EAChBjB,UAAoB,EACL;EACf,KAAK,MAAMkB,SAAS,IAAIlB,UAAU,EAAE;IAClC,MAAMmB,UAAU,GAAGJ,sBAAW,CAACK,MAAM,CAACJ,aAAa,EAAG,GAAEC,QAAS,IAAGC,SAAU,EAAC,CAAC;IAChF,IAAIC,UAAU,IAAIA,UAAU,CAACpC,QAAQ,CAACmC,SAAS,CAAC,EAAE;MAChD,OAAOC,UAAU;IACnB;EACF;EACA,OAAOJ,sBAAW,CAACK,MAAM,CAACJ,aAAa,EAAEC,QAAQ,CAAC,IAAI,IAAI;AAC5D;;AAEA;AACA;AACO,SAASN,qBAAqBA,CACnCK,aAAqB,EACrBC,QAAgB,EAChBjB,UAAoB,EACL;EACf,MAAMmB,UAAU,GAAGE,eAAI,CAACC,IAAI,CAACN,aAAa,EAAEC,QAAQ,CAAC;EACrD,IAAI9B,aAAE,CAACoC,UAAU,CAACJ,UAAU,CAAC,EAAE;IAC7B,OAAOA,UAAU;EACnB;EACA,KAAK,MAAMD,SAAS,IAAIlB,UAAU,EAAE;IAClC,MAAMmB,UAAU,GAAGE,eAAI,CAACC,IAAI,CAACN,aAAa,EAAG,GAAEC,QAAS,IAAGC,SAAU,EAAC,CAAC;IACvE,IAAI/B,aAAE,CAACoC,UAAU,CAACJ,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU;IACnB;EACF;EACA,OAAO,IAAI;AACb"}