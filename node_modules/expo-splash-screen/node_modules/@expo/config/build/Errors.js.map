{"version": 3, "file": "Errors.js", "names": ["ConfigError", "Error", "constructor", "message", "code", "cause", "name", "_defineProperty", "exports"], "sources": ["../src/Errors.ts"], "sourcesContent": ["import { ConfigErrorCode } from './Config.types';\n\n/**\n * Based on `JsonFileError` from `@expo/json-file`\n */\nexport class ConfigError extends Error {\n  readonly name = 'ConfigError';\n  readonly isConfigError = true;\n\n  constructor(message: string, public code: ConfigErrorCode, public cause?: Error) {\n    super(cause ? `${message}\\n└─ Cause: ${cause.name}: ${cause.message}` : message);\n  }\n}\n"], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AACO,MAAMA,WAAW,SAASC,KAAK,CAAC;EAIrCC,WAAWA,CAACC,OAAe,EAASC,IAAqB,EAASC,KAAa,EAAE;IAC/E,KAAK,CAACA,KAAK,GAAI,GAAEF,OAAQ,eAAcE,KAAK,CAACC,IAAK,KAAID,KAAK,CAACF,OAAQ,EAAC,GAAGA,OAAO,CAAC;IAAC,KAD/CC,IAAqB,GAArBA,IAAqB;IAAA,KAASC,KAAa,GAAbA,KAAa;IAAAE,eAAA,eAH/D,aAAa;IAAAA,eAAA,wBACJ,IAAI;EAI7B;AACF;AAACC,OAAA,CAAAR,WAAA,GAAAA,WAAA"}