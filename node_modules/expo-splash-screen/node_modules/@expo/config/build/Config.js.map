{"version": 3, "file": "Config.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_fs", "_glob", "_path", "_resolveFrom", "_semver", "_slugify", "_getConfig", "_getExpoSDKVersion", "_withConfigPlugins", "_withInternal", "_resolvePackageJson", "_Config", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "obj", "__esModule", "default", "reduceExpoObject", "config", "_config$expo", "undefined", "mods", "expo", "getSupportedPlatforms", "projectRoot", "platforms", "resolveFrom", "silent", "push", "getConfig", "options", "paths", "getConfigFilePaths", "rawStaticConfig", "staticConfigPath", "getStaticConfig", "rootConfig", "staticConfig", "packageJson", "packageJsonPath", "getPackageJsonAndPath", "fillAndReturnConfig", "dynamicConfigObjectType", "configWithDefaultValues", "ensureConfigHasDefaultValues", "exp", "pkg", "skipSDKVersionRequirement", "dynamicConfigPath", "isModdedConfig", "_config$mods", "withConfigPlugins", "skip<PERSON>lug<PERSON>", "isPublicConfig", "_configWithDefaultVal", "_configWithDefaultVal2", "_configWithDefaultVal3", "_configWithDefaultVal4", "_internal", "hooks", "ios", "android", "updates", "codeSigningCertificate", "codeSigningMetadata", "getContextConfig", "exportedObjectType", "rawDynamicConfig", "getDynamicConfig", "dynamicConfig", "getPackageJson", "getRootPackageJsonPath", "JsonFile", "read", "getDynamicConfigFilePath", "getStaticConfigFilePath", "fileName", "config<PERSON><PERSON>", "path", "join", "fs", "existsSync", "modifyConfigAsync", "modifications", "readOptions", "writeOptions", "type", "message", "relative", "outputConfig", "dryRun", "writeAsync", "json5", "_exp$name", "_exp$slug", "_exp$version", "withInternal", "pkgName", "name", "basename", "pkgVersion", "version", "pkgWithDefaults", "slug", "slugify", "toLowerCase", "description", "expWithDefaults", "sdkVersion", "getExpoSDKVersion", "error", "DEFAULT_BUILD_PATH", "getWebOutputPath", "_expo$web", "_expo$web$build", "process", "env", "WEBPACK_BUILD_OUTPUT_PATH", "web", "build", "output", "getNameFromConfig", "appManifest", "appName", "displayName", "webName", "getDefaultTarget", "_exp", "semver", "lt", "isBareWorkflowProject", "dependencies", "expokit", "xcodeprojFiles", "globSync", "absolute", "cwd", "length", "gradleFiles", "getProjectConfigDescription", "getProjectConfigDescriptionWithPaths", "projectConfig", "relativeDynamicConfigPath"], "sources": ["../src/Config.ts"], "sourcesContent": ["import { ModConfig } from '@expo/config-plugins';\nimport Json<PERSON><PERSON>, { JSONObject } from '@expo/json-file';\nimport fs from 'fs';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\nimport slugify from 'slugify';\n\nimport {\n  AppJSONConfig,\n  ConfigFilePaths,\n  ExpoConfig,\n  GetConfigOptions,\n  PackageJSONConfig,\n  Platform,\n  ProjectConfig,\n  ProjectTarget,\n  WriteConfigOptions,\n} from './Config.types';\nimport { getDynamicConfig, getStaticConfig } from './getConfig';\nimport { getExpoSDKVersion } from './getExpoSDKVersion';\nimport { withConfigPlugins } from './plugins/withConfigPlugins';\nimport { withInternal } from './plugins/withInternal';\nimport { getRootPackageJsonPath } from './resolvePackageJson';\n\ntype SplitConfigs = { expo: ExpoConfig; mods: ModConfig };\n\n/**\n * If a config has an `expo` object then that will be used as the config.\n * This method reduces out other top level values if an `expo` object exists.\n *\n * @param config Input config object to reduce\n */\nfunction reduceExpoObject(config?: any): SplitConfigs {\n  if (!config) return config === undefined ? null : config;\n\n  const { mods, ...expo } = config.expo ?? config;\n\n  return {\n    expo,\n    mods,\n  };\n}\n\n/**\n * Get all platforms that a project is currently capable of running.\n *\n * @param projectRoot\n * @param exp\n */\nfunction getSupportedPlatforms(projectRoot: string): Platform[] {\n  const platforms: Platform[] = [];\n  if (resolveFrom.silent(projectRoot, 'react-native')) {\n    platforms.push('ios', 'android');\n  }\n  if (resolveFrom.silent(projectRoot, 'react-native-web')) {\n    platforms.push('web');\n  }\n  return platforms;\n}\n\n/**\n * Evaluate the config for an Expo project.\n * If a function is exported from the `app.config.js` then a partial config will be passed as an argument.\n * The partial config is composed from any existing app.json, and certain fields from the `package.json` like name and description.\n *\n * If options.isPublicConfig is true, the Expo config will include only public-facing options (omitting private keys).\n * The resulting config should be suitable for hosting or embedding in a publicly readable location.\n *\n * **Example**\n * ```js\n * module.exports = function({ config }) {\n *   // mutate the config before returning it.\n *   config.slug = 'new slug'\n *   return { expo: config };\n * }\n * ```\n *\n * **Supports**\n * - `app.config.ts`\n * - `app.config.js`\n * - `app.config.json`\n * - `app.json`\n *\n * @param projectRoot the root folder containing all of your application code\n * @param options enforce criteria for a project config\n */\nexport function getConfig(projectRoot: string, options: GetConfigOptions = {}): ProjectConfig {\n  const paths = getConfigFilePaths(projectRoot);\n\n  const rawStaticConfig = paths.staticConfigPath ? getStaticConfig(paths.staticConfigPath) : null;\n  // For legacy reasons, always return an object.\n  const rootConfig = (rawStaticConfig || {}) as AppJSONConfig;\n  const staticConfig = reduceExpoObject(rawStaticConfig) || {};\n\n  // Can only change the package.json location if an app.json or app.config.json exists\n  const [packageJson, packageJsonPath] = getPackageJsonAndPath(projectRoot);\n\n  function fillAndReturnConfig(config: SplitConfigs, dynamicConfigObjectType: string | null) {\n    const configWithDefaultValues = {\n      ...ensureConfigHasDefaultValues({\n        projectRoot,\n        exp: config.expo,\n        pkg: packageJson,\n        skipSDKVersionRequirement: options.skipSDKVersionRequirement,\n        paths,\n        packageJsonPath,\n      }),\n      mods: config.mods,\n      dynamicConfigObjectType,\n      rootConfig,\n      dynamicConfigPath: paths.dynamicConfigPath,\n      staticConfigPath: paths.staticConfigPath,\n    };\n\n    if (options.isModdedConfig) {\n      // @ts-ignore: Add the mods back to the object.\n      configWithDefaultValues.exp.mods = config.mods ?? null;\n    }\n\n    // Apply static json plugins, should be done after _internal\n    configWithDefaultValues.exp = withConfigPlugins(\n      configWithDefaultValues.exp,\n      !!options.skipPlugins\n    );\n\n    if (!options.isModdedConfig) {\n      // @ts-ignore: Delete mods added by static plugins when they won't have a chance to be evaluated\n      delete configWithDefaultValues.exp.mods;\n    }\n\n    if (options.isPublicConfig) {\n      // TODD(EvanBacon): Drop plugins array after it's been resolved.\n\n      // Remove internal values with references to user's file paths from the public config.\n      delete configWithDefaultValues.exp._internal;\n\n      if (configWithDefaultValues.exp.hooks) {\n        delete configWithDefaultValues.exp.hooks;\n      }\n      if (configWithDefaultValues.exp.ios?.config) {\n        delete configWithDefaultValues.exp.ios.config;\n      }\n      if (configWithDefaultValues.exp.android?.config) {\n        delete configWithDefaultValues.exp.android.config;\n      }\n\n      delete configWithDefaultValues.exp.updates?.codeSigningCertificate;\n      delete configWithDefaultValues.exp.updates?.codeSigningMetadata;\n    }\n\n    return configWithDefaultValues;\n  }\n\n  // Fill in the static config\n  function getContextConfig(config: SplitConfigs) {\n    return ensureConfigHasDefaultValues({\n      projectRoot,\n      exp: config.expo,\n      pkg: packageJson,\n      skipSDKVersionRequirement: true,\n      paths,\n      packageJsonPath,\n    }).exp;\n  }\n\n  if (paths.dynamicConfigPath) {\n    // No app.config.json or app.json but app.config.js\n    const { exportedObjectType, config: rawDynamicConfig } = getDynamicConfig(\n      paths.dynamicConfigPath,\n      {\n        projectRoot,\n        staticConfigPath: paths.staticConfigPath,\n        packageJsonPath,\n        config: getContextConfig(staticConfig),\n      }\n    );\n    // Allow for the app.config.js to `export default null;`\n    // Use `dynamicConfigPath` to detect if a dynamic config exists.\n    const dynamicConfig = reduceExpoObject(rawDynamicConfig) || {};\n    return fillAndReturnConfig(dynamicConfig, exportedObjectType);\n  }\n\n  // No app.config.js but json or no config\n  return fillAndReturnConfig(staticConfig || {}, null);\n}\n\nexport function getPackageJson(projectRoot: string): PackageJSONConfig {\n  const [pkg] = getPackageJsonAndPath(projectRoot);\n  return pkg;\n}\n\nfunction getPackageJsonAndPath(projectRoot: string): [PackageJSONConfig, string] {\n  const packageJsonPath = getRootPackageJsonPath(projectRoot);\n  return [JsonFile.read(packageJsonPath), packageJsonPath];\n}\n\n/**\n * Get the static and dynamic config paths for a project. Also accounts for custom paths.\n *\n * @param projectRoot\n */\nexport function getConfigFilePaths(projectRoot: string): ConfigFilePaths {\n  return {\n    dynamicConfigPath: getDynamicConfigFilePath(projectRoot),\n    staticConfigPath: getStaticConfigFilePath(projectRoot),\n  };\n}\n\nfunction getDynamicConfigFilePath(projectRoot: string): string | null {\n  for (const fileName of ['app.config.ts', 'app.config.js']) {\n    const configPath = path.join(projectRoot, fileName);\n    if (fs.existsSync(configPath)) {\n      return configPath;\n    }\n  }\n  return null;\n}\n\nfunction getStaticConfigFilePath(projectRoot: string): string | null {\n  for (const fileName of ['app.config.json', 'app.json']) {\n    const configPath = path.join(projectRoot, fileName);\n    if (fs.existsSync(configPath)) {\n      return configPath;\n    }\n  }\n  return null;\n}\n\n/**\n * Attempt to modify an Expo project config.\n * This will only fully work if the project is using static configs only.\n * Otherwise 'warn' | 'fail' will return with a message about why the config couldn't be updated.\n * The potentially modified config object will be returned for testing purposes.\n *\n * @param projectRoot\n * @param modifications modifications to make to an existing config\n * @param readOptions options for reading the current config file\n * @param writeOptions If true, the static config file will not be rewritten\n */\nexport async function modifyConfigAsync(\n  projectRoot: string,\n  modifications: Partial<ExpoConfig>,\n  readOptions: GetConfigOptions = {},\n  writeOptions: WriteConfigOptions = {}\n): Promise<{\n  type: 'success' | 'warn' | 'fail';\n  message?: string;\n  config: AppJSONConfig | null;\n}> {\n  const config = getConfig(projectRoot, readOptions);\n  if (config.dynamicConfigPath) {\n    // We cannot automatically write to a dynamic config.\n    /* Currently we should just use the safest approach possible, informing the user that they'll need to manually modify their dynamic config.\n\n    if (config.staticConfigPath) {\n      // Both a dynamic and a static config exist.\n      if (config.dynamicConfigObjectType === 'function') {\n        // The dynamic config exports a function, this means it possibly extends the static config.\n      } else {\n        // Dynamic config ignores the static config, there isn't a reason to automatically write to it.\n        // Instead we should warn the user to add values to their dynamic config.\n      }\n    }\n    */\n    return {\n      type: 'warn',\n      message: `Cannot automatically write to dynamic config at: ${path.relative(\n        projectRoot,\n        config.dynamicConfigPath\n      )}`,\n      config: null,\n    };\n  } else if (config.staticConfigPath) {\n    // Static with no dynamic config, this means we can append to the config automatically.\n    let outputConfig: AppJSONConfig;\n    // If the config has an expo object (app.json) then append the options to that object.\n    if (config.rootConfig.expo) {\n      outputConfig = {\n        ...config.rootConfig,\n        expo: { ...config.rootConfig.expo, ...modifications },\n      };\n    } else {\n      // Otherwise (app.config.json) just add the config modification to the top most level.\n      outputConfig = { ...config.rootConfig, ...modifications };\n    }\n    if (!writeOptions.dryRun) {\n      await JsonFile.writeAsync(config.staticConfigPath, outputConfig, { json5: false });\n    }\n    return { type: 'success', config: outputConfig };\n  }\n\n  return { type: 'fail', message: 'No config exists', config: null };\n}\n\nfunction ensureConfigHasDefaultValues({\n  projectRoot,\n  exp,\n  pkg,\n  paths,\n  packageJsonPath,\n  skipSDKVersionRequirement = false,\n}: {\n  projectRoot: string;\n  exp: Partial<ExpoConfig> | null;\n  pkg: JSONObject;\n  skipSDKVersionRequirement?: boolean;\n  paths?: ConfigFilePaths;\n  packageJsonPath?: string;\n}): { exp: ExpoConfig; pkg: PackageJSONConfig } {\n  if (!exp) {\n    exp = {};\n  }\n  exp = withInternal(exp as any, {\n    projectRoot,\n    ...(paths ?? {}),\n    packageJsonPath,\n  });\n  // Defaults for package.json fields\n  const pkgName = typeof pkg.name === 'string' ? pkg.name : path.basename(projectRoot);\n  const pkgVersion = typeof pkg.version === 'string' ? pkg.version : '1.0.0';\n\n  const pkgWithDefaults = { ...pkg, name: pkgName, version: pkgVersion };\n\n  // Defaults for app.json/app.config.js fields\n  const name = exp.name ?? pkgName;\n  const slug = exp.slug ?? slugify(name.toLowerCase());\n  const version = exp.version ?? pkgVersion;\n  let description = exp.description;\n  if (!description && typeof pkg.description === 'string') {\n    description = pkg.description;\n  }\n\n  const expWithDefaults = { ...exp, name, slug, version, description };\n\n  let sdkVersion;\n  try {\n    sdkVersion = getExpoSDKVersion(projectRoot, expWithDefaults);\n  } catch (error) {\n    if (!skipSDKVersionRequirement) throw error;\n  }\n\n  let platforms = exp.platforms;\n  if (!platforms) {\n    platforms = getSupportedPlatforms(projectRoot);\n  }\n\n  return {\n    exp: { ...expWithDefaults, sdkVersion, platforms },\n    pkg: pkgWithDefaults,\n  };\n}\n\nconst DEFAULT_BUILD_PATH = `web-build`;\n\nexport function getWebOutputPath(config: { [key: string]: any } = {}): string {\n  if (process.env.WEBPACK_BUILD_OUTPUT_PATH) {\n    return process.env.WEBPACK_BUILD_OUTPUT_PATH;\n  }\n  const expo = config.expo || config || {};\n  return expo?.web?.build?.output || DEFAULT_BUILD_PATH;\n}\n\nexport function getNameFromConfig(exp: Record<string, any> = {}): {\n  appName?: string;\n  webName?: string;\n} {\n  // For RN CLI support\n  const appManifest = exp.expo || exp;\n  const { web = {} } = appManifest;\n\n  // rn-cli apps use a displayName value as well.\n  const appName = exp.displayName || appManifest.displayName || appManifest.name;\n  const webName = web.name || appName;\n\n  return {\n    appName,\n    webName,\n  };\n}\n\nexport function getDefaultTarget(\n  projectRoot: string,\n  exp?: Pick<ExpoConfig, 'sdkVersion'>\n): ProjectTarget {\n  exp ??= getConfig(projectRoot, { skipSDKVersionRequirement: true }).exp;\n\n  // before SDK 37, always default to managed to preserve previous behavior\n  if (exp.sdkVersion && exp.sdkVersion !== 'UNVERSIONED' && semver.lt(exp.sdkVersion, '37.0.0')) {\n    return 'managed';\n  }\n  return isBareWorkflowProject(projectRoot) ? 'bare' : 'managed';\n}\n\nfunction isBareWorkflowProject(projectRoot: string): boolean {\n  const [pkg] = getPackageJsonAndPath(projectRoot);\n\n  // TODO: Drop this\n  if (pkg.dependencies && pkg.dependencies.expokit) {\n    return false;\n  }\n\n  const xcodeprojFiles = globSync('ios/**/*.xcodeproj', {\n    absolute: true,\n    cwd: projectRoot,\n  });\n  if (xcodeprojFiles.length) {\n    return true;\n  }\n  const gradleFiles = globSync('android/**/*.gradle', {\n    absolute: true,\n    cwd: projectRoot,\n  });\n  if (gradleFiles.length) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Return a useful name describing the project config.\n * - dynamic: app.config.js\n * - static: app.json\n * - custom path app config relative to root folder\n * - both: app.config.js or app.json\n */\nexport function getProjectConfigDescription(projectRoot: string): string {\n  const paths = getConfigFilePaths(projectRoot);\n  return getProjectConfigDescriptionWithPaths(projectRoot, paths);\n}\n\n/**\n * Returns a string describing the configurations used for the given project root.\n * Will return null if no config is found.\n *\n * @param projectRoot\n * @param projectConfig\n */\nexport function getProjectConfigDescriptionWithPaths(\n  projectRoot: string,\n  projectConfig: ConfigFilePaths\n): string {\n  if (projectConfig.dynamicConfigPath) {\n    const relativeDynamicConfigPath = path.relative(projectRoot, projectConfig.dynamicConfigPath);\n    if (projectConfig.staticConfigPath) {\n      return `${relativeDynamicConfigPath} or ${path.relative(\n        projectRoot,\n        projectConfig.staticConfigPath\n      )}`;\n    }\n    return relativeDynamicConfigPath;\n  } else if (projectConfig.staticConfigPath) {\n    return path.relative(projectRoot, projectConfig.staticConfigPath);\n  }\n  // If a config doesn't exist, our tooling will generate a static app.json\n  return 'app.json';\n}\n\nexport * from './Config.types';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,aAAA;EAAA,MAAAN,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAI,YAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,SAAA;EAAA,MAAAR,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAM,QAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAaA,SAAAS,WAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,UAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,mBAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,kBAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAW,mBAAA;EAAA,MAAAX,IAAA,GAAAE,OAAA;EAAAS,kBAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAY,cAAA;EAAA,MAAAZ,IAAA,GAAAE,OAAA;EAAAU,aAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAa,oBAAA;EAAA,MAAAb,IAAA,GAAAE,OAAA;EAAAW,mBAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAobA,IAAAc,OAAA,GAAAZ,OAAA;AAAAa,MAAA,CAAAC,IAAA,CAAAF,OAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,OAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,OAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAA+B,SAAAjB,uBAAA0B,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAhb/B;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,gBAAgBA,CAACC,MAAY,EAAgB;EAAA,IAAAC,YAAA;EACpD,IAAI,CAACD,MAAM,EAAE,OAAOA,MAAM,KAAKE,SAAS,GAAG,IAAI,GAAGF,MAAM;EAExD,MAAM;IAAEG,IAAI;IAAE,GAAGC;EAAK,CAAC,IAAAH,YAAA,GAAGD,MAAM,CAACI,IAAI,cAAAH,YAAA,cAAAA,YAAA,GAAID,MAAM;EAE/C,OAAO;IACLI,IAAI;IACJD;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,qBAAqBA,CAACC,WAAmB,EAAc;EAC9D,MAAMC,SAAqB,GAAG,EAAE;EAChC,IAAIC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,cAAc,CAAC,EAAE;IACnDC,SAAS,CAACG,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC;EAClC;EACA,IAAIF,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,kBAAkB,CAAC,EAAE;IACvDC,SAAS,CAACG,IAAI,CAAC,KAAK,CAAC;EACvB;EACA,OAAOH,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,SAASA,CAACL,WAAmB,EAAEM,OAAyB,GAAG,CAAC,CAAC,EAAiB;EAC5F,MAAMC,KAAK,GAAGC,kBAAkB,CAACR,WAAW,CAAC;EAE7C,MAAMS,eAAe,GAAGF,KAAK,CAACG,gBAAgB,GAAG,IAAAC,4BAAe,EAACJ,KAAK,CAACG,gBAAgB,CAAC,GAAG,IAAI;EAC/F;EACA,MAAME,UAAU,GAAIH,eAAe,IAAI,CAAC,CAAmB;EAC3D,MAAMI,YAAY,GAAGpB,gBAAgB,CAACgB,eAAe,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAM,CAACK,WAAW,EAAEC,eAAe,CAAC,GAAGC,qBAAqB,CAAChB,WAAW,CAAC;EAEzE,SAASiB,mBAAmBA,CAACvB,MAAoB,EAAEwB,uBAAsC,EAAE;IACzF,MAAMC,uBAAuB,GAAG;MAC9B,GAAGC,4BAA4B,CAAC;QAC9BpB,WAAW;QACXqB,GAAG,EAAE3B,MAAM,CAACI,IAAI;QAChBwB,GAAG,EAAER,WAAW;QAChBS,yBAAyB,EAAEjB,OAAO,CAACiB,yBAAyB;QAC5DhB,KAAK;QACLQ;MACF,CAAC,CAAC;MACFlB,IAAI,EAAEH,MAAM,CAACG,IAAI;MACjBqB,uBAAuB;MACvBN,UAAU;MACVY,iBAAiB,EAAEjB,KAAK,CAACiB,iBAAiB;MAC1Cd,gBAAgB,EAAEH,KAAK,CAACG;IAC1B,CAAC;IAED,IAAIJ,OAAO,CAACmB,cAAc,EAAE;MAAA,IAAAC,YAAA;MAC1B;MACAP,uBAAuB,CAACE,GAAG,CAACxB,IAAI,IAAA6B,YAAA,GAAGhC,MAAM,CAACG,IAAI,cAAA6B,YAAA,cAAAA,YAAA,GAAI,IAAI;IACxD;;IAEA;IACAP,uBAAuB,CAACE,GAAG,GAAG,IAAAM,sCAAiB,EAC7CR,uBAAuB,CAACE,GAAG,EAC3B,CAAC,CAACf,OAAO,CAACsB,WAAW,CACtB;IAED,IAAI,CAACtB,OAAO,CAACmB,cAAc,EAAE;MAC3B;MACA,OAAON,uBAAuB,CAACE,GAAG,CAACxB,IAAI;IACzC;IAEA,IAAIS,OAAO,CAACuB,cAAc,EAAE;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC1B;;MAEA;MACA,OAAOd,uBAAuB,CAACE,GAAG,CAACa,SAAS;MAE5C,IAAIf,uBAAuB,CAACE,GAAG,CAACc,KAAK,EAAE;QACrC,OAAOhB,uBAAuB,CAACE,GAAG,CAACc,KAAK;MAC1C;MACA,KAAAL,qBAAA,GAAIX,uBAAuB,CAACE,GAAG,CAACe,GAAG,cAAAN,qBAAA,eAA/BA,qBAAA,CAAiCpC,MAAM,EAAE;QAC3C,OAAOyB,uBAAuB,CAACE,GAAG,CAACe,GAAG,CAAC1C,MAAM;MAC/C;MACA,KAAAqC,sBAAA,GAAIZ,uBAAuB,CAACE,GAAG,CAACgB,OAAO,cAAAN,sBAAA,eAAnCA,sBAAA,CAAqCrC,MAAM,EAAE;QAC/C,OAAOyB,uBAAuB,CAACE,GAAG,CAACgB,OAAO,CAAC3C,MAAM;MACnD;MAEA,CAAAsC,sBAAA,GAAOb,uBAAuB,CAACE,GAAG,CAACiB,OAAO,cAAAN,sBAAA,qBAA1C,OAAOA,sBAAA,CAAqCO,sBAAsB;MAClE,CAAAN,sBAAA,GAAOd,uBAAuB,CAACE,GAAG,CAACiB,OAAO,cAAAL,sBAAA,qBAA1C,OAAOA,sBAAA,CAAqCO,mBAAmB;IACjE;IAEA,OAAOrB,uBAAuB;EAChC;;EAEA;EACA,SAASsB,gBAAgBA,CAAC/C,MAAoB,EAAE;IAC9C,OAAO0B,4BAA4B,CAAC;MAClCpB,WAAW;MACXqB,GAAG,EAAE3B,MAAM,CAACI,IAAI;MAChBwB,GAAG,EAAER,WAAW;MAChBS,yBAAyB,EAAE,IAAI;MAC/BhB,KAAK;MACLQ;IACF,CAAC,CAAC,CAACM,GAAG;EACR;EAEA,IAAId,KAAK,CAACiB,iBAAiB,EAAE;IAC3B;IACA,MAAM;MAAEkB,kBAAkB;MAAEhD,MAAM,EAAEiD;IAAiB,CAAC,GAAG,IAAAC,6BAAgB,EACvErC,KAAK,CAACiB,iBAAiB,EACvB;MACExB,WAAW;MACXU,gBAAgB,EAAEH,KAAK,CAACG,gBAAgB;MACxCK,eAAe;MACfrB,MAAM,EAAE+C,gBAAgB,CAAC5B,YAAY;IACvC,CAAC,CACF;IACD;IACA;IACA,MAAMgC,aAAa,GAAGpD,gBAAgB,CAACkD,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC9D,OAAO1B,mBAAmB,CAAC4B,aAAa,EAAEH,kBAAkB,CAAC;EAC/D;;EAEA;EACA,OAAOzB,mBAAmB,CAACJ,YAAY,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;AACtD;AAEO,SAASiC,cAAcA,CAAC9C,WAAmB,EAAqB;EACrE,MAAM,CAACsB,GAAG,CAAC,GAAGN,qBAAqB,CAAChB,WAAW,CAAC;EAChD,OAAOsB,GAAG;AACZ;AAEA,SAASN,qBAAqBA,CAAChB,WAAmB,EAA+B;EAC/E,MAAMe,eAAe,GAAG,IAAAgC,4CAAsB,EAAC/C,WAAW,CAAC;EAC3D,OAAO,CAACgD,mBAAQ,CAACC,IAAI,CAAClC,eAAe,CAAC,EAAEA,eAAe,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASP,kBAAkBA,CAACR,WAAmB,EAAmB;EACvE,OAAO;IACLwB,iBAAiB,EAAE0B,wBAAwB,CAAClD,WAAW,CAAC;IACxDU,gBAAgB,EAAEyC,uBAAuB,CAACnD,WAAW;EACvD,CAAC;AACH;AAEA,SAASkD,wBAAwBA,CAAClD,WAAmB,EAAiB;EACpE,KAAK,MAAMoD,QAAQ,IAAI,CAAC,eAAe,EAAE,eAAe,CAAC,EAAE;IACzD,MAAMC,UAAU,GAAGC,eAAI,CAACC,IAAI,CAACvD,WAAW,EAAEoD,QAAQ,CAAC;IACnD,IAAII,aAAE,CAACC,UAAU,CAACJ,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU;IACnB;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASF,uBAAuBA,CAACnD,WAAmB,EAAiB;EACnE,KAAK,MAAMoD,QAAQ,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE;IACtD,MAAMC,UAAU,GAAGC,eAAI,CAACC,IAAI,CAACvD,WAAW,EAAEoD,QAAQ,CAAC;IACnD,IAAII,aAAE,CAACC,UAAU,CAACJ,UAAU,CAAC,EAAE;MAC7B,OAAOA,UAAU;IACnB;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeK,iBAAiBA,CACrC1D,WAAmB,EACnB2D,aAAkC,EAClCC,WAA6B,GAAG,CAAC,CAAC,EAClCC,YAAgC,GAAG,CAAC,CAAC,EAKpC;EACD,MAAMnE,MAAM,GAAGW,SAAS,CAACL,WAAW,EAAE4D,WAAW,CAAC;EAClD,IAAIlE,MAAM,CAAC8B,iBAAiB,EAAE;IAC5B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEI,OAAO;MACLsC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAG,oDAAmDT,eAAI,CAACU,QAAQ,CACxEhE,WAAW,EACXN,MAAM,CAAC8B,iBAAiB,CACxB,EAAC;MACH9B,MAAM,EAAE;IACV,CAAC;EACH,CAAC,MAAM,IAAIA,MAAM,CAACgB,gBAAgB,EAAE;IAClC;IACA,IAAIuD,YAA2B;IAC/B;IACA,IAAIvE,MAAM,CAACkB,UAAU,CAACd,IAAI,EAAE;MAC1BmE,YAAY,GAAG;QACb,GAAGvE,MAAM,CAACkB,UAAU;QACpBd,IAAI,EAAE;UAAE,GAAGJ,MAAM,CAACkB,UAAU,CAACd,IAAI;UAAE,GAAG6D;QAAc;MACtD,CAAC;IACH,CAAC,MAAM;MACL;MACAM,YAAY,GAAG;QAAE,GAAGvE,MAAM,CAACkB,UAAU;QAAE,GAAG+C;MAAc,CAAC;IAC3D;IACA,IAAI,CAACE,YAAY,CAACK,MAAM,EAAE;MACxB,MAAMlB,mBAAQ,CAACmB,UAAU,CAACzE,MAAM,CAACgB,gBAAgB,EAAEuD,YAAY,EAAE;QAAEG,KAAK,EAAE;MAAM,CAAC,CAAC;IACpF;IACA,OAAO;MAAEN,IAAI,EAAE,SAAS;MAAEpE,MAAM,EAAEuE;IAAa,CAAC;EAClD;EAEA,OAAO;IAAEH,IAAI,EAAE,MAAM;IAAEC,OAAO,EAAE,kBAAkB;IAAErE,MAAM,EAAE;EAAK,CAAC;AACpE;AAEA,SAAS0B,4BAA4BA,CAAC;EACpCpB,WAAW;EACXqB,GAAG;EACHC,GAAG;EACHf,KAAK;EACLQ,eAAe;EACfQ,yBAAyB,GAAG;AAQ9B,CAAC,EAA+C;EAAA,IAAA8C,SAAA,EAAAC,SAAA,EAAAC,YAAA;EAC9C,IAAI,CAAClD,GAAG,EAAE;IACRA,GAAG,GAAG,CAAC,CAAC;EACV;EACAA,GAAG,GAAG,IAAAmD,4BAAY,EAACnD,GAAG,EAAS;IAC7BrB,WAAW;IACX,IAAIO,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,CAAC,CAAC,CAAC;IAChBQ;EACF,CAAC,CAAC;EACF;EACA,MAAM0D,OAAO,GAAG,OAAOnD,GAAG,CAACoD,IAAI,KAAK,QAAQ,GAAGpD,GAAG,CAACoD,IAAI,GAAGpB,eAAI,CAACqB,QAAQ,CAAC3E,WAAW,CAAC;EACpF,MAAM4E,UAAU,GAAG,OAAOtD,GAAG,CAACuD,OAAO,KAAK,QAAQ,GAAGvD,GAAG,CAACuD,OAAO,GAAG,OAAO;EAE1E,MAAMC,eAAe,GAAG;IAAE,GAAGxD,GAAG;IAAEoD,IAAI,EAAED,OAAO;IAAEI,OAAO,EAAED;EAAW,CAAC;;EAEtE;EACA,MAAMF,IAAI,IAAAL,SAAA,GAAGhD,GAAG,CAACqD,IAAI,cAAAL,SAAA,cAAAA,SAAA,GAAII,OAAO;EAChC,MAAMM,IAAI,IAAAT,SAAA,GAAGjD,GAAG,CAAC0D,IAAI,cAAAT,SAAA,cAAAA,SAAA,GAAI,IAAAU,kBAAO,EAACN,IAAI,CAACO,WAAW,EAAE,CAAC;EACpD,MAAMJ,OAAO,IAAAN,YAAA,GAAGlD,GAAG,CAACwD,OAAO,cAAAN,YAAA,cAAAA,YAAA,GAAIK,UAAU;EACzC,IAAIM,WAAW,GAAG7D,GAAG,CAAC6D,WAAW;EACjC,IAAI,CAACA,WAAW,IAAI,OAAO5D,GAAG,CAAC4D,WAAW,KAAK,QAAQ,EAAE;IACvDA,WAAW,GAAG5D,GAAG,CAAC4D,WAAW;EAC/B;EAEA,MAAMC,eAAe,GAAG;IAAE,GAAG9D,GAAG;IAAEqD,IAAI;IAAEK,IAAI;IAAEF,OAAO;IAAEK;EAAY,CAAC;EAEpE,IAAIE,UAAU;EACd,IAAI;IACFA,UAAU,GAAG,IAAAC,sCAAiB,EAACrF,WAAW,EAAEmF,eAAe,CAAC;EAC9D,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAI,CAAC/D,yBAAyB,EAAE,MAAM+D,KAAK;EAC7C;EAEA,IAAIrF,SAAS,GAAGoB,GAAG,CAACpB,SAAS;EAC7B,IAAI,CAACA,SAAS,EAAE;IACdA,SAAS,GAAGF,qBAAqB,CAACC,WAAW,CAAC;EAChD;EAEA,OAAO;IACLqB,GAAG,EAAE;MAAE,GAAG8D,eAAe;MAAEC,UAAU;MAAEnF;IAAU,CAAC;IAClDqB,GAAG,EAAEwD;EACP,CAAC;AACH;AAEA,MAAMS,kBAAkB,GAAI,WAAU;AAE/B,SAASC,gBAAgBA,CAAC9F,MAA8B,GAAG,CAAC,CAAC,EAAU;EAAA,IAAA+F,SAAA,EAAAC,eAAA;EAC5E,IAAIC,OAAO,CAACC,GAAG,CAACC,yBAAyB,EAAE;IACzC,OAAOF,OAAO,CAACC,GAAG,CAACC,yBAAyB;EAC9C;EACA,MAAM/F,IAAI,GAAGJ,MAAM,CAACI,IAAI,IAAIJ,MAAM,IAAI,CAAC,CAAC;EACxC,OAAO,CAAAI,IAAI,aAAJA,IAAI,wBAAA2F,SAAA,GAAJ3F,IAAI,CAAEgG,GAAG,cAAAL,SAAA,wBAAAC,eAAA,GAATD,SAAA,CAAWM,KAAK,cAAAL,eAAA,uBAAhBA,eAAA,CAAkBM,MAAM,KAAIT,kBAAkB;AACvD;AAEO,SAASU,iBAAiBA,CAAC5E,GAAwB,GAAG,CAAC,CAAC,EAG7D;EACA;EACA,MAAM6E,WAAW,GAAG7E,GAAG,CAACvB,IAAI,IAAIuB,GAAG;EACnC,MAAM;IAAEyE,GAAG,GAAG,CAAC;EAAE,CAAC,GAAGI,WAAW;;EAEhC;EACA,MAAMC,OAAO,GAAG9E,GAAG,CAAC+E,WAAW,IAAIF,WAAW,CAACE,WAAW,IAAIF,WAAW,CAACxB,IAAI;EAC9E,MAAM2B,OAAO,GAAGP,GAAG,CAACpB,IAAI,IAAIyB,OAAO;EAEnC,OAAO;IACLA,OAAO;IACPE;EACF,CAAC;AACH;AAEO,SAASC,gBAAgBA,CAC9BtG,WAAmB,EACnBqB,GAAoC,EACrB;EAAA,IAAAkF,IAAA;EACf,CAAAA,IAAA,GAAAlF,GAAG,cAAAkF,IAAA,cAAAA,IAAA,GAAHlF,GAAG,GAAKhB,SAAS,CAACL,WAAW,EAAE;IAAEuB,yBAAyB,EAAE;EAAK,CAAC,CAAC,CAACF,GAAG;;EAEvE;EACA,IAAIA,GAAG,CAAC+D,UAAU,IAAI/D,GAAG,CAAC+D,UAAU,KAAK,aAAa,IAAIoB,iBAAM,CAACC,EAAE,CAACpF,GAAG,CAAC+D,UAAU,EAAE,QAAQ,CAAC,EAAE;IAC7F,OAAO,SAAS;EAClB;EACA,OAAOsB,qBAAqB,CAAC1G,WAAW,CAAC,GAAG,MAAM,GAAG,SAAS;AAChE;AAEA,SAAS0G,qBAAqBA,CAAC1G,WAAmB,EAAW;EAC3D,MAAM,CAACsB,GAAG,CAAC,GAAGN,qBAAqB,CAAChB,WAAW,CAAC;;EAEhD;EACA,IAAIsB,GAAG,CAACqF,YAAY,IAAIrF,GAAG,CAACqF,YAAY,CAACC,OAAO,EAAE;IAChD,OAAO,KAAK;EACd;EAEA,MAAMC,cAAc,GAAG,IAAAC,YAAQ,EAAC,oBAAoB,EAAE;IACpDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEhH;EACP,CAAC,CAAC;EACF,IAAI6G,cAAc,CAACI,MAAM,EAAE;IACzB,OAAO,IAAI;EACb;EACA,MAAMC,WAAW,GAAG,IAAAJ,YAAQ,EAAC,qBAAqB,EAAE;IAClDC,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAEhH;EACP,CAAC,CAAC;EACF,IAAIkH,WAAW,CAACD,MAAM,EAAE;IACtB,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,2BAA2BA,CAACnH,WAAmB,EAAU;EACvE,MAAMO,KAAK,GAAGC,kBAAkB,CAACR,WAAW,CAAC;EAC7C,OAAOoH,oCAAoC,CAACpH,WAAW,EAAEO,KAAK,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS6G,oCAAoCA,CAClDpH,WAAmB,EACnBqH,aAA8B,EACtB;EACR,IAAIA,aAAa,CAAC7F,iBAAiB,EAAE;IACnC,MAAM8F,yBAAyB,GAAGhE,eAAI,CAACU,QAAQ,CAAChE,WAAW,EAAEqH,aAAa,CAAC7F,iBAAiB,CAAC;IAC7F,IAAI6F,aAAa,CAAC3G,gBAAgB,EAAE;MAClC,OAAQ,GAAE4G,yBAA0B,OAAMhE,eAAI,CAACU,QAAQ,CACrDhE,WAAW,EACXqH,aAAa,CAAC3G,gBAAgB,CAC9B,EAAC;IACL;IACA,OAAO4G,yBAAyB;EAClC,CAAC,MAAM,IAAID,aAAa,CAAC3G,gBAAgB,EAAE;IACzC,OAAO4C,eAAI,CAACU,QAAQ,CAAChE,WAAW,EAAEqH,aAAa,CAAC3G,gBAAgB,CAAC;EACnE;EACA;EACA,OAAO,UAAU;AACnB"}