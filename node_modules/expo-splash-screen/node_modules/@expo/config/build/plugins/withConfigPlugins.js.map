{"version": 3, "file": "withConfigPlugins.js", "names": ["_configPlugins", "data", "require", "_Serialize", "withConfigPlugins", "config", "skip<PERSON>lug<PERSON>", "_config$plugins", "Array", "isArray", "plugins", "length", "with<PERSON><PERSON><PERSON>", "serializeAfterStaticPlugins", "exports"], "sources": ["../../src/plugins/withConfigPlugins.ts"], "sourcesContent": ["import { ConfigPlugin, withPlugins } from '@expo/config-plugins';\n\nimport { serializeAfterStaticPlugins } from '../Serialize';\n\n/**\n * Resolves static plugins array as config plugin functions.\n *\n * @param config\n * @param projectRoot\n */\nexport const withConfigPlugins: ConfigPlugin<boolean> = (config, skipPlugins) => {\n  // @ts-ignore: plugins not on config type yet -- TODO\n  if (!Array.isArray(config.plugins) || !config.plugins?.length) {\n    return config;\n  }\n  if (!skipPlugins) {\n    // Resolve and evaluate plugins\n    // @ts-ignore: TODO: add plugins to the config schema\n    config = withPlugins(config, config.plugins);\n  } else {\n    // Delete the plugins array in case someone added functions or other values which cannot be automatically serialized.\n    delete config.plugins;\n  }\n  // plugins aren't serialized by default, serialize the plugins after resolving them.\n  return serializeAfterStaticPlugins(config);\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMG,iBAAwC,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAK;EAAA,IAAAC,eAAA;EAC/E;EACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACK,OAAO,CAAC,IAAI,GAAAH,eAAA,GAACF,MAAM,CAACK,OAAO,cAAAH,eAAA,eAAdA,eAAA,CAAgBI,MAAM,GAAE;IAC7D,OAAON,MAAM;EACf;EACA,IAAI,CAACC,WAAW,EAAE;IAChB;IACA;IACAD,MAAM,GAAG,IAAAO,4BAAW,EAACP,MAAM,EAAEA,MAAM,CAACK,OAAO,CAAC;EAC9C,CAAC,MAAM;IACL;IACA,OAAOL,MAAM,CAACK,OAAO;EACvB;EACA;EACA,OAAO,IAAAG,wCAA2B,EAACR,MAAM,CAAC;AAC5C,CAAC;AAACS,OAAA,CAAAV,iBAAA,GAAAA,iBAAA"}