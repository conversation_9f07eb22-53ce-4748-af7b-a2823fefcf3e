{"version": 3, "file": "getUserState.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_getenv", "_os", "path", "_interopRequireWildcard", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "obj", "__esModule", "default", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "getExpoHomeDirectory", "home", "homedir", "process", "env", "__UNSAFE_EXPO_HOME_DIRECTORY", "boolish", "join", "getUserStatePath", "getUserState", "JsonFile", "jsonParseE<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantReadFileDefault"], "sources": ["../src/getUserState.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport { boolish } from 'getenv';\nimport { homedir } from 'os';\nimport * as path from 'path';\n\nexport type UserSettingsData = {\n  developmentCodeSigningId?: string;\n  appleId?: string;\n  accessToken?: string;\n  auth?: UserData | null;\n  ignoreBundledBinaries?: string[];\n  openDevToolsAtStartup?: boolean;\n  PATH?: string;\n  sendTo?: string;\n  uuid?: string;\n};\n\nexport type UserData = {\n  appleId?: string;\n  userId?: string;\n  username?: string;\n  currentConnection?: ConnectionType;\n  sessionSecret?: string;\n};\n\nexport type ConnectionType =\n  | 'Access-Token-Authentication'\n  | 'Username-Password-Authentication'\n  | 'facebook'\n  | 'google-oauth2'\n  | 'github';\n\n// The ~/.expo directory is used to store authentication sessions,\n// which are shared between EAS CLI and Expo CLI.\nexport function getExpoHomeDirectory() {\n  const home = homedir();\n\n  if (process.env.__UNSAFE_EXPO_HOME_DIRECTORY) {\n    return process.env.__UNSAFE_EXPO_HOME_DIRECTORY;\n  } else if (boolish('EXPO_STAGING', false)) {\n    return path.join(home, '.expo-staging');\n  } else if (boolish('EXPO_LOCAL', false)) {\n    return path.join(home, '.expo-local');\n  }\n  return path.join(home, '.expo');\n}\n\nexport function getUserStatePath() {\n  return path.join(getExpoHomeDirectory(), 'state.json');\n}\n\nexport function getUserState() {\n  return new JsonFile<UserSettingsData>(getUserStatePath(), {\n    jsonParseErrorDefault: {},\n    // This will ensure that an error isn't thrown if the file doesn't exist.\n    cantReadFileDefault: {},\n  });\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6B,SAAAO,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAF,wBAAAM,GAAA,EAAAJ,WAAA,SAAAA,WAAA,IAAAI,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAG,KAAA,GAAAR,wBAAA,CAAAC,WAAA,OAAAO,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAJ,GAAA,YAAAG,KAAA,CAAAE,GAAA,CAAAL,GAAA,SAAAM,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAX,GAAA,QAAAW,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAW,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAV,GAAA,EAAAW,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAX,GAAA,CAAAW,GAAA,SAAAL,MAAA,CAAAJ,OAAA,GAAAF,GAAA,MAAAG,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAhB,GAAA,EAAAM,MAAA,YAAAA,MAAA;AAAA,SAAAjB,uBAAAW,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AA6B7B;AACA;AACO,SAASiB,oBAAoBA,CAAA,EAAG;EACrC,MAAMC,IAAI,GAAG,IAAAC,aAAO,GAAE;EAEtB,IAAIC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;IAC5C,OAAOF,OAAO,CAACC,GAAG,CAACC,4BAA4B;EACjD,CAAC,MAAM,IAAI,IAAAC,iBAAO,EAAC,cAAc,EAAE,KAAK,CAAC,EAAE;IACzC,OAAO9B,IAAI,GAAC+B,IAAI,CAACN,IAAI,EAAE,eAAe,CAAC;EACzC,CAAC,MAAM,IAAI,IAAAK,iBAAO,EAAC,YAAY,EAAE,KAAK,CAAC,EAAE;IACvC,OAAO9B,IAAI,GAAC+B,IAAI,CAACN,IAAI,EAAE,aAAa,CAAC;EACvC;EACA,OAAOzB,IAAI,GAAC+B,IAAI,CAACN,IAAI,EAAE,OAAO,CAAC;AACjC;AAEO,SAASO,gBAAgBA,CAAA,EAAG;EACjC,OAAOhC,IAAI,GAAC+B,IAAI,CAACP,oBAAoB,EAAE,EAAE,YAAY,CAAC;AACxD;AAEO,SAASS,YAAYA,CAAA,EAAG;EAC7B,OAAO,KAAIC,mBAAQ,EAAmBF,gBAAgB,EAAE,EAAE;IACxDG,qBAAqB,EAAE,CAAC,CAAC;IACzB;IACAC,mBAAmB,EAAE,CAAC;EACxB,CAAC,CAAC;AACJ"}