{"version": 3, "file": "Serialize.js", "names": ["_Errors", "data", "require", "serializeAndEvaluate", "val", "includes", "Array", "isArray", "map", "output", "property", "hasOwnProperty", "ConfigError", "serializeSkippingMods", "serializeAndEvaluatePlugin", "_val$name", "name", "serializeAfterStaticPlugins"], "sources": ["../src/Serialize.ts"], "sourcesContent": ["import { ConfigError } from './Errors';\n\nexport function serializeAndEvaluate(val: any): any {\n  if (['undefined', 'string', 'boolean', 'number', 'bigint'].includes(typeof val)) {\n    return val;\n  } else if (typeof val === 'function') {\n    // TODO: Bacon: Should we support async methods?\n    return val();\n  } else if (Array.isArray(val)) {\n    return val.map(serializeAndEvaluate);\n  } else if (typeof val === 'object') {\n    const output: { [key: string]: any } = {};\n    for (const property in val) {\n      if (val.hasOwnProperty(property)) {\n        output[property] = serializeAndEvaluate(val[property]);\n      }\n    }\n    return output;\n  }\n  // symbol\n  throw new ConfigError(`Expo config doesn't support \\`Symbols\\`: ${val}`, 'INVALID_CONFIG');\n}\n\nexport function serializeSkippingMods(val: any): any {\n  if (typeof val === 'object' && !Array.isArray(val)) {\n    const output: { [key: string]: any } = {};\n    for (const property in val) {\n      if (val.hasOwnProperty(property)) {\n        if (property === 'mods' || property === 'plugins') {\n          // Don't serialize mods or plugins\n          output[property] = val[property];\n        } else {\n          output[property] = serializeAndEvaluate(val[property]);\n        }\n      }\n    }\n    return output;\n  }\n  return serializeAndEvaluate(val);\n}\n\nfunction serializeAndEvaluatePlugin(val: any): any {\n  if (['undefined', 'string', 'boolean', 'number', 'bigint'].includes(typeof val)) {\n    return val;\n  } else if (typeof val === 'function') {\n    return val.name ?? 'withAnonymous';\n  } else if (Array.isArray(val)) {\n    return val.map(serializeAndEvaluatePlugin);\n  } else if (typeof val === 'object') {\n    const output: { [key: string]: any } = {};\n    for (const property in val) {\n      if (val.hasOwnProperty(property)) {\n        output[property] = serializeAndEvaluatePlugin(val[property]);\n      }\n    }\n    return output;\n  }\n  // symbol\n  throw new ConfigError(`Expo config doesn't support \\`Symbols\\`: ${val}`, 'INVALID_CONFIG');\n}\n\nexport function serializeAfterStaticPlugins(val: any): any {\n  if (typeof val === 'object' && !Array.isArray(val)) {\n    const output: { [key: string]: any } = {};\n    for (const property in val) {\n      if (val.hasOwnProperty(property)) {\n        if (property === 'mods') {\n          // Don't serialize mods\n          output[property] = val[property];\n        } else if (property === 'plugins' && Array.isArray(val[property])) {\n          // Serialize the mods by removing any config plugins\n          output[property] = val[property].map(serializeAndEvaluatePlugin);\n        } else {\n          output[property] = serializeAndEvaluate(val[property]);\n        }\n      }\n    }\n    return output;\n  }\n  return serializeAndEvaluate(val);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,SAASE,oBAAoBA,CAACC,GAAQ,EAAO;EAClD,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAAC,OAAOD,GAAG,CAAC,EAAE;IAC/E,OAAOA,GAAG;EACZ,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IACpC;IACA,OAAOA,GAAG,EAAE;EACd,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;IAC7B,OAAOA,GAAG,CAACI,GAAG,CAACL,oBAAoB,CAAC;EACtC,CAAC,MAAM,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;IAClC,MAAMK,MAA8B,GAAG,CAAC,CAAC;IACzC,KAAK,MAAMC,QAAQ,IAAIN,GAAG,EAAE;MAC1B,IAAIA,GAAG,CAACO,cAAc,CAACD,QAAQ,CAAC,EAAE;QAChCD,MAAM,CAACC,QAAQ,CAAC,GAAGP,oBAAoB,CAACC,GAAG,CAACM,QAAQ,CAAC,CAAC;MACxD;IACF;IACA,OAAOD,MAAM;EACf;EACA;EACA,MAAM,KAAIG,qBAAW,EAAE,4CAA2CR,GAAI,EAAC,EAAE,gBAAgB,CAAC;AAC5F;AAEO,SAASS,qBAAqBA,CAACT,GAAQ,EAAO;EACnD,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;IAClD,MAAMK,MAA8B,GAAG,CAAC,CAAC;IACzC,KAAK,MAAMC,QAAQ,IAAIN,GAAG,EAAE;MAC1B,IAAIA,GAAG,CAACO,cAAc,CAACD,QAAQ,CAAC,EAAE;QAChC,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,SAAS,EAAE;UACjD;UACAD,MAAM,CAACC,QAAQ,CAAC,GAAGN,GAAG,CAACM,QAAQ,CAAC;QAClC,CAAC,MAAM;UACLD,MAAM,CAACC,QAAQ,CAAC,GAAGP,oBAAoB,CAACC,GAAG,CAACM,QAAQ,CAAC,CAAC;QACxD;MACF;IACF;IACA,OAAOD,MAAM;EACf;EACA,OAAON,oBAAoB,CAACC,GAAG,CAAC;AAClC;AAEA,SAASU,0BAA0BA,CAACV,GAAQ,EAAO;EACjD,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAAC,OAAOD,GAAG,CAAC,EAAE;IAC/E,OAAOA,GAAG;EACZ,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;IAAA,IAAAW,SAAA;IACpC,QAAAA,SAAA,GAAOX,GAAG,CAACY,IAAI,cAAAD,SAAA,cAAAA,SAAA,GAAI,eAAe;EACpC,CAAC,MAAM,IAAIT,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;IAC7B,OAAOA,GAAG,CAACI,GAAG,CAACM,0BAA0B,CAAC;EAC5C,CAAC,MAAM,IAAI,OAAOV,GAAG,KAAK,QAAQ,EAAE;IAClC,MAAMK,MAA8B,GAAG,CAAC,CAAC;IACzC,KAAK,MAAMC,QAAQ,IAAIN,GAAG,EAAE;MAC1B,IAAIA,GAAG,CAACO,cAAc,CAACD,QAAQ,CAAC,EAAE;QAChCD,MAAM,CAACC,QAAQ,CAAC,GAAGI,0BAA0B,CAACV,GAAG,CAACM,QAAQ,CAAC,CAAC;MAC9D;IACF;IACA,OAAOD,MAAM;EACf;EACA;EACA,MAAM,KAAIG,qBAAW,EAAE,4CAA2CR,GAAI,EAAC,EAAE,gBAAgB,CAAC;AAC5F;AAEO,SAASa,2BAA2BA,CAACb,GAAQ,EAAO;EACzD,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE;IAClD,MAAMK,MAA8B,GAAG,CAAC,CAAC;IACzC,KAAK,MAAMC,QAAQ,IAAIN,GAAG,EAAE;MAC1B,IAAIA,GAAG,CAACO,cAAc,CAACD,QAAQ,CAAC,EAAE;QAChC,IAAIA,QAAQ,KAAK,MAAM,EAAE;UACvB;UACAD,MAAM,CAACC,QAAQ,CAAC,GAAGN,GAAG,CAACM,QAAQ,CAAC;QAClC,CAAC,MAAM,IAAIA,QAAQ,KAAK,SAAS,IAAIJ,KAAK,CAACC,OAAO,CAACH,GAAG,CAACM,QAAQ,CAAC,CAAC,EAAE;UACjE;UACAD,MAAM,CAACC,QAAQ,CAAC,GAAGN,GAAG,CAACM,QAAQ,CAAC,CAACF,GAAG,CAACM,0BAA0B,CAAC;QAClE,CAAC,MAAM;UACLL,MAAM,CAACC,QAAQ,CAAC,GAAGP,oBAAoB,CAACC,GAAG,CAACM,QAAQ,CAAC,CAAC;QACxD;MACF;IACF;IACA,OAAOD,MAAM;EACf;EACA,OAAON,oBAAoB,CAACC,GAAG,CAAC;AAClC"}