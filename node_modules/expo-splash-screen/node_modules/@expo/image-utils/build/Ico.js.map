{"version": 3, "file": "Ico.js", "sourceRoot": "", "sources": ["../src/Ico.ts"], "names": [], "mappings": ";;;;;;AAAA,sGAAsG;AACtG,0DAAiC;AASjC,MAAM,SAAS,GAAG;IAChB,aAAa,EAAE,EAAE;IACjB,UAAU,EAAE,EAAE;IACd,UAAU,EAAE,CAAC;IACb,SAAS,EAAE,CAAC;CACb,CAAC;AAEF,SAAS,YAAY,CAAC,MAAc;IAClC,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAElD,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAEhC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,eAAe,CAAC,IAAS,EAAE,MAAc;IAChD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IACrD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;IACrD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IAClD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACrD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IAEzB,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC5B,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC7B,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3B,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC7B,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9B,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAEjC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,IAAS,EAAE,WAAmB;IAClD,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAElD,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAC9C,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACnC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC5B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACvC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IACtC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAC3C,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3B,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3B,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC5B,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAE5B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAAC,IAAY,EAAE,KAAa,EAAE,MAAc,EAAE,GAAW;IACzE,MAAM,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC;IACzB,MAAM,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;IAC3B,MAAM,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;IACxB,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAEzC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE;QACzC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,IAAI,GAAG,EAAE;YACxC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;YAEpB,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAElC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;YAEtB,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC1B,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;SAC/B;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAW;IACnC,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;IAErB,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IACxB,IAAI,MAAM,GAAG,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;IAE1E,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,MAAM,GAAG,GAAG,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACzC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACd,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC;QAClB,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;KAClD;IAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;QACtD,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACtB,GAAG,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;KACnC;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACjC,CAAC;AAEM,KAAK,UAAU,aAAa,CAAC,OAAiB;IACnD,MAAM,IAAI,GAAU,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,mBAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC;AAHD,sCAGC", "sourcesContent": ["// Inspired by https://github.com/kevva/to-ico but reuses existing packages to keep bundle size small.\nimport parsePng from 'parse-png';\n\ntype PNG = {\n  data: Buffer;\n  width: number;\n  height: number;\n  bpp: number;\n};\n\nconst constants = {\n  directorySize: 16,\n  bitmapSize: 40,\n  headerSize: 6,\n  colorMode: 0,\n};\n\nfunction createHeader(header: number): Buffer {\n  const buffer = Buffer.alloc(constants.headerSize);\n\n  buffer.writeUInt16LE(0, 0);\n  buffer.writeUInt16LE(1, 2);\n  buffer.writeUInt16LE(header, 4);\n\n  return buffer;\n}\n\nfunction createDirectory(data: PNG, offset: number): Buffer {\n  const buffer = Buffer.alloc(constants.directorySize);\n  const size = data.data.length + constants.bitmapSize;\n  const width = data.width === 256 ? 0 : data.width;\n  const height = data.height === 256 ? 0 : data.height;\n  const bpp = data.bpp * 8;\n\n  buffer.writeUInt8(width, 0);\n  buffer.writeUInt8(height, 1);\n  buffer.writeUInt8(0, 2);\n  buffer.writeUInt8(0, 3);\n  buffer.writeUInt16LE(1, 4);\n  buffer.writeUInt16LE(bpp, 6);\n  buffer.writeUInt32LE(size, 8);\n  buffer.writeUInt32LE(offset, 12);\n\n  return buffer;\n}\n\nfunction createBitmap(data: PNG, compression: number): Buffer {\n  const buffer = Buffer.alloc(constants.bitmapSize);\n\n  buffer.writeUInt32LE(constants.bitmapSize, 0);\n  buffer.writeInt32LE(data.width, 4);\n  buffer.writeInt32LE(data.height * 2, 8);\n  buffer.writeUInt16LE(1, 12);\n  buffer.writeUInt16LE(data.bpp * 8, 14);\n  buffer.writeUInt32LE(compression, 16);\n  buffer.writeUInt32LE(data.data.length, 20);\n  buffer.writeInt32LE(0, 24);\n  buffer.writeInt32LE(0, 28);\n  buffer.writeUInt32LE(0, 32);\n  buffer.writeUInt32LE(0, 36);\n\n  return buffer;\n}\n\nfunction createDIB(data: Buffer, width: number, height: number, bpp: number): Buffer {\n  const cols = width * bpp;\n  const rows = height * cols;\n  const end = rows - cols;\n  const buffer = Buffer.alloc(data.length);\n\n  for (let row = 0; row < rows; row += cols) {\n    for (let col = 0; col < cols; col += bpp) {\n      let pos = row + col;\n\n      const r = data.readUInt8(pos);\n      const g = data.readUInt8(pos + 1);\n      const b = data.readUInt8(pos + 2);\n      const a = data.readUInt8(pos + 3);\n\n      pos = end - row + col;\n\n      buffer.writeUInt8(b, pos);\n      buffer.writeUInt8(g, pos + 1);\n      buffer.writeUInt8(r, pos + 2);\n      buffer.writeUInt8(a, pos + 3);\n    }\n  }\n\n  return buffer;\n}\n\nfunction generateFromPNGs(pngs: PNG[]): Buffer {\n  const header = createHeader(pngs.length);\n  const arr = [header];\n\n  let len = header.length;\n  let offset = constants.headerSize + constants.directorySize * pngs.length;\n\n  for (const png of pngs) {\n    const dir = createDirectory(png, offset);\n    arr.push(dir);\n    len += dir.length;\n    offset += png.data.length + constants.bitmapSize;\n  }\n\n  for (const png of pngs) {\n    const header = createBitmap(png, constants.colorMode);\n    const dib = createDIB(png.data, png.width, png.height, png.bpp);\n    arr.push(header, dib);\n    len += header.length + dib.length;\n  }\n\n  return Buffer.concat(arr, len);\n}\n\nexport async function generateAsync(buffers: Buffer[]): Promise<Buffer> {\n  const pngs: PNG[] = await Promise.all(buffers.map(x => parsePng(x)));\n  return generateFromPNGs(pngs);\n}\n"]}