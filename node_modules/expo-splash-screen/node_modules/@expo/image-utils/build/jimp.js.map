{"version": 3, "file": "jimp.js", "sourceRoot": "", "sources": ["../src/jimp.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAA0B;AAC1B,aAAa;AACb,gEAAgC;AAChC,2CAA6B;AAetB,KAAK,UAAU,iBAAiB,CAAC,MAAc,EAAE,KAAe;IACrE,OAAO,OAAO,CAAC,GAAG,CAChB,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,IAAI,EAAC,EAAE;QACrB,wDAAwD;QACxD,wDAAwD;QACxD,MAAM,SAAS,GAAG,MAAM,sBAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QAEjC,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AAXD,8CAWC;AAED,SAAgB,aAAa,CAAC,MAAe;IAC3C,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAO,MAAM,CAAC;IAEjD,MAAM,KAAK,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,WAAW,EAAE,CAAC;IACpC,QAAQ,KAAK,EAAE;QACb,KAAK,KAAK,CAAC;QACX,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,SAAS,KAAK,EAAE,CAAC;QAC1B,KAAK,KAAK;YACR,OAAO,YAAY,CAAC;KACvB;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAbD,sCAaC;AAEM,KAAK,UAAU,SAAS,CAC7B,OAA0B,EAC1B,WAAkC,EAAE;IAEpC,IAAI,QAAQ,CAAC,MAAM,EAAE;QACnB,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,OAAO,EAAE;YACX,IAAI,KAAW,CAAC;YAChB,IAAI,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE;gBAClC,KAAK,GAAG,MAAM,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aACxC;iBAAM,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;gBAC1C,KAAK,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;aACzC;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,SAAS,8BAA8B,CAAC,CAAC;aACrF;YACD,aAAa;YACb,OAAO,SAAS,CAAC,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC;SACnD;KACF;IAED,MAAM,KAAK,GAAG,MAAM,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACrD,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACnF,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAEnD,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;QACtC,IAAI,MAAM,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvC,MAAM,kBAAE,CAAC,SAAS,CAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAC/D,SAAS,CACV,CAAC;SACH;aAAM;YACL,MAAM,kBAAE,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;SAC/C;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAnCD,8BAmCC;AAEM,KAAK,UAAU,aAAa,CAAC,IAAY;IAC9C,IAAI;QACF,OAAO,CAAC,MAAM,kBAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;KAC5C;IAAC,MAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAND,sCAMC;AAED,SAAgB,WAAW,CAAC,IAAU;IACpC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAEnE,MAAM,MAAM,GAAG;QACb,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;KAC1B,CAAC;IAEF,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAC3B,IAAI,CAAC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,IAAI,CAAC,MAAM,CAAC,MAAM,EAClB,CAAC,CAAS,EAAE,CAAS,EAAE,GAAW,EAAE,EAAE;YACpC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAE9E,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE;gBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aAC/B;iBAAM,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG,EAAE;gBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;aACnD;YACD,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AA1BD,kCA0BC;AAEM,KAAK,UAAU,iBAAiB,CAAC,KAA6B;IACnE,oCAAoC;IACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,YAAY,MAAM;QAAE,OAAO,MAAM,sBAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAExF,OAAO,KAAK,CAAC;AACf,CAAC;AALD,8CAKC;AAEM,KAAK,UAAU,MAAM,CAC1B,EAAE,KAAK,EAAE,OAAO,GAAG,GAAG,EAAqB,EAC3C,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAoC;IAE9E,IAAI,YAAY,GAAG,MAAM,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAElD,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;QACpB,MAAM,GAAG,sBAAI,CAAC,IAAI,CAAC;KACpB;SAAM,IAAI,CAAC,KAAK,IAAI,MAAM,EAAE;QAC3B,KAAK,GAAG,sBAAI,CAAC,IAAI,CAAC;KACnB;SAAM,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;QAC5B,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC;QAClC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;KACrC;IAED,MAAM,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;IAC/C,MAAM,WAAW,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;IAChE,IAAI,GAAG,KAAK,OAAO,EAAE;QACnB,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;KAChE;SAAM,IAAI,GAAG,KAAK,SAAS,EAAE;QAC5B,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;KAClE;SAAM;QACL,MAAM,IAAI,KAAK,CACb,oBAAoB,GAAG,8DAA8D,CACtF,CAAC;KACH;IACD,IAAI,UAAU,EAAE;QACd,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,IAAI,sBAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC/E,IAAI,EAAE,sBAAI,CAAC,sBAAsB;YACjC,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,CAAC;SACf,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACjD,CAAC;AAnCD,wBAmCC;AAED,KAAK,UAAU,OAAO,CACpB,EAAE,KAAK,EAAE,OAAO,GAAG,GAAG,EAAqB,EAC3C,EAAE,UAAU,EAAqC;IAEjD,MAAM,YAAY,GAAG,MAAM,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,WAAW,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;IAChE,OAAO,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,sBAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;AACtF,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,QAAmB;IAC1C,IAAI,CAAC,QAAQ;QAAE,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;IAEhD,QAAQ,QAAQ,EAAE;QAChB,KAAK,QAAQ,CAAC;QACd,KAAK,QAAQ;YACX,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,uBAAuB,CAAC;QACnE,KAAK,OAAO,CAAC;QACb,KAAK,KAAK;YACR,OAAO,sBAAI,CAAC,kBAAkB,GAAG,sBAAI,CAAC,uBAAuB,CAAC;QAChE,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO;YACV,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,sBAAsB,CAAC;QAClE,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ;YACX,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,uBAAuB,CAAC;QACnE,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,qBAAqB,CAAC;QACjE,KAAK,WAAW,CAAC;QACjB,KAAK,WAAW;YACd,OAAO,sBAAI,CAAC,kBAAkB,GAAG,sBAAI,CAAC,sBAAsB,CAAC;QAC/D,KAAK,WAAW,CAAC;QACjB,KAAK,cAAc;YACjB,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,sBAAsB,CAAC;QAClE,KAAK,WAAW,CAAC;QACjB,KAAK,aAAa;YAChB,OAAO,sBAAI,CAAC,qBAAqB,GAAG,sBAAI,CAAC,qBAAqB,CAAC;QACjE,KAAK,WAAW,CAAC;QACjB,KAAK,UAAU;YACb,OAAO,sBAAI,CAAC,kBAAkB,GAAG,sBAAI,CAAC,qBAAqB,CAAC;QAC9D,KAAK,SAAS,CAAC;QACf,KAAK,WAAW;YACd,MAAM,IAAI,KAAK,CAAC,cAAc,QAAQ,oBAAoB,CAAC,CAAC;QAC9D;YACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,GAAG,CAAC,CAAC;KACtD;AACH,CAAC", "sourcesContent": ["import fs from 'fs-extra';\n// @ts-ignore\nimport <PERSON><PERSON> from 'jimp-compact';\nimport * as path from 'path';\n\nimport {\n  FlattenOptions,\n  Position,\n  ResizeOptions,\n  SharpCommandOptions,\n  SharpGlobalOptions,\n} from './sharp.types';\n\ntype JimpGlobalOptions = Omit<SharpGlobalOptions, 'input'> & {\n  input: string | Buffer | Jimp;\n  originalInput: string;\n};\n\nexport async function resizeBufferAsync(buffer: Buffer, sizes: number[]): Promise<Buffer[]> {\n  return Promise.all(\n    sizes.map(async size => {\n      // Parse the buffer each time to prevent mutable copies.\n      // Parse the buffer each time to prevent mutable copies.\n      const jimpImage = await Jimp.read(buffer);\n      const mime = jimpImage.getMIME();\n\n      return jimpImage.resize(size, size).getBufferAsync(mime);\n    })\n  );\n}\n\nexport function convertFormat(format?: string): string | undefined {\n  if (typeof format === 'undefined') return format;\n\n  const input = format?.toLowerCase();\n  switch (input) {\n    case 'png':\n    case 'webp':\n    case 'jpeg':\n      return `image/${input}`;\n    case 'jpg':\n      return `image/jpeg`;\n  }\n  return undefined;\n}\n\nexport async function jimpAsync(\n  options: JimpGlobalOptions,\n  commands: SharpCommandOptions[] = []\n): Promise<Buffer> {\n  if (commands.length) {\n    const command = commands.shift();\n    if (command) {\n      let input: Jimp;\n      if (command.operation === 'resize') {\n        input = await resize(options, command);\n      } else if (command.operation === 'flatten') {\n        input = await flatten(options, command);\n      } else {\n        throw new Error(`The operation: '${command.operation}' is not supported with Jimp`);\n      }\n      // @ts-ignore\n      return jimpAsync({ ...options, input }, commands);\n    }\n  }\n\n  const image = await getJimpImageAsync(options.input);\n  const mime = typeof options.format === 'string' ? options.format : image.getMIME();\n  const imgBuffer = await image.getBufferAsync(mime);\n\n  if (typeof options.output === 'string') {\n    if (await isFolderAsync(options.output)) {\n      await fs.writeFile(\n        path.join(options.output, path.basename(options.originalInput)),\n        imgBuffer\n      );\n    } else {\n      await fs.writeFile(options.output, imgBuffer);\n    }\n  }\n  return imgBuffer;\n}\n\nexport async function isFolderAsync(path: string): Promise<boolean> {\n  try {\n    return (await fs.stat(path)).isDirectory();\n  } catch {\n    return false;\n  }\n}\n\nexport function circleAsync(jimp: Jimp): Promise<Jimp> {\n  const radius = Math.min(jimp.bitmap.width, jimp.bitmap.height) / 2;\n\n  const center = {\n    x: jimp.bitmap.width / 2,\n    y: jimp.bitmap.height / 2,\n  };\n\n  return new Promise(resolve => {\n    jimp.scanQuiet(\n      0,\n      0,\n      jimp.bitmap.width,\n      jimp.bitmap.height,\n      (x: number, y: number, idx: number) => {\n        const curR = Math.sqrt(Math.pow(x - center.x, 2) + Math.pow(y - center.y, 2));\n\n        if (radius - curR <= 0.0) {\n          jimp.bitmap.data[idx + 3] = 0;\n        } else if (radius - curR < 1.0) {\n          jimp.bitmap.data[idx + 3] = 255 * (radius - curR);\n        }\n        resolve(jimp);\n      }\n    );\n  });\n}\n\nexport async function getJimpImageAsync(input: string | Buffer | Jimp): Promise<Jimp> {\n  // @ts-ignore: Jimp types are broken\n  if (typeof input === 'string' || input instanceof Buffer) return await Jimp.read(input);\n\n  return input;\n}\n\nexport async function resize(\n  { input, quality = 100 }: JimpGlobalOptions,\n  { background, position, fit, width, height }: Omit<ResizeOptions, 'operation'>\n): Promise<Jimp> {\n  let initialImage = await getJimpImageAsync(input);\n\n  if (width && !height) {\n    height = Jimp.AUTO;\n  } else if (!width && height) {\n    width = Jimp.AUTO;\n  } else if (!width && !height) {\n    width = initialImage.bitmap.width;\n    height = initialImage.bitmap.height;\n  }\n\n  const jimpPosition = convertPosition(position);\n  const jimpQuality = typeof quality !== 'number' ? 100 : quality;\n  if (fit === 'cover') {\n    initialImage = initialImage.cover(width, height, jimpPosition);\n  } else if (fit === 'contain') {\n    initialImage = initialImage.contain(width, height, jimpPosition);\n  } else {\n    throw new Error(\n      `Unsupported fit: ${fit}. Please choose either 'cover', or 'contain' when using Jimp`\n    );\n  }\n  if (background) {\n    initialImage = initialImage.composite(new Jimp(width, height, background), 0, 0, {\n      mode: Jimp.BLEND_DESTINATION_OVER,\n      opacitySource: 1,\n      opacityDest: 1,\n    });\n  }\n\n  return await initialImage.quality(jimpQuality);\n}\n\nasync function flatten(\n  { input, quality = 100 }: JimpGlobalOptions,\n  { background }: Omit<FlattenOptions, 'operation'>\n): Promise<Jimp> {\n  const initialImage = await getJimpImageAsync(input);\n  const jimpQuality = typeof quality !== 'number' ? 100 : quality;\n  return initialImage.quality(jimpQuality).background(Jimp.cssColorToHex(background));\n}\n\n/**\n * Convert sharp position to Jimp position.\n *\n * @param position\n */\nfunction convertPosition(position?: Position): number {\n  if (!position) return convertPosition('center');\n\n  switch (position) {\n    case 'center':\n    case 'centre':\n      return Jimp.VERTICAL_ALIGN_MIDDLE | Jimp.HORIZONTAL_ALIGN_CENTER;\n    case 'north':\n    case 'top':\n      return Jimp.VERTICAL_ALIGN_TOP | Jimp.HORIZONTAL_ALIGN_CENTER;\n    case 'east':\n    case 'right':\n      return Jimp.VERTICAL_ALIGN_MIDDLE | Jimp.HORIZONTAL_ALIGN_RIGHT;\n    case 'south':\n    case 'bottom':\n      return Jimp.VERTICAL_ALIGN_BOTTOM | Jimp.HORIZONTAL_ALIGN_CENTER;\n    case 'west':\n    case 'left':\n      return Jimp.VERTICAL_ALIGN_MIDDLE | Jimp.HORIZONTAL_ALIGN_LEFT;\n    case 'northeast':\n    case 'right top':\n      return Jimp.VERTICAL_ALIGN_TOP | Jimp.HORIZONTAL_ALIGN_RIGHT;\n    case 'southeast':\n    case 'right bottom':\n      return Jimp.VERTICAL_ALIGN_BOTTOM | Jimp.HORIZONTAL_ALIGN_RIGHT;\n    case 'southwest':\n    case 'left bottom':\n      return Jimp.VERTICAL_ALIGN_BOTTOM | Jimp.HORIZONTAL_ALIGN_LEFT;\n    case 'northwest':\n    case 'left top':\n      return Jimp.VERTICAL_ALIGN_TOP | Jimp.HORIZONTAL_ALIGN_LEFT;\n    case 'entropy':\n    case 'attention':\n      throw new Error(`Position: '${position}' is not supported`);\n    default:\n      throw new Error(`Unknown position: '${position}'`);\n  }\n}\n"]}