{"version": 3, "file": "props.js", "sourceRoot": "", "sources": ["../../src/utils/props.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAU7C,OAAO,aAAa,MAAM,0BAA0B,CAAC;AAErD,2EAA2E;AAC3E,MAAM,CAAC,MAAM,gBAAgB,GAKzB;IACF,IAAI,EAAE,aAAa,CAAC,IAAI;IACxB,SAAS,EAAE,aAAa,CAAC,SAAS;IAClC,SAAS,EAAE,aAAa,CAAC,SAAS;IAClC,YAAY,EAAE,aAAa,CAAC,YAAY;CACzC,CAAC;AAEF,MAAM,UAAU,kBAAkB,CAAC,KAAmB;IACpD,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACvC,OAAO,EAAE,CAAC;KACX;IAED,MAAM,WAAW,GAAsB,EAAE,CAAC;IAE1C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAChD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE;YACtD,WAAW,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;SACjD;aAAM;YACL,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAC1B;KACF;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,KAAmB;IACnD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAE3C,IAAI,QAAQ,CAAC,gBAAgB,EAAE;QAC7B,QAAQ,CAAC,qBAAqB,GAAG,IAAI,CAAC;KACvC;IAED,IAAI,QAAQ,CAAC,eAAe,EAAE;QAC5B,QAAQ,CAAC,mBAAmB,GAAG,IAAI,CAAC;KACrC;IAED,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;QAC7B,OAAO,QAAQ,CAAC,KAAK,CAAC;QACtB,OAAO,QAAQ,CAAC,aAAa,CAAC;KAC/B;IAED,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACzB,OAAO,QAAQ,CAAC,MAAM,CAAC;KACxB;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nimport {\n  CameraNativeProps,\n  CameraType,\n  FlashMode,\n  AutoFocus,\n  WhiteBalance,\n  CameraProps,\n} from '../Camera.types';\nimport CameraManager from '../ExponentCameraManager';\n\n// Values under keys from this object will be transformed to native options\nexport const ConversionTables: {\n  type: Record<keyof typeof CameraType, CameraNativeProps['type']>;\n  flashMode: Record<keyof typeof FlashMode, CameraNativeProps['flashMode']>;\n  autoFocus: Record<keyof typeof AutoFocus, CameraNativeProps['autoFocus']>;\n  whiteBalance: Record<keyof typeof WhiteBalance, CameraNativeProps['whiteBalance']>;\n} = {\n  type: CameraManager.Type,\n  flashMode: CameraManager.FlashMode,\n  autoFocus: CameraManager.AutoFocus,\n  whiteBalance: CameraManager.WhiteBalance,\n};\n\nexport function convertNativeProps(props?: CameraProps): CameraNativeProps {\n  if (!props || typeof props !== 'object') {\n    return {};\n  }\n\n  const nativeProps: CameraNativeProps = {};\n\n  for (const [key, value] of Object.entries(props)) {\n    if (typeof value === 'string' && ConversionTables[key]) {\n      nativeProps[key] = ConversionTables[key][value];\n    } else {\n      nativeProps[key] = value;\n    }\n  }\n\n  return nativeProps;\n}\n\nexport function ensureNativeProps(props?: CameraProps): CameraNativeProps {\n  const newProps = convertNativeProps(props);\n\n  if (newProps.onBarCodeScanned) {\n    newProps.barCodeScannerEnabled = true;\n  }\n\n  if (newProps.onFacesDetected) {\n    newProps.faceDetectorEnabled = true;\n  }\n\n  if (Platform.OS !== 'android') {\n    delete newProps.ratio;\n    delete newProps.useCamera2Api;\n  }\n\n  if (Platform.OS !== 'web') {\n    delete newProps.poster;\n  }\n\n  return newProps;\n}\n"]}