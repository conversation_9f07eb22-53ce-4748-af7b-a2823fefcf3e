{"version": 3, "file": "WebConstants.js", "sourceRoot": "", "sources": ["../src/WebConstants.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAEvD,qFAAqF;AACrF,MAAM,CAAC,MAAM,mBAAmB,GAAG;IACjC,WAAW,EAAE,IAAI,GAAG,IAAI;IACxB,WAAW,EAAE,IAAI,GAAG,IAAI;IACxB,UAAU,EAAE,IAAI,GAAG,GAAG;IACtB,SAAS,EAAE,GAAG,GAAG,GAAG;IACpB,SAAS,EAAE,GAAG,GAAG,GAAG;CACrB,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAE7D,MAAM,CAAC,MAAM,eAAe,GAAG;IAC7B,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,YAAY;IAC7B,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,WAAW;CAC7B,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA2B;IACxD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,MAAM;IAC1B,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,aAAa;CACjC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC,IAAI,EAAE,UAAU,CAAC,KAAK;IACtB,WAAW,EAAE,UAAU,CAAC,IAAI;CAC7B,CAAC", "sourcesContent": ["import { CameraType, ImageType } from './Camera.types';\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints/aspectRatio\nexport const VIDEO_ASPECT_RATIOS = {\n  '3840x2160': 3840 / 2160,\n  '1920x1080': 1920 / 1080,\n  '1280x720': 1280 / 720,\n  '640x480': 640 / 480,\n  '352x288': 352 / 288,\n};\n\nexport const PictureSizes = Object.keys(VIDEO_ASPECT_RATIOS);\n\nexport const ImageTypeFormat = {\n  [ImageType.jpg]: 'image/jpeg',\n  [ImageType.png]: 'image/png',\n};\n\nexport const MinimumConstraints: MediaStreamConstraints = {\n  audio: false,\n  video: true,\n};\n\nexport const CameraTypeToFacingMode = {\n  [CameraType.front]: 'user',\n  [CameraType.back]: 'environment',\n};\n\nexport const FacingModeToCameraType = {\n  user: CameraType.front,\n  environment: CameraType.back,\n};\n"]}