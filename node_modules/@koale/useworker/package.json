{"name": "@koale/useworker", "version": "4.1.2", "description": "useWorker() - Web worker using React hook", "author": "alewin", "license": "MIT", "repository": "alewin/useworker", "keywords": ["react", "react-hooks", "web worker", "useWorker", "background"], "exports": {".": {"import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "dependencies": {"dequal": "^2.0.3"}, "devDependencies": {"@types/react": "^18.3.12", "@vitejs/plugin-react": "^4.3.3", "react": "^18.3.1", "vite": "^5.4.12", "vite-plugin-dts": "^4.3.0"}, "peerDependencies": {"react": ">=16.8.0 <20.0.0"}, "homepage": "https://github.com/alewin/useworker", "bugs": {"url": "https://github.com/alewin/useworker/issues"}, "scripts": {"build": "vite build", "dev": "vite"}}