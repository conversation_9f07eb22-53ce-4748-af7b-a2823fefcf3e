{
  "compilerOptions": {
    "target": "es5",
    "moduleResolution": "node",
    "module": "ES2015",
    "declaration": true,
    "sourceMap": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "experimentalDecorators": false,
    "allowJs": false,
    "isolatedModules": false,
    /* Strictness Checks */
    "alwaysStrict": true,
    "noImplicitAny": true,
    "noUnusedLocals": true,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "noFallthroughCasesInSwitch": true,
    "strictPropertyInitialization": true,
    /* Debugging Options */
    "traceResolution": false,
    "listEmittedFiles": false,
    "listFiles": false,
    "pretty": true,
    "lib": ["es2019", "dom"],
    "outDir": "./dist",
    "baseUrl": "."
  },
  "compileOnSave": false,
  "include": ["src/**/*"],
  "exclude": ["node_modules/**"]
}
