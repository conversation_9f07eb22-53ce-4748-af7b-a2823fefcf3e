import a from "react";
var b = Object.prototype.hasOwnProperty;
function O(e, r, n) {
  for (n of e.keys())
    if (h(n, r)) return n;
}
function h(e, r) {
  var n, t, s;
  if (e === r) return !0;
  if (e && r && (n = e.constructor) === r.constructor) {
    if (n === Date) return e.getTime() === r.getTime();
    if (n === RegExp) return e.toString() === r.toString();
    if (n === Array) {
      if ((t = e.length) === r.length)
        for (; t-- && h(e[t], r[t]); ) ;
      return t === -1;
    }
    if (n === Set) {
      if (e.size !== r.size)
        return !1;
      for (t of e)
        if (s = t, s && typeof s == "object" && (s = O(r, s), !s) || !r.has(s)) return !1;
      return !0;
    }
    if (n === Map) {
      if (e.size !== r.size)
        return !1;
      for (t of e)
        if (s = t[0], s && typeof s == "object" && (s = O(r, s), !s) || !h(t[1], r.get(s)))
          return !1;
      return !0;
    }
    if (n === ArrayBuffer)
      e = new Uint8Array(e), r = new Uint8Array(r);
    else if (n === DataView) {
      if ((t = e.byteLength) === r.byteLength)
        for (; t-- && e.getInt8(t) === r.getInt8(t); ) ;
      return t === -1;
    }
    if (ArrayBuffer.isView(e)) {
      if ((t = e.byteLength) === r.byteLength)
        for (; t-- && e[t] === r[t]; ) ;
      return t === -1;
    }
    if (!n || typeof e == "object") {
      t = 0;
      for (n in e)
        if (b.call(e, n) && ++t && !b.call(r, n) || !(n in r) || !h(e[n], r[n])) return !1;
      return Object.keys(r).length === t;
    }
  }
  return e !== e && r !== r;
}
const j = (e, r) => {
  const n = a.useRef(r);
  return h(n.current, r) || (n.current = r), a.useCallback(e, n.current);
};
class v extends Error {
  constructor() {
    super("Aborted"), this.name = "AbortError";
  }
}
const A = (e) => (r) => {
  const [n] = r.data;
  return Promise.resolve(e.fn(...n)).then((t) => {
    const s = (u) => "ArrayBuffer" in self && u instanceof ArrayBuffer || "MessagePort" in self && u instanceof MessagePort || "ImageBitmap" in self && u instanceof ImageBitmap || "OffscreenCanvas" in self && u instanceof OffscreenCanvas, E = e.transferable === "auto" && s(t) ? [t] : [];
    postMessage(["SUCCESS", t], E);
  }).catch((t) => {
    postMessage(["ERROR", t]);
  });
}, W = (e) => e.length === 0 ? "" : `importScripts(${e.map((n) => `'${n}'`).toString()})`, G = (e, r, n) => {
  const t = `
    ${W(r)};
    onmessage=(${A})({
      fn: (${e}),
      transferable: '${n}'
    })
  `, s = new Blob([t], { type: "text/javascript" });
  return URL.createObjectURL(s);
};
var o = /* @__PURE__ */ ((e) => (e.PENDING = "PENDING", e.SUCCESS = "SUCCESS", e.RUNNING = "RUNNING", e.ERROR = "ERROR", e.TIMEOUT_EXPIRED = "TIMEOUT_EXPIRED", e.KILLED = "KILLED", e))(o || {});
const U = "resolve", I = "reject", m = {
  timeout: void 0,
  remoteDependencies: [],
  autoTerminate: !0,
  transferable: "auto"
  /* AUTO */
}, x = (e, r = m) => {
  const [n, t] = a.useState(
    o.PENDING
  ), s = a.useRef(), E = a.useRef(!1), u = a.useRef({}), y = a.useRef(), l = a.useCallback(() => {
    var i, c, g;
    (i = s.current) != null && i._url && (u.current && ((g = (c = u.current)[I]) == null || g.call(c, new v())), s.current.terminate(), URL.revokeObjectURL(s.current._url), u.current = {}, s.current = void 0, E.current = !1, window.clearTimeout(y.current));
  }, []), C = a.useCallback(
    (i) => {
      (r.autoTerminate != null ? r.autoTerminate : m.autoTerminate) && l(), t(i);
    },
    [r.autoTerminate, l, t]
  ), d = j(() => {
    const {
      remoteDependencies: i = m.remoteDependencies,
      timeout: c = m.timeout,
      transferable: g = m.transferable
    } = r, p = G(e, i, g), w = new Worker(p);
    return w._url = p, w.onmessage = (k) => {
      var N, S, L, P;
      const [f, R] = k.data;
      switch (f) {
        case o.SUCCESS:
          (S = (N = u.current)[U]) == null || S.call(N, R), C(o.SUCCESS);
          break;
        default:
          (P = (L = u.current)[I]) == null || P.call(L, R), C(o.ERROR);
          break;
      }
    }, w.onerror = (k) => {
      var f, R;
      (R = (f = u.current)[I]) == null || R.call(f, k), C(o.ERROR);
    }, c && (y.current = window.setTimeout(() => {
      l(), t(o.TIMEOUT_EXPIRED);
    }, c)), w;
  }, [e, r, l]), D = a.useCallback(
    (...i) => {
      const { transferable: c = m.transferable } = r;
      return new Promise((g, p) => {
        var k;
        u.current = {
          [U]: g,
          [I]: p
        };
        const w = c === "auto" ? i.filter(
          (f) => "ArrayBuffer" in window && f instanceof ArrayBuffer || "MessagePort" in window && f instanceof MessagePort || "ImageBitmap" in window && f instanceof ImageBitmap || "OffscreenCanvas" in window && f instanceof OffscreenCanvas
        ) : [];
        (k = s.current) == null || k.postMessage([[...i]], w), t(o.RUNNING);
      });
    },
    [t]
  ), M = a.useCallback(
    (...i) => {
      const c = r.autoTerminate != null ? r.autoTerminate : m.autoTerminate;
      return E.current ? (console.error(
        "[useWorker] You can only run one instance of the worker at a time, if you want to run more than one in parallel, create another instance with the hook useWorker(). Read more: https://github.com/alewin/useWorker"
      ), Promise.reject()) : ((c || !s.current) && (s.current = d()), D(...i));
    },
    [r.autoTerminate, d, D]
  ), T = a.useCallback(() => {
    l(), t(o.KILLED);
  }, [l, t]), B = {
    status: n,
    kill: T
  };
  return a.useEffect(() => {
    E.current = n === o.RUNNING;
  }, [n]), a.useEffect(
    () => () => {
      l();
    },
    [l]
  ), [M, B];
};
export {
  v as AbortError,
  o as WORKER_STATUS,
  x as useWorker
};
//# sourceMappingURL=index.mjs.map
