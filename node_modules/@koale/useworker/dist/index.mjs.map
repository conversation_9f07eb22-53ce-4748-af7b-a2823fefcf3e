{"version": 3, "file": "index.mjs", "sources": ["../../../node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs", "../src/hook/useDeepCallback.ts", "../src/lib/abortError.ts", "../src/lib/jobRunner.ts", "../src/lib/remoteDepsParser.ts", "../src/lib/createWorkerBlobUrl.ts", "../src/lib/status.ts", "../src/useWorker.ts"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "import { dequal } from 'dequal'\nimport React, { DependencyList } from 'react'\n\nexport const useDeepCallback = <T extends (...args: any[]) => any>(\n  callback: T,\n  dependencies: DependencyList,\n) => {\n  const prevDependencies = React.useRef<DependencyList>(dependencies)\n  const areDeepsEqual = dequal(prevDependencies.current, dependencies)\n  if (!areDeepsEqual) {\n    prevDependencies.current = dependencies\n  }\n\n  return React.useCallback(callback, prevDependencies.current)\n}\n", "export class AbortError extends Error {\n  constructor() {\n    super('Aborted')\n    this.name = 'AbortError'\n  }\n}\n", "import { TRANSFERABLE_TYPE } from 'src/useWorker'\n\ninterface JOB_RUNNER_OPTIONS {\n  fn: Function\n  transferable: TRANSFERABLE_TYPE\n}\n\n/**\n * This function accepts as a parameter a function \"userFunc\"\n * And as a result returns an anonymous function.\n * This anonymous function, accepts as arguments,\n * the parameters to pass to the function \"useArgs\" and returns a Promise\n * This function can be used as a wrapper, only inside a Worker\n * because it depends by \"postMessage\".\n *\n * @param {Function} userFunc {Function} fn the function to run with web worker\n *\n * @returns {Function} returns a function that accepts the parameters\n * to be passed to the \"userFunc\" function\n */\nconst jobRunner =\n  (options: JOB_RUNNER_OPTIONS): Function =>\n  (e: MessageEvent) => {\n    const [userFuncArgs] = e.data as [any[]]\n    return Promise.resolve(options.fn(...userFuncArgs))\n      .then((result) => {\n        const isTransferable = (val: any) =>\n          ('ArrayBuffer' in self && val instanceof ArrayBuffer) ||\n          ('MessagePort' in self && val instanceof MessagePort) ||\n          ('ImageBitmap' in self && val instanceof ImageBitmap) ||\n          ('OffscreenCanvas' in self && val instanceof OffscreenCanvas)\n        const transferList: any[] =\n          options.transferable === 'auto' && isTransferable(result)\n            ? [result]\n            : []\n        // @ts-ignore\n        postMessage(['SUCCESS', result], transferList)\n      })\n      .catch((error) => {\n        // @ts-ignore\n        postMessage(['ERROR', error])\n      })\n  }\n\nexport default jobRunner\n", "/**\n *\n * Concatenates the remote dependencies into a comma separated string.\n * this string will then be passed as an argument to the \"importScripts\" function\n *\n * @param {Array.<String>}} deps array of string\n * @returns {String} a string composed by the concatenation of the array\n * elements \"deps\" and \"importScripts\".\n *\n * @example\n * remoteDepsParser(['http://js.com/1.js', 'http://js.com/2.js']) // importScripts('http://js.com/1.js', 'http://js.com/2.js')\n */\nconst remoteDepsParser = (deps: string[]) => {\n  if (deps.length === 0) return ''\n\n  const depsString = deps.map((dep) => `'${dep}'`).toString()\n  return `importScripts(${depsString})`\n}\n\nexport default remoteDepsParser\n", "// import isoworker from 'isoworker'\nimport { TRANSFERABLE_TYPE } from '../useWorker'\nimport jobRunner from './jobRunner'\nimport remoteDepsParser from './remoteDepsParser'\n\n/**\n * Converts the \"fn\" function into the syntax needed to be executed within a web worker\n *\n * @param {Function} fn the function to run with web worker\n * @param {Array.<String>} deps array of strings, imported into the worker through \"importScripts\"\n *\n * @returns {String} a blob url, containing the code of \"fn\" as a string\n *\n * @example\n * createWorkerBlobUrl((a,b) => a+b, [])\n * // return \"onmessage=return Promise.resolve((a,b) => a + b)\n * .then(postMessage(['SUCCESS', result]))\n * .catch(postMessage(['ERROR', error])\"\n */\nconst createWorkerBlobUrl = (\n  fn: Function,\n  deps: string[],\n  transferable: TRANSFERABLE_TYPE /* localDeps: () => unknown[], */,\n) => {\n  // const [context] = isoworker.createContext(localDeps)\n  const blobCode = `\n    ${remoteDepsParser(deps)};\n    onmessage=(${jobRunner})({\n      fn: (${fn}),\n      transferable: '${transferable}'\n    })\n  `\n  const blob = new Blob([blobCode], { type: 'text/javascript' })\n  const url = URL.createObjectURL(blob)\n  return url\n}\n\nexport default createWorkerBlobUrl\n", "export enum WORKER_STATUS {\n  PENDING = 'PENDING',\n  SUCCESS = 'SUCCESS',\n  RUNNING = 'RUNNING',\n  ERROR = 'ERROR',\n  TIMEOUT_EXPIRED = 'TIMEOUT_EXPIRED',\n  KILLED = 'KILLED',\n}\n\nexport default WORKER_STATUS\n", "import React from 'react'\nimport { useDeep<PERSON>allback } from './hook/useDeepCallback'\nimport { AbortError } from './lib/abortError'\nimport createWorkerBlobUrl from './lib/createWorkerBlobUrl'\nimport WORKER_STATUS from './lib/status'\n\ntype WorkerController = {\n  status: WORKER_STATUS\n  kill: Function\n}\n\nexport enum TRANSFERABLE_TYPE {\n  AUTO = 'auto',\n  NONE = 'none',\n}\n\ntype Options = {\n  timeout?: number\n  remoteDependencies?: string[]\n  autoTerminate?: boolean\n  transferable?: TRANSFERABLE_TYPE\n}\n\nconst PROMISE_RESOLVE = 'resolve'\nconst PROMISE_REJECT = 'reject'\nconst DEFAULT_OPTIONS: Options = {\n  timeout: undefined,\n  remoteDependencies: [],\n  autoTerminate: true,\n  transferable: TRANSFERABLE_TYPE.AUTO,\n}\n\n/**\n *\n * @param {Function} fn the function to run with web worker\n * @param {Object} options useWorker option params\n */\nexport const useWorker = <T extends (...fnArgs: any[]) => any>(\n  fn: T,\n  options: Options = DEFAULT_OPTIONS,\n) => {\n  const [workerStatus, setWorkerStatus] = React.useState<WORKER_STATUS>(\n    WORKER_STATUS.PENDING,\n  )\n  const worker = React.useRef<Worker & { _url?: string }>()\n  const isRunning = React.useRef(false)\n  const promise = React.useRef<{\n    [PROMISE_REJECT]?: (result: ReturnType<T> | ErrorEvent | AbortError) => void\n    [PROMISE_RESOLVE]?: (result: ReturnType<T>) => void\n  }>({})\n  const timeoutId = React.useRef<number>()\n\n  const killWorker = React.useCallback(() => {\n    if (worker.current?._url) {\n      if (promise.current) {\n        promise.current[PROMISE_REJECT]?.(new AbortError())\n      }\n\n      worker.current.terminate()\n      URL.revokeObjectURL(worker.current._url)\n      promise.current = {}\n      worker.current = undefined\n      isRunning.current = false\n      window.clearTimeout(timeoutId.current)\n    }\n  }, [])\n\n  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>\n  const onWorkerEnd = React.useCallback(\n    (status: WORKER_STATUS) => {\n      const terminate =\n        options.autoTerminate != null\n          ? options.autoTerminate\n          : DEFAULT_OPTIONS.autoTerminate\n\n      if (terminate) {\n        killWorker()\n      }\n      setWorkerStatus(status)\n    },\n    [options.autoTerminate, killWorker, setWorkerStatus],\n  )\n\n  const generateWorker = useDeepCallback(() => {\n    const {\n      remoteDependencies = DEFAULT_OPTIONS.remoteDependencies,\n      timeout = DEFAULT_OPTIONS.timeout,\n      transferable = DEFAULT_OPTIONS.transferable,\n    } = options\n\n    const blobUrl = createWorkerBlobUrl(fn, remoteDependencies!, transferable!)\n    const newWorker: Worker & { _url?: string } = new Worker(blobUrl)\n    newWorker._url = blobUrl\n\n    newWorker.onmessage = (e: MessageEvent) => {\n      const [status, result] = e.data as [WORKER_STATUS, ReturnType<T>]\n\n      switch (status) {\n        case WORKER_STATUS.SUCCESS:\n          promise.current[PROMISE_RESOLVE]?.(result)\n          onWorkerEnd(WORKER_STATUS.SUCCESS)\n          break\n        default:\n          promise.current[PROMISE_REJECT]?.(result)\n          onWorkerEnd(WORKER_STATUS.ERROR)\n          break\n      }\n    }\n\n    newWorker.onerror = (e: ErrorEvent) => {\n      promise.current[PROMISE_REJECT]?.(e)\n      onWorkerEnd(WORKER_STATUS.ERROR)\n    }\n\n    if (timeout) {\n      timeoutId.current = window.setTimeout(() => {\n        killWorker()\n        setWorkerStatus(WORKER_STATUS.TIMEOUT_EXPIRED)\n      }, timeout)\n    }\n    return newWorker\n  }, [fn, options, killWorker])\n\n  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>\n  const callWorker = React.useCallback(\n    (...workerArgs: Parameters<T>) => {\n      const { transferable = DEFAULT_OPTIONS.transferable } = options\n      return new Promise<ReturnType<T>>((resolve, reject) => {\n        promise.current = {\n          [PROMISE_RESOLVE]: resolve,\n          [PROMISE_REJECT]: reject,\n        }\n        const transferList: any[] =\n          transferable === TRANSFERABLE_TYPE.AUTO\n            ? workerArgs.filter(\n                (val: any) =>\n                  ('ArrayBuffer' in window && val instanceof ArrayBuffer) ||\n                  ('MessagePort' in window && val instanceof MessagePort) ||\n                  ('ImageBitmap' in window && val instanceof ImageBitmap) ||\n                  ('OffscreenCanvas' in window &&\n                    val instanceof OffscreenCanvas),\n              )\n            : []\n\n        worker.current?.postMessage([[...workerArgs]], transferList)\n\n        setWorkerStatus(WORKER_STATUS.RUNNING)\n      })\n    },\n    [setWorkerStatus],\n  )\n\n  const workerHook = React.useCallback(\n    (...fnArgs: Parameters<T>) => {\n      const terminate =\n        options.autoTerminate != null\n          ? options.autoTerminate\n          : DEFAULT_OPTIONS.autoTerminate\n\n      if (isRunning.current) {\n        console.error(\n          '[useWorker] You can only run one instance of the worker at a time, if you want to run more than one in parallel, create another instance with the hook useWorker(). Read more: https://github.com/alewin/useWorker',\n        )\n        return Promise.reject()\n      }\n      if (terminate || !worker.current) {\n        worker.current = generateWorker()\n      }\n\n      return callWorker(...fnArgs)\n    },\n    [options.autoTerminate, generateWorker, callWorker],\n  )\n\n  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>\n  const killWorkerController = React.useCallback(() => {\n    killWorker()\n    setWorkerStatus(WORKER_STATUS.KILLED)\n  }, [killWorker, setWorkerStatus])\n\n  const workerController = {\n    status: workerStatus,\n    kill: killWorkerController,\n  }\n\n  React.useEffect(() => {\n    isRunning.current = workerStatus === WORKER_STATUS.RUNNING\n  }, [workerStatus])\n\n  React.useEffect(\n    () => () => {\n      killWorker()\n    },\n    [killWorker],\n  )\n\n  return [workerHook, workerController] as [typeof workerHook, WorkerController]\n}\n"], "names": ["has", "find", "iter", "tar", "key", "dequal", "foo", "bar", "ctor", "len", "tmp", "useDeepCallback", "callback", "dependencies", "prevDependencies", "React", "AbortError", "<PERSON><PERSON><PERSON><PERSON>", "options", "e", "userFuncArgs", "result", "isTransferable", "val", "transferList", "error", "remoteDepsParser", "deps", "dep", "createWorkerBlobUrl", "fn", "transferable", "blobCode", "blob", "WORKER_STATUS", "PROMISE_RESOLVE", "PROMISE_REJECT", "DEFAULT_OPTIONS", "useWorker", "workerStatus", "setWorkerStatus", "worker", "isRunning", "promise", "timeoutId", "killWorker", "_a", "_c", "_b", "onWorkerEnd", "status", "generateWorker", "remoteDependencies", "timeout", "blobUrl", "newWorker", "_d", "callWorker", "workerArgs", "resolve", "reject", "worker<PERSON><PERSON>", "fnArgs", "terminate", "killWorkerController", "workerController"], "mappings": ";AAAA,IAAIA,IAAM,OAAO,UAAU;AAE3B,SAASC,EAAKC,GAAMC,GAAKC,GAAK;AAC7B,OAAKA,KAAOF,EAAK;AAChB,QAAIG,EAAOD,GAAKD,CAAG,EAAG,QAAOC;AAE/B;AAEO,SAASC,EAAOC,GAAKC,GAAK;AAChC,MAAIC,GAAMC,GAAKC;AACf,MAAIJ,MAAQC,EAAK,QAAO;AAExB,MAAID,KAAOC,MAAQC,IAAKF,EAAI,iBAAiBC,EAAI,aAAa;AAC7D,QAAIC,MAAS,KAAM,QAAOF,EAAI,QAAS,MAAKC,EAAI,QAAS;AACzD,QAAIC,MAAS,OAAQ,QAAOF,EAAI,SAAU,MAAKC,EAAI,SAAU;AAE7D,QAAIC,MAAS,OAAO;AACnB,WAAKC,IAAIH,EAAI,YAAYC,EAAI;AAC5B,eAAOE,OAASJ,EAAOC,EAAIG,CAAG,GAAGF,EAAIE,CAAG,CAAC,IAAE;AAE5C,aAAOA,MAAQ;AAAA,IAClB;AAEE,QAAID,MAAS,KAAK;AACjB,UAAIF,EAAI,SAASC,EAAI;AACpB,eAAO;AAER,WAAKE,KAAOH;AAMX,YALAI,IAAMD,GACFC,KAAO,OAAOA,KAAQ,aACzBA,IAAMT,EAAKM,GAAKG,CAAG,GACf,CAACA,MAEF,CAACH,EAAI,IAAIG,CAAG,EAAG,QAAO;AAE3B,aAAO;AAAA,IACV;AAEE,QAAIF,MAAS,KAAK;AACjB,UAAIF,EAAI,SAASC,EAAI;AACpB,eAAO;AAER,WAAKE,KAAOH;AAMX,YALAI,IAAMD,EAAI,CAAC,GACPC,KAAO,OAAOA,KAAQ,aACzBA,IAAMT,EAAKM,GAAKG,CAAG,GACf,CAACA,MAEF,CAACL,EAAOI,EAAI,CAAC,GAAGF,EAAI,IAAIG,CAAG,CAAC;AAC/B,iBAAO;AAGT,aAAO;AAAA,IACV;AAEE,QAAIF,MAAS;AACZ,MAAAF,IAAM,IAAI,WAAWA,CAAG,GACxBC,IAAM,IAAI,WAAWA,CAAG;AAAA,aACdC,MAAS,UAAU;AAC7B,WAAKC,IAAIH,EAAI,gBAAgBC,EAAI;AAChC,eAAOE,OAASH,EAAI,QAAQG,CAAG,MAAMF,EAAI,QAAQE,CAAG,IAAE;AAEvD,aAAOA,MAAQ;AAAA,IAClB;AAEE,QAAI,YAAY,OAAOH,CAAG,GAAG;AAC5B,WAAKG,IAAIH,EAAI,gBAAgBC,EAAI;AAChC,eAAOE,OAASH,EAAIG,CAAG,MAAMF,EAAIE,CAAG,IAAE;AAEvC,aAAOA,MAAQ;AAAA,IAClB;AAEE,QAAI,CAACD,KAAQ,OAAOF,KAAQ,UAAU;AACrC,MAAAG,IAAM;AACN,WAAKD,KAAQF;AAEZ,YADIN,EAAI,KAAKM,GAAKE,CAAI,KAAK,EAAEC,KAAO,CAACT,EAAI,KAAKO,GAAKC,CAAI,KACnD,EAAEA,KAAQD,MAAQ,CAACF,EAAOC,EAAIE,CAAI,GAAGD,EAAIC,CAAI,CAAC,EAAG,QAAO;AAE7D,aAAO,OAAO,KAAKD,CAAG,EAAE,WAAWE;AAAA,IACtC;AAAA,EACA;AAEC,SAAOH,MAAQA,KAAOC,MAAQA;AAC/B;AChFa,MAAAI,IAAkB,CAC7BC,GACAC,MACG;AACG,QAAAC,IAAmBC,EAAM,OAAuBF,CAAY;AAElE,SADsBR,EAAOS,EAAiB,SAASD,CAAY,MAEjEC,EAAiB,UAAUD,IAGtBE,EAAM,YAAYH,GAAUE,EAAiB,OAAO;AAC7D;ACdO,MAAME,UAAmB,MAAM;AAAA,EACpC,cAAc;AACZ,UAAM,SAAS,GACf,KAAK,OAAO;AAAA,EAAA;AAEhB;ACeA,MAAMC,IACJ,CAACC,MACD,CAACC,MAAoB;AACb,QAAA,CAACC,CAAY,IAAID,EAAE;AAClB,SAAA,QAAQ,QAAQD,EAAQ,GAAG,GAAGE,CAAY,CAAC,EAC/C,KAAK,CAACC,MAAW;AAChB,UAAMC,IAAiB,CAACC,MACrB,iBAAiB,QAAQA,aAAe,eACxC,iBAAiB,QAAQA,aAAe,eACxC,iBAAiB,QAAQA,aAAe,eACxC,qBAAqB,QAAQA,aAAe,iBACzCC,IACJN,EAAQ,iBAAiB,UAAUI,EAAeD,CAAM,IACpD,CAACA,CAAM,IACP,CAAC;AAEP,gBAAY,CAAC,WAAWA,CAAM,GAAGG,CAAY;AAAA,EAAA,CAC9C,EACA,MAAM,CAACC,MAAU;AAEJ,gBAAA,CAAC,SAASA,CAAK,CAAC;AAAA,EAAA,CAC7B;AACL,GC9BIC,IAAmB,CAACC,MACpBA,EAAK,WAAW,IAAU,KAGvB,iBADYA,EAAK,IAAI,CAACC,MAAQ,IAAIA,CAAG,GAAG,EAAE,SAAS,CACxB,KCG9BC,IAAsB,CAC1BC,GACAH,GACAI,MACG;AAEH,QAAMC,IAAW;AAAA,MACbN,EAAiBC,CAAI,CAAC;AAAA,iBACXV,CAAS;AAAA,aACba,CAAE;AAAA,uBACQC,CAAY;AAAA;AAAA,KAG3BE,IAAO,IAAI,KAAK,CAACD,CAAQ,GAAG,EAAE,MAAM,mBAAmB;AAEtD,SADK,IAAI,gBAAgBC,CAAI;AAEtC;ACnCY,IAAAC,sBAAAA,OACVA,EAAA,UAAU,WACVA,EAAA,UAAU,WACVA,EAAA,UAAU,WACVA,EAAA,QAAQ,SACRA,EAAA,kBAAkB,mBAClBA,EAAA,SAAS,UANCA,IAAAA,KAAA,CAAA,CAAA;ACuBZ,MAAMC,IAAkB,WAClBC,IAAiB,UACjBC,IAA2B;AAAA,EAC/B,SAAS;AAAA,EACT,oBAAoB,CAAC;AAAA,EACrB,eAAe;AAAA,EACf,cAAc;AAAA;AAChB,GAOaC,IAAY,CACvBR,GACAZ,IAAmBmB,MAChB;AACH,QAAM,CAACE,GAAcC,CAAe,IAAIzB,EAAM;AAAA,IAC5CmB,EAAc;AAAA,EAChB,GACMO,IAAS1B,EAAM,OAAmC,GAClD2B,IAAY3B,EAAM,OAAO,EAAK,GAC9B4B,IAAU5B,EAAM,OAGnB,EAAE,GACC6B,IAAY7B,EAAM,OAAe,GAEjC8B,IAAa9B,EAAM,YAAY,MAAM;;AACrC,KAAA+B,IAAAL,EAAO,YAAP,QAAAK,EAAgB,SACdH,EAAQ,aACVI,KAAAC,IAAAL,EAAQ,SAAQP,OAAhB,QAAAW,EAAA,KAAAC,GAAkC,IAAIhC,OAGxCyB,EAAO,QAAQ,UAAU,GACrB,IAAA,gBAAgBA,EAAO,QAAQ,IAAI,GACvCE,EAAQ,UAAU,CAAC,GACnBF,EAAO,UAAU,QACjBC,EAAU,UAAU,IACb,OAAA,aAAaE,EAAU,OAAO;AAAA,EAEzC,GAAG,EAAE,GAGCK,IAAclC,EAAM;AAAA,IACxB,CAACmC,MAA0B;AAMzB,OAJEhC,EAAQ,iBAAiB,OACrBA,EAAQ,gBACRmB,EAAgB,kBAGTQ,EAAA,GAEbL,EAAgBU,CAAM;AAAA,IACxB;AAAA,IACA,CAAChC,EAAQ,eAAe2B,GAAYL,CAAe;AAAA,EACrD,GAEMW,IAAiBxC,EAAgB,MAAM;AACrC,UAAA;AAAA,MACJ,oBAAAyC,IAAqBf,EAAgB;AAAA,MACrC,SAAAgB,IAAUhB,EAAgB;AAAA,MAC1B,cAAAN,IAAeM,EAAgB;AAAA,IAAA,IAC7BnB,GAEEoC,IAAUzB,EAAoBC,GAAIsB,GAAqBrB,CAAa,GACpEwB,IAAwC,IAAI,OAAOD,CAAO;AAChE,WAAAC,EAAU,OAAOD,GAEPC,EAAA,YAAY,CAACpC,MAAoB;;AACzC,YAAM,CAAC+B,GAAQ7B,CAAM,IAAIF,EAAE;AAE3B,cAAQ+B,GAAQ;AAAA,QACd,KAAKhB,EAAc;AACT,WAAAc,KAAAF,IAAAH,EAAA,SAAQR,OAAR,QAAAa,EAAA,KAAAF,GAA2BzB,IACnC4B,EAAYf,EAAc,OAAO;AACjC;AAAA,QACF;AACU,WAAAsB,KAAAT,IAAAJ,EAAA,SAAQP,OAAR,QAAAoB,EAAA,KAAAT,GAA0B1B,IAClC4B,EAAYf,EAAc,KAAK;AAC/B;AAAA,MAAA;AAAA,IAEN,GAEUqB,EAAA,UAAU,CAACpC,MAAkB;;AAC7B,OAAA6B,KAAAF,IAAAH,EAAA,SAAQP,OAAR,QAAAY,EAAA,KAAAF,GAA0B3B,IAClC8B,EAAYf,EAAc,KAAK;AAAA,IACjC,GAEImB,MACQT,EAAA,UAAU,OAAO,WAAW,MAAM;AAC/B,MAAAC,EAAA,GACXL,EAAgBN,EAAc,eAAe;AAAA,OAC5CmB,CAAO,IAELE;AAAA,EACN,GAAA,CAACzB,GAAIZ,GAAS2B,CAAU,CAAC,GAGtBY,IAAa1C,EAAM;AAAA,IACvB,IAAI2C,MAA8B;AAChC,YAAM,EAAE,cAAA3B,IAAeM,EAAgB,aAAiB,IAAAnB;AACxD,aAAO,IAAI,QAAuB,CAACyC,GAASC,MAAW;;AACrD,QAAAjB,EAAQ,UAAU;AAAA,UAChB,CAACR,CAAe,GAAGwB;AAAA,UACnB,CAACvB,CAAc,GAAGwB;AAAA,QACpB;AACM,cAAApC,IACJO,MAAiB,SACb2B,EAAW;AAAA,UACT,CAACnC,MACE,iBAAiB,UAAUA,aAAe,eAC1C,iBAAiB,UAAUA,aAAe,eAC1C,iBAAiB,UAAUA,aAAe,eAC1C,qBAAqB,UACpBA,aAAe;AAAA,QAAA,IAErB,CAAC;AAEA,SAAAuB,IAAAL,EAAA,YAAA,QAAAK,EAAS,YAAY,CAAC,CAAC,GAAGY,CAAU,CAAC,GAAGlC,IAE/CgB,EAAgBN,EAAc,OAAO;AAAA,MAAA,CACtC;AAAA,IACH;AAAA,IACA,CAACM,CAAe;AAAA,EAClB,GAEMqB,IAAa9C,EAAM;AAAA,IACvB,IAAI+C,MAA0B;AAC5B,YAAMC,IACJ7C,EAAQ,iBAAiB,OACrBA,EAAQ,gBACRmB,EAAgB;AAEtB,aAAIK,EAAU,WACJ,QAAA;AAAA,QACN;AAAA,MACF,GACO,QAAQ,OAAO,OAEpBqB,KAAa,CAACtB,EAAO,aACvBA,EAAO,UAAUU,EAAe,IAG3BM,EAAW,GAAGK,CAAM;AAAA,IAC7B;AAAA,IACA,CAAC5C,EAAQ,eAAeiC,GAAgBM,CAAU;AAAA,EACpD,GAGMO,IAAuBjD,EAAM,YAAY,MAAM;AACxC,IAAA8B,EAAA,GACXL,EAAgBN,EAAc,MAAM;AAAA,EAAA,GACnC,CAACW,GAAYL,CAAe,CAAC,GAE1ByB,IAAmB;AAAA,IACvB,QAAQ1B;AAAA,IACR,MAAMyB;AAAA,EACR;AAEA,SAAAjD,EAAM,UAAU,MAAM;AACV,IAAA2B,EAAA,UAAUH,MAAiBL,EAAc;AAAA,EAAA,GAClD,CAACK,CAAY,CAAC,GAEXxB,EAAA;AAAA,IACJ,MAAM,MAAM;AACC,MAAA8B,EAAA;AAAA,IACb;AAAA,IACA,CAACA,CAAU;AAAA,EACb,GAEO,CAACgB,GAAYI,CAAgB;AACtC;", "x_google_ignoreList": [0]}