{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAE7F,KAAK,cAAc,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;AAEvD;;GAEG;AACH,MAAM,MAAM,gBAAgB,GAAG;IAC7B,mCAAmC,CAAC,KAAK,EAAE,aAAa,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC;IACtF,iCAAiC,CAAC,KAAK,EAAE,aAAa,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC;CACnF,CAAC;AAEF,MAAM,CAAC,OAAO,OAAO,wBAAyB,SAAQ,YAAY,CAAC,gBAAgB,CAAC;IAClF,QAAQ,CAAC,iBAAiB,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1C,QAAQ,CAAC,cAAc,EAAE,MAAM,GAAG,IAAI,CAAC;IACvC,QAAQ,CAAC,eAAe,EAAE,MAAM,GAAG,IAAI,CAAC;IACxC,QAAQ,CAAC,YAAY,CAAC,EAAE,cAAc,CAAC;IACvC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,cAAc,CAAC;IAC5C,QAAQ,CAAC,kBAAkB,CAAC,EAAE,cAAc,CAAC;IAC7C,QAAQ,CAAC,WAAW,CAAC,EAAE,cAAc,CAAC;IACtC,QAAQ,CAAC,SAAS,CAAC,EAAE,cAAc,CAAC;IACpC,QAAQ,CAAC,SAAS,CAAC,EAAE,cAAc,CAAC;IACpC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,cAAc,CAAC;IAC7C,QAAQ,CAAC,kBAAkB,CAAC,EAAE,cAAc,CAAC;IAC7C,QAAQ,CAAC,aAAa,CAAC,EAAE,cAAc,CAAC;IACxC,QAAQ,CAAC,WAAW,CAAC,EAAE,cAAc,CAAC;IACtC,QAAQ,CAAC,2BAA2B,CAAC,EAAE,cAAc,CAAC;IACtD,QAAQ,CAAC,2BAA2B,CAAC,EAAE,cAAc,CAAC;IACtD,QAAQ,CAAC,kBAAkB,CAAC,EAAE,cAAc,CAAC;IAC7C,QAAQ,CAAC,uBAAuB,CAAC,EAAE,cAAc,CAAC;IAClD,QAAQ,CAAC,yBAAyB,CAAC,EAAE,cAAc,CAAC;IACpD,QAAQ,CAAC,gCAAgC,CAAC,EAAE,cAAc,CAAC;IAC3D,QAAQ,CAAC,qBAAqB,CAAC,EAAE,cAAc,CAAC;IAChD,QAAQ,CAAC,qBAAqB,CAAC,EAAE,cAAc,CAAC;IAChD,QAAQ,CAAC,kBAAkB,CAAC,EAAE,cAAc,CAAC;IAC7C,QAAQ,CAAC,sBAAsB,CAAC,EAAE,cAAc,CAAC;IACjD,QAAQ,CAAC,oBAAoB,CAAC,EAAE,cAAc,CAAC;CAChD"}