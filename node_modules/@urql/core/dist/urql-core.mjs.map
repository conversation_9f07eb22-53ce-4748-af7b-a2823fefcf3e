{"version": 3, "file": "urql-core.mjs", "sources": ["../src/utils/collectTypenames.ts", "../src/utils/formatDocument.ts", "../src/utils/streamUtils.ts", "../src/utils/operation.ts", "../src/utils/index.ts", "../src/gql.ts", "../src/exchanges/cache.ts", "../src/exchanges/ssr.ts", "../src/exchanges/subscription.ts", "../src/exchanges/debug.ts", "../src/exchanges/fetch.ts", "../src/exchanges/compose.ts", "../src/exchanges/map.ts", "../src/exchanges/fallback.ts", "../src/client.ts"], "sourcesContent": null, "names": ["collectTypes", "obj", "types", "Array", "isArray", "i", "l", "length", "key", "add", "formatNode", "node", "definitions", "newDefinition", "push", "directives", "_directives", "directive", "name", "value", "slice", "selections", "hasTypename", "kind", "Kind", "OPERATION_DEFINITION", "selectionSet", "selection", "FIELD", "alias", "newSelection", "NAME", "_generated", "formattedDocs", "Map", "formatDocument", "query", "keyDocument", "result", "get", "__key", "set", "Object", "defineProperty", "enumerable", "with<PERSON><PERSON><PERSON>", "_source$", "source$", "sink", "to<PERSON>romise", "take", "filter", "stale", "hasNext", "then", "onResolve", "onReject", "subscribe", "onResult", "makeOperation", "request", "context", "addMetadata", "operation", "meta", "noop", "gql", "parts", "fragmentNames", "source", "body", "arguments", "unshift", "j", "definition", "FRAGMENT_DEFINITION", "stringifyDocument", "has", "process", "env", "NODE_ENV", "console", "warn", "DOCUMENT", "shouldSkip", "mapTypeNames", "formattedOperation", "cacheExchange", "forward", "client", "dispatchDebug", "resultCache", "operationCache", "isOperationCached", "requestPolicy", "ops$", "cachedOps$", "map", "cachedResult", "type", "message", "makeResult", "data", "cacheOutcome", "reexecuteOperation", "op", "forwardedOps$", "tap", "response", "typenames", "additionalTypenames", "Set", "collectTypenames", "concat", "pendingOperations", "typeName", "operations", "values", "clear", "delete", "merge", "revalidated", "ssrExchange", "params", "staleWhileRevalidate", "includeExtensions", "invalidate<PERSON><PERSON><PERSON>", "invalidate", "Promise", "resolve", "shift", "ssr", "isClient", "suspense", "deserializeResult", "JSON", "parse", "undefined", "extensions", "error", "CombinedError", "networkError", "Error", "graphQLErrors", "serialized", "serializeResult", "stringify", "path", "restoreData", "restore", "extractData", "initialState", "subscriptionExchange", "forwardSubscription", "enableAllOperations", "isSubscriptionOperation", "isSubscriptionOperationFn", "subscriptionResults$", "mergeMap", "teardown$", "takeUntil", "observableish", "makeFetchBody", "make", "observer", "isComplete", "sub", "nextResult", "next", "mergeResultPatch", "errors", "makeErrorResult", "complete", "unsubscribe", "createSubscriptionSource", "forward$", "debugExchange", "debug", "fetchExchange", "fetchResults$", "url", "makeFetchURL", "fetchOptions", "makeFetchOptions", "makeFetchSource", "onPush", "fetchSubscriptions", "composeExchanges", "exchanges", "reduceRight", "exchange", "forwarded", "operations$", "share", "event", "timestamp", "Date", "now", "mapExchange", "onOperation", "onError", "newResult", "fromPromise", "fromValue", "newOperation", "fallbackExchange", "_x", "Client", "opts", "ids", "replays", "active", "dispatched", "queue", "baseOpts", "fetch", "preferGetMethod", "makeSubject", "nextOperation", "isOperationBatchActive", "dispatchOperation", "makeResultSource", "result$", "res", "_instance", "results$", "<PERSON><PERSON><PERSON><PERSON>", "switchMap", "value$", "onEnd", "splice", "onStart", "instance", "this", "create", "prototype", "assign", "queued", "createRequestOperation", "requestOperationType", "getOperationType", "executeRequestOperation", "lazy", "replay", "execute<PERSON>uery", "executeSubscription", "executeMutation", "readQuery", "variables", "createRequest", "subscription", "mutation", "subscribeToDebugTarget", "onEvent", "composedExchange", "publish", "createClient"], "mappings": ";;;;;;;;AAKA,IAAMA,eAAeA,CAACC,GAAgCC;EACpD,IAAIC,MAAMC,QAAQH;IAChB,KAAK,IAAII,IAAI,GAAGC,IAAIL,EAAIM,QAAQF,IAAIC,GAAGD;MACrCL,aAAaC,EAAII,IAAIH;;SAElB,IAAmB,mBAARD,KAA4B,SAARA;IACpC,KAAK,IAAMO,KAAOP;MAChB,IAAY,iBAARO,KAA4C,mBAAbP,EAAIO;QACrCN,EAAMO,IAAIR,EAAIO;;QAEdR,aAAaC,EAAIO,IAAMN;;;;EAK7B,OAAOA;AAAK;;ACTd,IAAMQ,aAGJC;EAEA,IAAI,iBAAiBA,GAAM;IACzB,IAAMC,IAA+C;IACrD,KAAK,IAAIP,IAAI,GAAGC,IAAIK,EAAKC,YAAYL,QAAQF,IAAIC,GAAGD,KAAK;MACvD,IAAMQ,IAAgBH,WAAWC,EAAKC,YAAYP;MAClDO,EAAYE,KAAKD;AACnB;IAEA,OAAO;SAAKF;MAAMC;;AACpB;EAEA,IAAI,gBAAgBD,KAAQA,EAAKI,cAAcJ,EAAKI,WAAWR,QAAQ;IACrE,IAAMQ,IAA8B;IACpC,IAAMC,IAAc,CAAA;IACpB,KAAK,IAAIX,IAAI,GAAGC,IAAIK,EAAKI,WAAWR,QAAQF,IAAIC,GAAGD,KAAK;MACtD,IAAMY,IAAYN,EAAKI,WAAWV;MAClC,IAAIa,IAAOD,EAAUC,KAAKC;MAC1B,IAAgB,QAAZD,EAAK;QACPH,EAAWD,KAAKG;;QAEhBC,IAAOA,EAAKE,MAAM;;MAEpBJ,EAAYE,KAAQD;AACtB;IACAN,IAAO;SAAKA;MAAMI;MAAYC;;AAChC;EAEA,IAAI,kBAAkBL,GAAM;IAC1B,IAAMU,IAA6C;IACnD,IAAIC,IAAcX,EAAKY,SAASC,EAAKC;IACrC,IAAId,EAAKe,cAAc;MACrB,KAAK,IAAIrB,IAAI,GAAGC,IAAIK,EAAKe,aAAaL,WAAWd,QAAQF,IAAIC,GAAGD,KAAK;QACnE,IAAMsB,IAAYhB,EAAKe,aAAaL,WAAWhB;QAC/CiB,IACEA,KACCK,EAAUJ,SAASC,EAAKI,SACE,iBAAzBD,EAAUT,KAAKC,UACdQ,EAAUE;QACf,IAAMC,IAAepB,WAAWiB;QAChCN,EAAWP,KAAKgB;AAClB;MAEA,KAAKR;QACHD,EAAWP,KAAK;UACdS,MAAMC,EAAKI;UACXV,MAAM;YACJK,MAAMC,EAAKO;YACXZ,OAAO;;UAETa,aAAY;;;MAIhB,OAAO;WACFrB;QACHe,cAAc;aAAKf,EAAKe;UAAcL;;;AAE1C;AACF;EAEA,OAAOV;AAAI;;AAGb,IAAMsB,IAAgD,IAAIC;;AA2B7CC,IAAAA,iBACXxB;EAEA,IAAMyB,IAAQC,EAAY1B;EAE1B,IAAI2B,IAASL,EAAcM,IAAIH,EAAMI;EACrC,KAAKF,GAAQ;IACXL,EAAcQ,IACZL,EAAMI,OACLF,IAAS5B,WAAW0B;IAMvBM,OAAOC,eAAeL,GAAQ,SAAS;MACrCnB,OAAOiB,EAAMI;MACbI,aAAY;;AAEhB;EAEA,OAAON;AAAM;;ACrHR,SAASO,YACdC;EAEA,IAAMC,UAAYC,KAChBF,EAASE;EACXD,QAAQE,YAAY,MAKhBA,EADAC,EAAK,EAALA,CADAC,GAAOb,MAAWA,EAAOc,UAAUd,EAAOe,SAA1CF,CADAJ;EAKJA,QAAQO,OAAO,CAACC,GAAWC,MACzBT,QAAQE,YAAYK,KAAKC,GAAWC;EACtCT,QAAQU,YAAYC,KAAYD,EAAUC,EAAVD,CAAoBV;EACpD,OAAOA;AACT;;AC2BA,SAASY,cAAcpC,GAAMqC,GAASC;EACpC,OAAO;OACFD;IACHrC;IACAsC,SAASD,EAAQC,UACb;SACKD,EAAQC;SACRA;QAELA,KAAWD,EAAQC;;AAE3B;;AAOO,IAAMC,cAAcA,CACzBC,GACAC,MAEOL,cAAcI,EAAUxC,MAAMwC,GAAW;EAC9CC,MAAM;OACDD,EAAUF,QAAQG;OAClBA;;;;ACpEF,IAAMC,OAAOA;;ACoDpB,SAASC,IAAIC;EACX,IAAMC,IAAgB,IAAIlC;EAC1B,IAAMtB,IAAgC;EACtC,IAAMyD,IAAyB;EAG/B,IAAIC,IAAenE,MAAMC,QAAQ+D,KAASA,EAAM,KAAKA,KAAS;EAC9D,KAAK,IAAI9D,IAAI,GAAGA,IAAIkE,UAAUhE,QAAQF,KAAK;IACzC,IAAMc,IAAQoD,UAAUlE;IACxB,IAAIc,KAASA,EAAMP;MACjByD,EAAOvD,KAAKK;;MAEZmD,KAAQnD;;IAGVmD,KAAQC,UAAU,GAAGlE;AACvB;EAEAgE,EAAOG,QAAQnC,EAAYiC;EAC3B,KAAK,IAAIjE,IAAI,GAAGA,IAAIgE,EAAO9D,QAAQF;IACjC,KAAK,IAAIoE,IAAI,GAAGA,IAAIJ,EAAOhE,GAAGO,YAAYL,QAAQkE,KAAK;MACrD,IAAMC,IAAaL,EAAOhE,GAAGO,YAAY6D;MACzC,IAAIC,EAAWnD,SAASC,EAAKmD,qBAAqB;QAChD,IAAMzD,IAAOwD,EAAWxD,KAAKC;QAC7B,IAAMA,IAAQyD,EAAkBF;QAEhC,KAAKN,EAAcS,IAAI3D,IAAO;UAC5BkD,EAAc3B,IAAIvB,GAAMC;UACxBP,EAAYE,KAAK4D;AACnB,eAAO,IACoB,iBAAzBI,QAAQC,IAAIC,YACZZ,EAAc7B,IAAIrB,OAAUC;UAG5B8D,QAAQC,KACN,yDACEhE,IADF;;AAMN;QACEN,EAAYE,KAAK4D;;AAErB;;EAGF,OAAOrC,EAAY;IACjBd,MAAMC,EAAK2D;IACXvE;;AAEJ;;AC/FA,IAAMwE,aAAaA,EAAG7D,aACX,eAATA,KAAgC,YAATA;;AAGlB,IAAM8D,eAAgBtB;EAC3B,IAAM3B,IAAQD,eAAe4B,EAAU3B;EACvC,IAAIA,MAAU2B,EAAU3B,OAAO;IAC7B,IAAMkD,IAAqB3B,cAAcI,EAAUxC,MAAMwC;IACzDuB,EAAmBlD,QAAQA;IAC3B,OAAOkD;AACT;IACE,OAAOvB;;AACT;;AAuBK,IAAMwB,gBAA0BA,EAAGC,YAASC,WAAQC;EACzD,IAAMC,IAA2B,IAAIzD;EACrC,IAAM0D,IAAiC,IAAI1D;EAE3C,IAAM2D,oBAAqB9B,KACN,YAAnBA,EAAUxC,QAC0B,mBAApCwC,EAAUF,QAAQiC,kBACmB,iBAApC/B,EAAUF,QAAQiC,iBACjBH,EAAYd,IAAId,EAAUvD;EAE9B,OAAOuF;IACL,IAAMC,IAGJC,GAAIlC;MACF,IAAMmC,IAAeP,EAAYpD,IAAIwB,EAAUvD;MAE/C,iBAAAsE,QAAAC,IAAAC,YAAAU,EAAc;QACZ3B;WACImC,IACA;UACEC,MAAM;UACNC,SAAS;YAEX;UACED,MAAM;UACNC,SAAS;;QACT/B,QAAA;;MAGR,IAAI/B,IACF4D,KACAG,EAAWtC,GAAW;QACpBuC,MAAM;;MAGVhE,IAAS;WACJA;QACHyB,WAAWD,YAAYC,GAAW;UAChCwC,cAAcL,IAAe,QAAQ;;;MAIzC,IAAwC,wBAApCnC,EAAUF,QAAQiC,eAAuC;QAC3DxD,EAAOc,SAAQ;QACfoD,mBAAmBf,GAAQ1B;AAC7B;MAEA,OAAOzB;AAAM,OAlCf2D,CADA9C,GAAOsD,MAAOrB,WAAWqB,MAAOZ,kBAAkBY,IAAlDtD,CADA4C;IAwCF,IAAMW,IAiBJC,GAAIC;MACF,KAAI7C,WAAEA,KAAc6C;MACpB,KAAK7C;QAAW;;MAEhB,IAAI8C,IAAY9C,EAAUF,QAAQiD,uBAAuB;MAMzD,IAAgC,mBAA5BF,EAAS7C,UAAUxC;QACrBsF,INvGuBD,MAA+B,KAC3D5G,aAAa4G,GAAwB,IAAIG,OMsGxBC,CAAiBJ,EAASN,MAAMW,OAAOJ;;MAIrD,IAC8B,eAA5BD,EAAS7C,UAAUxC,QACS,mBAA5BqF,EAAS7C,UAAUxC,MACnB;QACA,IAAM2F,IAAoB,IAAIH;QAE9B,iBAAAjC,QAAAC,IAAAC,YAAAU,EAAc;UACZS,MAAM;UACNC,SAAS,kDAAkDS;UAC3D9C;UACAuC,MAAM;YAAEO;YAAWD;;UAAUvC,QAAA;;QAG/B,KAAK,IAAIhE,IAAI,GAAGA,IAAIwG,EAAUtG,QAAQF,KAAK;UACzC,IAAM8G,IAAWN,EAAUxG;UAC3B,IAAI+G,IAAaxB,EAAerD,IAAI4E;UACpC,KAAKC;YACHxB,EAAenD,IAAI0E,GAAWC,IAAa,IAAIL;;UACjD,KAAK,IAAMvG,KAAO4G,EAAWC;YAAUH,EAAkBzG,IAAID;;UAC7D4G,EAAWE;AACb;QAEA,KAAK,IAAM9G,KAAO0G,EAAkBG;UAClC,IAAI1B,EAAYd,IAAIrE,IAAM;YACxBuD,IAAa4B,EAAYpD,IAAI/B,GAAyBuD;YACtD4B,EAAY4B,OAAO/G;YACnBgG,mBAAmBf,GAAQ1B;AAC7B;;AAEH,aAAM,IAAuB,YAAnBA,EAAUxC,QAAoBqF,EAASN,MAAM;QACtDX,EAAYlD,IAAIsB,EAAUvD,KAAKoG;QAC/B,KAAK,IAAIvG,IAAI,GAAGA,IAAIwG,EAAUtG,QAAQF,KAAK;UACzC,IAAM8G,IAAWN,EAAUxG;UAC3B,IAAI+G,IAAaxB,EAAerD,IAAI4E;UACpC,KAAKC;YACHxB,EAAenD,IAAI0E,GAAWC,IAAa,IAAIL;;UACjDK,EAAW3G,IAAIsD,EAAUvD;AAC3B;AACF;AAAA,OArDFmG,CADAnB,EAHArC,GACEsD,KAAkB,YAAZA,EAAGlF,QAAiD,iBAA7BkF,EAAG5C,QAAQiC,eAD1C3C,CADA8C,GAAIQ,KAAM3C,YAAY2C,GAAI;MAAEF,cAAc;QAA1CN,CAXAuB,EAAM,EAIFvB,EAAIZ,aAAJY,CADA9C,GAAOsD,MAAOrB,WAAWqB,OAAQZ,kBAAkBY,IAAnDtD,CADA4C,KAMA5C,GAAOsD,KAAMrB,WAAWqB,IAAxBtD,CADA4C;IAkEN,OAAOyB,EAAM,EAACxB,GAAYU;AAAe;AAC1C;;AAMI,IAAMF,qBAAqBA,CAACf,GAAgB1B,MAC1C0B,EAAOe,mBACZ7C,cAAcI,EAAUxC,MAAMwC,GAAW;EACvC+B,eAAe;;;ACnBrB,IAAM2B,IAAc,IAAIV;;AAkBjB,IAAMW,cAAcA,CAACC,IAA4B;EACtD,IAAMC,MAAyBD,EAAOC;EACtC,IAAMC,MAAsBF,EAAOE;EACnC,IAAMvB,IAAgD,CAAA;EAItD,IAAMwB,IAA4B;EAClC,IAAMC,aAAczF;IAClBwF,EAAgBhH,KAAKwB,EAAOyB,UAAUvD;IACtC,IAA+B,MAA3BsH,EAAgBvH;MAClByH,QAAQC,UAAU3E,MAAK;QACrB,IAAI9C;QACJ,OAAQA,IAAMsH,EAAgBI;UAC5B5B,EAAK9F,KAAO;;AACd;;AAEJ;EAKF,IAAM2H,MACJA,EAAG1C,WAAQD,gBACXO;IAGE,IAAMqC,IACJT,KAAqC,oBAApBA,EAAOS,aAClBT,EAAOS,YACR3C,EAAO4C;IAEd,IAAI3B,IAUFlB,EADAS,EAAIZ,aAAJY,CAPA9C,GACEY,KACqB,eAAnBA,EAAUxC,SACT+E,EAAKvC,EAAUvD,UACd8F,EAAKvC,EAAUvD,KAAM6C,WACa,mBAApCU,EAAUF,QAAQiC,eALtB3C,CADA4C;IAcF,IAAIC,IAQFC,GAAIQ;MAEF,IAAMP,IAlGUoC,EACxBvE,GACAzB,GACAuF,OACqB;QACrB9D;QACAuC,MAAMhE,EAAOgE,OAAOiC,KAAKC,MAAMlG,EAAOgE,aAAQmC;QAC9CC,YACEb,KAAqBvF,EAAOoG,aACxBH,KAAKC,MAAMlG,EAAOoG,mBAClBD;QACNE,OAAOrG,EAAOqG,QACV,IAAIC,EAAc;UAChBC,cAAcvG,EAAOqG,MAAME,eACvB,IAAIC,MAAMxG,EAAOqG,MAAME,qBACvBJ;UACJM,eAAezG,EAAOqG,MAAMI;kBAE9BN;QACJrF,QAAO;QACPC,WAAWf,EAAOe;SA8EWiF,CACnB7B,GAFiBH,EAAKG,EAAGjG,MAIzBqH;MAGF,IAAID,MAAyBH,EAAY5C,IAAI4B,EAAGjG,MAAM;QACpD0F,EAAa9C,SAAQ;QACrBqE,EAAYhH,IAAIgG,EAAGjG;QACnBgG,mBAAmBf,GAAQgB;AAC7B;MAQA,OANgC;WAC3BP;QACHnC,WAAWD,YAAY2C,GAAI;UACzBF,cAAc;;;AAGL,OApBfN,CANA9C,GACEY,KACqB,eAAnBA,EAAUxC,UACR+E,EAAKvC,EAAUvD,QACmB,mBAApCuD,EAAUF,QAAQiC,eAJtB3C,CADA4C;IA+BF,KAAKqC;MAEH1B,IAEEC,GAAKrE;QACH,KAAMyB,WAAEA,KAAczB;QACtB,IAAuB,eAAnByB,EAAUxC,MAAqB;UACjC,IAAMyH,IAvKIC,EACtB3G,GACAuF;YAEA,IAAMmB,IAA+B;cACnC3F,SAASf,EAAOe;;YAGlB,SAAoBoF,MAAhBnG,EAAOgE;cACT0C,EAAW1C,OAAOiC,KAAKW,UAAU5G,EAAOgE;;YAG1C,IAAIuB,UAA2CY,MAAtBnG,EAAOoG;cAC9BM,EAAWN,aAAaH,KAAKW,UAAU5G,EAAOoG;;YAGhD,IAAIpG,EAAOqG,OAAO;cAChBK,EAAWL,QAAQ;gBACjBI,eAAezG,EAAOqG,MAAMI,cAAc9C,KAAI0C;kBAC5C,KAAKA,EAAMQ,SAASR,EAAMD;oBAAY,OAAOC,EAAMvC;;kBAEnD,OAAO;oBACLA,SAASuC,EAAMvC;oBACf+C,MAAMR,EAAMQ;oBACZT,YAAYC,EAAMD;;AACnB;;cAIL,IAAIpG,EAAOqG,MAAME;gBACfG,EAAWL,MAAME,eAAe,KAAKvG,EAAOqG,MAAME;;AAEtD;YAEA,OAAOG;AAAU,YAqIcC,CAAgB3G,GAAQuF;UAC3CvB,EAAKvC,EAAUvD,OAAOwI;AACxB;AAAA,SALFrC,CADAD;;MAWFV,IAA8BW,EAAIoB,WAAJpB,CAAZX;;IAGpB,OAAOwB,EAAM,EAACd,GAAeV;AAAY;EAG7CmC,IAAIiB,cAAeC;IACjB,KAAK,IAAM7I,KAAO6I;MAEhB,IAAkB,SAAd/C,EAAK9F;QACP8F,EAAK9F,KAAO6I,EAAQ7I;;;AAExB;EAGF2H,IAAImB,cAAc;IAChB,IAAMhH,IAAkB,CAAA;IACxB,KAAK,IAAM9B,KAAO8F;MAAM,IAAiB,QAAbA,EAAK9F;QAAc8B,EAAO9B,KAAO8F,EAAK9F;;;IAClE,OAAO8B;AAAM;EAGf,IAAIqF,KAAUA,EAAO4B;IACnBpB,IAAIiB,YAAYzB,EAAO4B;;EAGzB,OAAOpB;AAAG;;ACrLL,IAAMqB,uBACXA,EACEC,wBACAC,wBACAC,gCAEF,EAAGlE,WAAQD;EA+DT,IAAMoE,IACJD,KACC5F,CAAAA,KACoB,mBAAnBA,EAAUxC,UACPmI,MACmB,YAAnB3F,EAAUxC,QAAuC,eAAnBwC,EAAUxC;EAE/C,OAAOwE;IACL,IAAM8D,IAOJC,GAAS/F;MACP,KAAMvD,KAAEA,KAAQuD;MAChB,IAAMgG,IAEJ5G,GAAOsD,KAAkB,eAAZA,EAAGlF,QAAuBkF,EAAGjG,QAAQA,GAAlD2C,CADA4C;MAIF,OAEEiE,EAAUD,EAAVC,CArFNjG;QAEA,IAAMkG,IAAgBR,EACpBS,EAAcnG,IACdA;QAGF,OAAOoG,GAAsBC;UAC3B,IAAIC,KAAa;UACjB,IAAIC;UACJ,IAAIhI;UAEJ,SAASiI,WAAWpJ;YAClBiJ,EAASI,KACNlI,IAASA,IACNmI,EAAiBnI,GAAQnB,KACzBkF,EAAWtC,GAAW5C;AAE9B;UAEA6G,QAAQC,UAAU3E,MAAK;YACrB,IAAI+G;cAAY;;YAEhBC,IAAML,EAAcxG,UAAU;cAC5B+G,MAAMD;cACN5B,KAAAA,CAAMA;gBACJ,IAAIxI,MAAMC,QAAQuI;kBAKhB4B,WAAW;oBAAEG,QAAQ/B;;;kBAErByB,EAASI,KAAKG,EAAgB5G,GAAW4E;;gBAE3CyB,EAASQ;AACV;cACDA,QAAAA;gBACE,KAAKP,GAAY;kBACfA,KAAa;kBACb,IAAuB,mBAAnBtG,EAAUxC;oBACZkE,EAAOe,mBACL7C,cAAc,YAAYI,GAAWA,EAAUF;;kBAGnD,IAAIvB,KAAUA,EAAOe;oBACnBkH,WAAW;sBAAElH,UAAS;;;kBAExB+G,EAASQ;AACX;AACF;;AACA;UAGJ,OAAO;YACLP,KAAa;YACb,IAAIC;cAAKA,EAAIO;;AAAa;AAC3B;AACD,QA0BIC,CAAyB/G;AAAU,OARvC+F,CALA3G,GACEY,KACqB,eAAnBA,EAAUxC,QACVqI,EAA0B7F,IAH9BZ,CADA4C;IAoBF,IAAMgF,IAOJvF,EALArC,GACEY,KACqB,eAAnBA,EAAUxC,SACTqI,EAA0B7F,IAH/BZ,CADA4C;IASF,OAAOyB,EAAM,EAACqC,GAAsBkB;AAAU;AAC/C;;ACxNE,IAAMC,gBAA0BA,EAAGxF;EACxC,IAA6B,iBAAzBV,QAAQC,IAAIC;IACd,OAAOe,KAAQP,EAAQO;;IAEvB,OAAOA,KAMHY,GAAIrE,KAEF2C,QAAQgG,MAAM,2CAA2C3I,IAF3DqE,CADAnB,EADAmB,GAAIF,KAAMxB,QAAQgG,MAAM,0CAA0CxE,IAAlEE,CAFAZ;;AASN;;ACJK,IAAMmF,gBAA0BA,EAAG1F,YAASE,sBAC1CK;EACL,IAAMoF,IASJrB,GAAS/F;IACP,IAAMO,IAAO4F,EAAcnG;IAC3B,IAAMqH,IAAMC,EAAatH,GAAWO;IACpC,IAAMgH,IAAeC,EAAiBxH,GAAWO;IAEjD,iBAAAQ,QAAAC,IAAAC,YAAAU,EAAc;MACZS,MAAM;MACNC,SAAS;MACTrC;MACAuC,MAAM;QACJ8E;QACAE;;MACDjH,QAAA;;IAGH,IAAMA,IAEJ2F,EAGI7G,GAAOsD,KAAkB,eAAZA,EAAGlF,QAAuBkF,EAAGjG,QAAQuD,EAAUvD,KAA5D2C,CADA4C,GAFJiE,CADAwB,EAAgBzH,GAAWqH,GAAKE;IASlC,IAA6B,iBAAzBxG,QAAQC,IAAIC;MACd,OAEEyG,GAAOnJ;QACL,IAAMqG,KAASrG,EAAOgE,OAAOhE,EAAOqG,aAAQF;QAE5C,iBAAA3D,QAAAC,IAAAC,YAAAU,EAAc;UACZS,MAAMwC,IAAQ,eAAe;UAC7BvC,SAAS,KACPuC,IAAQ,WAAW;UAErB5E;UACAuC,MAAM;YACJ8E;YACAE;YACAnK,OAAOwH,KAASrG;;UACjB+B,QAAA;;AACD,SAdJoH,CADApH;;IAoBJ,OAAOA;AAAM,KA/CfyF,CAPA3G,GAAOY,KAEgB,eAAnBA,EAAUxC,SACU,mBAAnBwC,EAAUxC,UACPwC,EAAUF,QAAQ6H,qBAJ1BvI,CADA4C;EA2DF,IAAMgF,IASJvF,EAPArC,GAAOY,KAEgB,eAAnBA,EAAUxC,QACU,mBAAnBwC,EAAUxC,SACRwC,EAAUF,QAAQ6H,oBAJzBvI,CADA4C;EAWF,OAAOyB,EAAM,EAAC2D,GAAeJ;AAAU;;AChF9BY,IAAAA,mBACVC,KACD,EAAGnG,WAAQD,YAASE,sBAClBkG,EAAUC,aAAY,CAACrG,GAASsG;EAC9B,IAAIC,KAAY;EAChB,OAAOD,EAAS;IACdrG;IACAD,OAAAA,CAAQwG;MACN,IAA6B,iBAAzBlH,QAAQC,IAAIC,UAA2B;QACzC,IAAI+G;UACF,MAAM,IAAIjD,MACR;;QAEJiD,KAAY;AACd;MACA,OAAOE,EAAMzG,EAAQyG,EAAMD;AAC5B;IACDtG,aAAAA,CAAcwG;MACZ,iBAAApH,QAAAC,IAAAC,YAAAU,EAAc;QACZyG,WAAWC,KAAKC;QAChBhI,QAAQyH,EAAS5K;WACdgL;;AAEP;;AACA,IACD1G;;ACqBA,IAAM8G,cAAcA,EACzBC,gBACA7I,aACA8I,gBAEO,EAAGhH,gBACRO,KAaI+D,GAASxH;EACP,IAAIkK,KAAWlK,EAAOqG;IAAO6D,EAAQlK,EAAOqG,OAAOrG,EAAOyB;;EAC1D,IAAM0I,IAAa/I,KAAYA,EAASpB,MAAYA;EACpD,OAAO,UAAUmK,IACbC,EAAYD,KACZE,EAAUF;AAAU,GAL1B3C,CADAtE,EAREsE,GAAS/F;EACP,IAAM6I,IACHL,KAAeA,EAAYxI,MAAeA;EAC7C,OAAO,UAAU6I,IACbF,EAAYE,KACZD,EAAUC;AAAa,GAL7B9C,CADA/D;;ACjEH,IAAM8G,mBAGXA,EAAGnH,sBACHK;EACE,IAA6B,iBAAzBjB,QAAQC,IAAIC;IACde,IAEEY,GAAI5C;MACF,IACqB,eAAnBA,EAAUxC,QACe,iBAAzBuD,QAAQC,IAAIC,UACZ;QACA,IAAMoB,IAAU,+CAA+CrC,EAAUxC;QAEzE,iBAAAuD,QAAAC,IAAAC,YAAAU,EAAc;UACZS,MAAM;UACNC;UACArC;UAASM,QAAA;;QAEXY,QAAQC,KAAKkB;AACf;AAAA,OAbFO,CADAZ;;EAoBJ,OAAO5C,GAAQ2J,MAAoB,GAA5B3J,CAAmC4C;AAAK;;IC4etCgH,IAA8C,SAASA,OAElEC;EAEA,IAA6B,iBAAzBlI,QAAQC,IAAIC,aAA8BgI,EAAK5B;IACjD,MAAM,IAAItC,MAAM;;EAGlB,IAAImE,IAAM;EAEV,IAAMC,IAAU,IAAIhL;EACpB,IAAMiL,IAA+C,IAAIjL;EACzD,IAAMkL,IAAa,IAAIrG;EACvB,IAAMsG,IAAqB;EAE3B,IAAMC,IAAW;IACflC,KAAK4B,EAAK5B;IACVM,oBAAoBsB,EAAKtB;IACzBJ,cAAc0B,EAAK1B;IACnBiC,OAAOP,EAAKO;IACZC,iBAAiBR,EAAKQ;IACtB1H,eAAekH,EAAKlH,iBAAiB;;EAKvC,IAAMsB,IAAaqG;EAEnB,SAASC,cAAc3J;IACrB,IACqB,eAAnBA,EAAUxC,QACS,eAAnBwC,EAAUxC,SACT6L,EAAWvI,IAAId,EAAUvD,MAC1B;MACA,IAAuB,eAAnBuD,EAAUxC;QACZ6L,EAAW7F,OAAOxD,EAAUvD;aACvB,IAAuB,eAAnBuD,EAAUxC;QACnB6L,EAAW3M,IAAIsD,EAAUvD;;MAE3B4G,EAAWoD,KAAKzG;AAClB;AACF;EAIA,IAAI4J,KAAyB;EAC7B,SAASC,kBAAkB7J;IACzB,IAAIA;MAAW2J,cAAc3J;;IAE7B,KAAK4J,GAAwB;MAC3BA,KAAyB;MACzB,OAAOA,MAA2B5J,IAAYsJ,EAAMnF;QAClDwF,cAAc3J;;MAChB4J,KAAyB;AAC3B;AACF;EAGA,IAAME,mBAAoB9J;IACxB,IAAI+J,IAWF9D,EAGI7G,GAAOsD,KAAkB,eAAZA,EAAGlF,QAAuBkF,EAAGjG,QAAQuD,EAAUvD,KAA5D2C,CADAiE,EAAW/C,QAFf2F,CARA7G,GACG4K,KACCA,EAAIhK,UAAUxC,SAASwC,EAAUxC,QACjCwM,EAAIhK,UAAUvD,QAAQuD,EAAUvD,SAC9BuN,EAAIhK,UAAUF,QAAQmK,aACtBD,EAAIhK,UAAUF,QAAQmK,cAAcjK,EAAUF,QAAQmK,YAL5D7K,CAFA8K;IAkBF,IAAuB,YAAnBlK,EAAUxC;MAEZuM,IAEEI,GAAU5L,OAAYA,EAAOe,WAAS,EAAtC6K,CADAJ;;MAIFA,IAGEK,GAAU7L;QACR,IAAM8L,IAASzB,EAAUrK;QACzB,OAAOA,EAAOc,SAASd,EAAOe,UAC1B+K,IACA5G,EAAM,EACJ4G,GAKEnI,GAAI;UACF3D,EAAOc,SAAQ;UACf,OAAOd;AAAM,WAFf2D,CADA/C,EAAK,EAALA,CADAC,GAAOsD,KAAMA,EAAGjG,QAAQuD,EAAUvD,KAAlC2C,CADAiE,EAAW/C;AAQb,SAfR8J,CAFAL;;IAsBJ,IAAuB,eAAnB/J,EAAUxC;MACZuM,IAyBEO,GAAM;QAEJjB,EAAW7F,OAAOxD,EAAUvD;QAC5B0M,EAAQ3F,OAAOxD,EAAUvD;QACzB2M,EAAO5F,OAAOxD,EAAUvD;QAExBmN,KAAyB;QAEzB,KAAK,IAAItN,IAAIgN,EAAM9M,SAAS,GAAGF,KAAK,GAAGA;UACrC,IAAIgN,EAAMhN,GAAGG,QAAQuD,EAAUvD;YAAK6M,EAAMiB,OAAOjO,GAAG;;;QAEtDqN,cACE/J,cAAc,YAAYI,GAAWA,EAAUF;AAChD,SAbHwK,CAtBA5C,GAAOnJ;QACL,IAAIA,EAAOc;UACT,KAAKd,EAAOe;YAEV+J,EAAW7F,OAAOxD,EAAUvD;;YAI5B,KAAK,IAAIH,IAAI,GAAGA,IAAIgN,EAAM9M,QAAQF,KAAK;cACrC,IAAM0D,IAAYsJ,EAAMhN;cACxB,IAAI0D,EAAUvD,QAAQ8B,EAAOyB,UAAUvD,KAAK;gBAC1C4M,EAAW7F,OAAOxD,EAAUvD;gBAC5B;AACF;AACF;;eAEG,KAAK8B,EAAOe;UACjB+J,EAAW7F,OAAOxD,EAAUvD;;QAE9B0M,EAAQzK,IAAIsB,EAAUvD,KAAK8B;AAAO,SAnBpCmJ,CAFAqC;;MAyCFA,IAGES,GAAQ;QACNb,cAAc3J;AAAU,SAD1BwK,CAFAT;;IAQJ,OAAO7B,EAAM6B;AAAQ;EAGvB,IAAMU,IACJC,gBAAgB1B,SAAS0B,OAAO/L,OAAOgM,OAAO3B,OAAO4B;EACvD,IAAMlJ,IAAiB/C,OAAOkM,OAAOJ,GAAU;IAC7CnG,YAAY2E,EAAK3E;IACjB2D,aAAa5E,EAAW/C;IAExBmC,kBAAAA,CAAmBzC;MAGjB,IAAuB,eAAnBA,EAAUxC;QACZqM,kBAAkB7J;aACb,IAAuB,eAAnBA,EAAUxC,MAAqB;QACxC8L,EAAMvM,KAAKiD;QACXiE,QAAQC,UAAU3E,KAAKsK;AACxB,aAAM,IAAIT,EAAOtI,IAAId,EAAUvD,MAAM;QACpC,IAAIqO,KAAS;QACb,KAAK,IAAIxO,IAAI,GAAGA,IAAIgN,EAAM9M,QAAQF;UAChC,IAAIgN,EAAMhN,GAAGG,QAAQuD,EAAUvD,KAAK;YAClC6M,EAAMhN,KAAK0D;YACX8K,KAAS;AACX;;QAGF,MACGA,KACCzB,EAAWvI,IAAId,EAAUvD,QACW,mBAApCuD,EAAUF,QAAQiC,gBACpB;UACAuH,EAAMvM,KAAKiD;UACXiE,QAAQC,UAAU3E,KAAKsK;AACzB,eAAO;UACLR,EAAW7F,OAAOxD,EAAUvD;UAC5BwH,QAAQC,UAAU3E,KAAKsK;AACzB;AACF;AACD;IAEDkB,sBAAAA,CAAuBvN,GAAMqC,GAASoJ;MACpC,KAAKA;QAAMA,IAAO;;MAElB,IAAI+B;MACJ,IAC2B,iBAAzBjK,QAAQC,IAAIC,YACH,eAATzD,MACCwN,IAAuBC,EAAiBpL,EAAQxB,YAAYb;QAE7D,MAAM,IAAIuH,MACR,+BAA+BvH,iBAAoBwN;;MAIvD,OAAOpL,cAAcpC,GAAMqC,GAAS;QAClCoK,WACW,eAATzM,IACM0L,IAAOA,IAAM,IAAK,SACpBxE;WACH6E;WACAN;QACHlH,eAAekH,EAAKlH,iBAAiBwH,EAASxH;QAC9CuC,UAAU2E,EAAK3E,aAA+B,MAAlB2E,EAAK3E,YAAsB5C,EAAO4C;;AAEjE;IAED4G,uBAAAA,CAAwBlL;MACtB,IAAuB,eAAnBA,EAAUxC;QACZ,OAAOsB,YAAYgL,iBAAiB9J;;MAGtC,OAAOlB,YACLqM,GAAsB;QACpB,IAAI7K,IAAS8I,EAAO5K,IAAIwB,EAAUvD;QAClC,KAAK6D;UACH8I,EAAO1K,IAAIsB,EAAUvD,KAAM6D,IAASwJ,iBAAiB9J;;QAGvDM,IAEEkK,GAAQ;UACNX,kBAAkB7J;AAAU,WAD9BwK,CADAlK;QAMF,IAAM8K,IAASjC,EAAQ3K,IAAIwB,EAAUvD;QACrC,IACqB,YAAnBuD,EAAUxC,QACV4N,MACCA,EAAO/L,SAAS+L,EAAO9L;UAExB,OAQE8K,EAAUxB,EAAVwB,CAPA3G,EAAM,EACJnD,GAGElB,GAAOgM,KAAUA,MAAWjC,EAAQ3K,IAAIwB,EAAUvD,MAAlD2C,CADAwJ,EAAUwC;;UAOhB,OAAO9K;;AACT;AAGL;IAED+K,YAAAA,CAAahN,GAAO4K;MAClB,IAAMjJ,IAAY0B,EAAOqJ,uBAAuB,SAAS1M,GAAO4K;MAChE,OAAOvH,EAAOwJ,wBAAwBlL;AACvC;IAEDsL,mBAAAA,CAAoBjN,GAAO4K;MACzB,IAAMjJ,IAAY0B,EAAOqJ,uBACvB,gBACA1M,GACA4K;MAEF,OAAOvH,EAAOwJ,wBAAwBlL;AACvC;IAEDuL,eAAAA,CAAgBlN,GAAO4K;MACrB,IAAMjJ,IAAY0B,EAAOqJ,uBAAuB,YAAY1M,GAAO4K;MACnE,OAAOvH,EAAOwJ,wBAAwBlL;AACvC;IAEDwL,SAAAA,CAAUnN,GAAOoN,GAAW3L;MAC1B,IAAIvB,IAAiC;MAInCmB,GAAUsK;QACRzL,IAASyL;AAAG,SADdtK,CADAgC,EAAOrD,MAAMA,GAAOoN,GAAW3L,IAI/BgH;MAEF,OAAOvI;AACR;IAEDF,OAAKA,CAACA,GAAOoN,GAAW3L,MACf4B,EAAO2J,aAAaK,EAAcrN,GAAOoN,IAAY3L;IAG9D6L,cAAYA,CAACtN,GAAOoN,GAAW3L,MACtB4B,EAAO4J,oBACZI,EAAcrN,GAAOoN,IACrB3L;IAIJ8L,UAAQA,CAACvN,GAAOoN,GAAW3L,MAClB4B,EAAO6J,gBAAgBG,EAAcrN,GAAOoN,IAAY3L;;EAInE,IAAI6B,IAAgDzB;EACpD,IAA6B,iBAAzBa,QAAQC,IAAIC,UAA2B;IACzC,KAAMwF,MAAEA,GAAInG,QAAEA,KAAWoJ;IACzBhI,EAAOmK,yBAA0BC,KAClBpM,EAAUoM,EAAVpM,CAARY;IACPqB,IAAgB8E;AAClB;EAIA,IAAMsF,IAAmBnE,iBAAiBqB,EAAKpB;EAK/C,IAAMqC,IAAWhC,EACf6D,EAAiB;IACfrK;IACAC;IACAF,SAASqH,iBAAiB;MAAEnH;;IAH9BoK,CAIG1I,EAAW/C;EAKD0L,EAAV9B;EAEL,OAAOxI;AACT;;AAMO,IAAMuK,IAAejD;;"}