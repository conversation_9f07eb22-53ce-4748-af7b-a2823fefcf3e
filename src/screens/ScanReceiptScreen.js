import React, { useState } from 'react';
import { View, Text, Button, Image, StyleSheet, FlatList } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { supabase } from '../services/supabase';
import { ocrExtractItemsFromImage } from '../services/ocr';
import ItemShareSelector from '../components/ItemShareSelector';

export default function ScanReceiptScreen({ route, navigation }) {
  const { tripId } = route.params;
  const [image, setImage] = useState(null);
  const [items, setItems] = useState([]);
  const [participants, setParticipants] = useState([]);
  const [itemSharers, setItemSharers] = useState({});

  async function pickImage() {
    const result = await ImagePicker.launchCameraAsync({ quality: 0.7, allowsEditing: false });
    const asset = result?.assets?.[0];
    if (!result.canceled && asset) {
      setImage(asset.uri);
      const extracted = await ocrExtractItemsFromImage(asset.uri);
      setItems(extracted || []);
      // load participants - simple fetch of trip members
      const { data: members } = await supabase.from('trip_members').select('user_id, id').eq('trip_id', tripId);
      const mapped = (members || []).map(m => ({ id: m.user_id, name: m.user_id }));
      setParticipants(mapped);
      // default sharers: all participants
      const defaultSharers = {};
      (extracted || []).forEach((it, idx) => { defaultSharers[idx] = mapped.map(p => p.id); });
      setItemSharers(defaultSharers);
    }
  }

  function toggleSharer(itemIdx, userId) {
    const cur = itemSharers[itemIdx] || [];
    const next = cur.includes(userId) ? cur.filter(x=>x!==userId) : [...cur, userId];
    setItemSharers({ ...itemSharers, [itemIdx]: next });
  }

  async function saveExpenseFromScan() {
    const total = items.reduce((s,it)=>s+(it.amount||0),0);
    const user = (await supabase.auth.getUser()).data.user;
    const { data: expense, error } = await supabase.from('expenses').insert({ trip_id: tripId, payer_id: user.id, description: 'Scanned receipt', total_amount: total }).select().single();
    if (error) return alert(error.message);

    const inserts = items.map((it, idx) => ({ expense_id: expense.id, name: it.name, amount: it.amount, shared: true, share_user_ids: itemSharers[idx] || [] }));
    const { error: e2 } = await supabase.from('expense_items').insert(inserts);
    if (e2) return alert(e2.message);

    navigation.goBack();
  }

  return (
    <View style={{ flex:1, padding:12 }}>
      <Button title="Take Photo" onPress={pickImage} />
      {image && <Image source={{ uri: image }} style={{ width: 300, height: 300, marginTop:12 }} />}
      <Text style={{ marginTop:12, fontWeight:'600' }}>Items</Text>
      <FlatList
        data={items}
        keyExtractor={(it, i) => String(i)}
        renderItem={({ item, index }) => (
          <View style={styles.itemRow}>
            <View style={{ flex:1 }}>
              <Text style={{ fontWeight:'600' }}>{item.name}</Text>
              <Text>{(item.amount||0).toFixed(2)}</Text>
              <ItemShareSelector participants={participants} selectedIds={itemSharers[index]||[]} onToggle={(uid)=>toggleSharer(index, uid)} />
            </View>
          </View>
        )}
      />
      <Button title="Save Scanned Expense" onPress={saveExpenseFromScan} disabled={items.length===0} />
    </View>
  );
}

const styles = StyleSheet.create({ itemRow: { paddingVertical:8 } });
